<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.github</groupId>
    <artifactId>ai-llm4code</artifactId>
    <!-- 用作实际的jar文件后缀版本号 -->
    <version>1.0</version>
    <packaging>jar</packaging>

    <name>llm4code</name>
    <!-- 项目的版本描述 docker 打包的版本号 -->
    <description>*******</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.0.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>

        <druid-spring-boot-starter.version>1.1.20</druid-spring-boot-starter.version>
        <mybatis-spring-boot-starter.version>2.1.0</mybatis-spring-boot-starter.version>
        <pagehelper-spring-boot-starter.version>1.2.12</pagehelper-spring-boot-starter.version>

        <commons-lang.version>2.6</commons-lang.version>
        <commons-io.version>2.6</commons-io.version>
        <commons-codec.version>1.11</commons-codec.version>
        <fastjson.version>1.2.83</fastjson.version>
        <cas.client.version>3.5.1</cas.client.version>
        <tomcat.version>9.0.98</tomcat.version>
        <spring-framework.version>5.2.25.RELEASE</spring-framework.version>
        <docker.repostory>************:5005</docker.repostory>
        <docker.host>http://************:2375</docker.host>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-simple</artifactId>
                <version>1.7.30</version>
                <scope>provided</scope> <!-- 或者直接排除 -->
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons-lang.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons-codec.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid-spring-boot-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis-spring-boot-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${pagehelper-spring-boot-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.12.1</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.6.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-juli</artifactId>
            <version>${tomcat.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.18.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-to-slf4j</artifactId>
            <version>2.18.0</version>
        </dependency>
        <dependency>
            <groupId>com.jthinking.common</groupId>
            <artifactId>ip-info</artifactId>
            <version>2.1.4</version>
        </dependency>


        <!-- uuid -->
        <dependency>
            <groupId>com.fasterxml.uuid</groupId>
            <artifactId>java-uuid-generator</artifactId>
            <version>4.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
            <version>5.1.8.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>io.projectreactor.netty</groupId>
            <artifactId>reactor-netty</artifactId>
            <version>1.0.11</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
            <version>4.1.68.Final</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <!-- 请将 'the-latest-version' 替换为查询到的最新版本号：https://mvnrepository.com/artifact/com.alibaba/dashscope-sdk-java -->
            <version>2.19.2</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.6.1</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-imageenhan</artifactId>
            <version>1.1.12</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>viapi-utils</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.13.0</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.13.6</version>
            <exclusions>
                <exclusion>
                    <groupId>org.redisson</groupId>
                    <artifactId>redisson-spring-data-23</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-data-21</artifactId>
            <version>3.13.6</version>
        </dependency>

        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>kryo</artifactId>
            <version>4.0.0</version>
        </dependency>

    </dependencies>

    <repositories>
        <repository>
            <id>central</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </repository>
        <repository>
            <id>nexus-t</id>
            <url>http://************:8081/repository/maven-releases/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <archive>
                        <manifest>
                            <mainClass>com.github.Application</mainClass>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>./</Class-Path>
                        </manifestEntries>
                    </archive>
                    <includes>
                        <include>**/xml/*.xml</include>
                        <include>**/*.class</include>
                        <include>*.json</include>
                        <include>**/git/info.json</include>
                        <include>views/**</include>
                        <!--						<include>templates/**</include>-->

                        <!--<include>static/**</include>-->
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <!-- not append assembly id in release file name -->
                    <appendAssemblyId>false</appendAssemblyId>
                    <descriptors>
                        <descriptor>./package.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!--配置docker打包插件-->
            <!--			<plugin>-->
            <!--				<groupId>com.spotify</groupId>-->
            <!--				<artifactId>docker-maven-plugin</artifactId>-->
            <!--				<version>1.1.0</version>-->

            <!--				<executions>-->
            <!--					<execution>-->
            <!--						<id>build-image</id>-->
            <!--						<phase>package</phase>-->
            <!--						<goals>-->
            <!--							<goal>build</goal>-->
            <!--						</goals>-->
            <!--					</execution>-->

            <!--					<execution>-->
            <!--						<id>push-image</id>-->
            <!--						<phase>deploy</phase>-->
            <!--						<goals>-->
            <!--							<goal>push</goal>-->
            <!--						</goals>-->
            <!--						<configuration>-->
            <!--							<imageName>mh/${project.name}:${project.description}</imageName>-->
            <!--						</configuration>-->
            <!--					</execution>-->
            <!--				</executions>-->
            <!--				<configuration>-->
            <!--					<serverId>docker-registry</serverId>-->
            <!--					<registryUrl>${docker.repostory}</registryUrl>-->
            <!--					&lt;!&ndash; 配置docker服务器地址 &ndash;&gt;-->
            <!--					<dockerHost>${docker.host}</dockerHost>-->
            <!--					<imageName>${docker.repostory}/mh/${project.name}:${project.description}</imageName> &lt;!&ndash;镜像名称&ndash;&gt;-->
            <!--					&lt;!&ndash; 配置Dockerfile目录 说明：配置dockerDirectory后会忽略掉pom里的baseImage, maintainer, cmd and entryPoint配置, 只保留resources配置 &ndash;&gt;-->
            <!--					<dockerDirectory>${project.basedir}/docker</dockerDirectory>-->
            <!--					<forceTags>true</forceTags>&lt;!&ndash;覆盖已存在的标签 镜像&ndash;&gt;-->
            <!--					<pushImage>true</pushImage>-->
            <!--					<resources>-->
            <!--						<resource>-->
            <!--							<targetPath>/</targetPath>&lt;!&ndash; 将打包后的资源文件复制到该目录，如果定义了dockerDirectory，将会复制到 dockerDirectory目录下 &ndash;&gt;-->
            <!--							<directory>${project.build.directory}/${project.name}-${project.version}</directory>-->
            <!--							<include>${project.build.finalName}.jar</include>-->
            <!--							<include>lib/</include>-->
            <!--							<include>static/</include>-->
            <!--							<include>templates/</include>-->
            <!--							<include>application*</include>-->
            <!--							<include>logback*</include>-->
            <!--							<include>plugs.json</include>-->
            <!--						</resource>-->
            <!--					</resources>-->
            <!--				</configuration>-->
            <!--			</plugin>-->
        </plugins>
    </build>
</project>
