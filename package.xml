<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">

    <id>package</id>

    <formats>
        <format>dir</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <fileSets>
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>${file.separator}</outputDirectory>
            <includes>
                <include>plugs.json</include>
                <include>*.xml</include>
                <include>*.properties</include>
                <include>*.db</include>
                <include>**/*.xls</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>src/main/resources/static</directory>
            <outputDirectory>${file.separator}/static</outputDirectory>
            <includes>
                <include>**</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>src/main/resources/static_dynamic</directory>
            <outputDirectory>${file.separator}/static</outputDirectory>
            <includes>
                <include>assets_dy/*</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>src/main/resources/templates</directory>
            <outputDirectory>${file.separator}/templates</outputDirectory>
            <includes>
                <include>**</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>${file.separator}</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${basedir}/lib/</directory>
            <outputDirectory>lib/</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
    <dependencySets>
        <dependencySet>
            <outputDirectory>lib/</outputDirectory>
            <useProjectArtifact>false</useProjectArtifact>
        </dependencySet>
    </dependencySets>
</assembly>

