<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全国高校教师教学创新大赛智能抽取专家系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: linear-gradient(135deg, #e8d5ff 0%, #b8e6ff 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('../assets/img/1.png');
            background-size: cover;
            background-position: center;
            z-index: -1;
        }

        .container {
            position: relative;
            z-index: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: 10px 20px;
        }

        /* Logo部分 */
        .logo-section {
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            font-weight: bold;
        }

        .logo-tic {
            font-size: 48px;
            margin-right: 10px;
        }

        .logo-t { color: #ff6b35; }
        .logo-i { color: #4ecdc4; }
        .logo-c { color: #45b7d1; }

        .logo-text {
            font-size: 14px;
            color: #666;
            margin-left: 10px;
            line-height: 1.4;
        }

        /* 主标题 */
        .main-title {
            text-align: center;
            font-size: 48px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 80px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 中央抽取部分 */
        .extract-section {
            text-align: center;
            margin-bottom: 80px;
            position: relative;
        }

        .extract-circle {
            position: relative;
            display: inline-block;
            margin-bottom: 60px;
        }

        .orbit {
            width: 300px;
            height: 300px;
            border: 2px solid rgba(74, 144, 226, 0.3);
            border-radius: 50%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .orbit::before {
            content: '';
            position: absolute;
            width: 400px;
            height: 400px;
            border: 1px solid rgba(74, 144, 226, 0.2);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .orbit::after {
            content: '';
            position: absolute;
            width: 500px;
            height: 500px;
            border: 1px solid rgba(74, 144, 226, 0.1);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .extract-btn {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #4a90e2, #357abd);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
            transition: all 0.3s ease;
            z-index: 2;
            position: relative;
        }

        .extract-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(74, 144, 226, 0.6);
        }

        .orbit-dots {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #4a90e2;
            border-radius: 50%;
        }

        .dot-1 {
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .dot-2 {
            bottom: 20px;
            right: 50px;
        }

        .dot-3 {
            bottom: 20px;
            left: 50px;
        }

        /* 规则部分 */
        .rules-section {
            border-radius: 20px;
            padding: 40px;
        }

        .rules-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 30px;
        }

        .rule-item {
            margin-bottom: 20px;
            font-size: 16px;
            line-height: 1.8;
            color: #34495e;
            display: flex;
            align-items: flex-start;
        }

        .rule-number {
            color: #4a90e2;
            font-weight: bold;
            margin-right: 10px;
            min-width: 30px;
        }

        .rule-text {
            flex: 1;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-title {
                font-size: 32px;
            }
            
            .orbit {
                width: 250px;
                height: 250px;
            }
            
            .orbit::before {
                width: 320px;
                height: 320px;
            }
            
            .orbit::after {
                width: 390px;
                height: 390px;
            }
            
            .extract-btn {
                width: 100px;
                height: 100px;
                font-size: 18px;
            }
            
            .rules-section {
                padding: 20px;
            }
        }

        /* 动画效果 */
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .orbit {
            animation: rotate 30s linear infinite;
        }

        .orbit-dots {
            animation: rotate 30s linear infinite reverse;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Logo部分 -->
        <div class="logo-section">
            <div class="logo">
                <div class="logo-tic">
                    <span class="logo-t"><img src="https://p.ananas.chaoxing.com/star3/origin/5bccadd3309af00c6088336a0e14b3db.png"></span>
                </div>
            </div>
        </div>

        <!-- 主标题 -->
        <h1 class="main-title">全国高校教师教学创新大赛智能抽取专家系统</h1>

        <!-- 抽取部分 -->
        <div class="extract-section">
            <div class="extract-circle">
                <div class="orbit">
                    <div class="orbit-dots dot-1"></div>
                    <div class="orbit-dots dot-2"></div>
                    <div class="orbit-dots dot-3"></div>
                    <div class="extract-btn" onclick="chooseExpert()">
                        抽取<br>专家
                    </div>
                </div>
            </div>
        </div>

        <!-- 规则部分 -->
        <div class="rules-section">
            <h2 class="rules-title">抽取规则：</h2>
            <div class="rule-item">
                <span class="rule-number">(1)</span>
                <span class="rule-text">地域分布全覆盖，抽选出来的专家覆盖到32个赛区；</span>
            </div>
            <div class="rule-item">
                <span class="rule-number">(2)</span>
                <span class="rule-text">同校回避；</span>
            </div>
            <div class="rule-item">
                <span class="rule-number">(3)</span>
                <span class="rule-text">各评审组至少1位评审专家与本组作品学科相近；</span>
            </div>
            <div class="rule-item">
                <span class="rule-number">(4)</span>
                <span class="rule-text">各组至少1位评审专家是从事教育教学研究的专家；</span>
            </div>
            <div class="rule-item">
                <span class="rule-number">(5)</span>
                <span class="rule-text">产教融合组有行业企业专家参与评审；</span>
            </div>
            <div class="rule-item">
                <span class="rule-number">(6)</span>
                <span class="rule-text">未参加学会各类赛事讲培训活动的专家，不得作为专家参与本年度相关工作；</span>
            </div>
            <div class="rule-item">
                <span class="rule-number">(7)</span>
                <span class="rule-text">本届参赛教师不得作为本届评委。</span>
            </div>
        </div>
    </div>
<script>
    function chooseExpert() {
        window.location.href = "/page/zhuanjia";
    }
</script>
</body>
</html>