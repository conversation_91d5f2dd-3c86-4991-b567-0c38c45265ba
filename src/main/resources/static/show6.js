const puppeteer = require('puppeteer');
const fs = require('fs');
const axios = require('axios');

//服务启动时 运行Browser实例
async function launchBrowser() {
    try {
        const browser = await puppeteer.launch({
            args: ['--no-sandbox', '--disable-setuid-sandbox', '--enable-accelerated-2d-canvas', '--enable-aggressive-domstorage-flushing'],
            ignoreHTTPSErrors: true,
            headless: true,
            timeout: 60000,
        });
        const wsAddress = browser.wsEndpoint();
        const w_data = Buffer.from(wsAddress);
        fs.writeFile(__dirname + '/wsa.txt', w_data, {flag: 'w+'}, function (err) {
            if (err) {
                console.log(err);
            } else {
                console.log("浏览器启动成功：", wsAddress);
            }
        });
    } catch (e) {
        console.log(e)
    }
}


//获取新页签 ws方式连接Browser
async function newPage() {
    const getWSAddress = () => new Promise(resolve => {
        fs.readFile(__dirname + '/wsa.txt', {flag: 'r+', encoding: 'utf8'}, function (err, data) {
            if (err) {
                console.error(err);
                return;
            }
            resolve(data);
        });
    });
    const wsa = await getWSAddress();
    const browserConfig = {
        browserWSEndpoint: wsa
    };
    const browser = await puppeteer.connect(browserConfig);
    return browser.newPage()
}

//导出PDF
const options = {
    //纸张尺寸
    format: 'A4',
    //打印背景,默认为false
    printBackground: true,
    //不展示页眉
    displayHeaderFooter: true,
    margin: {
        top: '1px',
        bottom: '15px'
    },
}

const getPdfHandle = async (opt) => {
    const browser = await puppeteer.launch({
        headless: true,
        //设置该参数能解决网站证书无效问题（Error: net::ERR_CERT_COMMON_NAME_INVALID ）
        ignoreHTTPSErrors: true,
        // 不使用 websocket
        // pipe: true,
        //在linux运行要加这个参数，不加会报 Error: Failed to launch the browser process!
        args: [
            "--no-zygote",
            "--no-sandbox",
            "--disable-gpu",
            "--no-first-run",
            "--single-process",
            "--ignore-certificate-errors",
            "--disable-extensions",
            "--disable-xss-auditor",
            "--disable-dev-shm-usage",
            "--disable-popup-blocking",
            "--disable-setuid-sandbox",
            "--disable-accelerated-2d-canvas",
            "--enable-features=NetworkService",
        ],
    });
    const page = await browser.newPage();
    // 禁用接口缓存
    await page.setCacheEnabled(false);

    console.log("request-url:"+opt.url)
    // opt.url = 'http://127.0.0.1:8080/dkio9-nsa1/report?orderNo=odai-d05766d033a9d111ef0a22667b50945feb4e'
    await page.goto(opt.url, {
        timeout: 10000,
        ignoreHTTPSErrors: true,
        waitUntil: ["domcontentloaded", "networkidle0", "load"],
    });

    await new Promise((r) => setTimeout(r, 1000));

    page.on('console', msg => {
        console.log('页面控制台消息:', msg.text());
    });

    let option = {
        format: "A4",
        displayHeaderFooter: false,
        printBackground: true,
        scale: 1.36,
        margin: {
            top: '2px',
            bottom: '2px'
        },
        preferCSSPageSize: true,
    };
    // const path = getPdfPath("") + "/" + opt.uid;
    const path = `/opt/upload/pdfs`;
    // const path = `/Users/<USER>/java/wkspace/ai-content-check/files`;
    if (!fs.existsSync(path)) {
        fs.mkdirSync(path, {recursive: true});
    }
    const saveFile = `${path}/${opt.data}.pdf`;
    if (saveFile) {
        option.path = saveFile;
    }
    await page.pdf(option);
    console.log("save pdf file:" + saveFile);
    await page.close();
    browser.close();
};

// 指定日志文件的路径
const logFilePath = 'logs.txt';


async function generalPdf(){
    startGeneral = false
    console.log("staring generalpdf")
    await axios.get('http://aicheck.chaoxing.com/dkio9-nsa1/get-one')
        .then(function (response) {
            console.log('odNo:'+response.data.data)
            if(response.data.code == 1 && response.data.data != null){
                const opt = {};
                opt.data = response.data.data;
                opt.url = "http://aicheck.chaoxing.com/dkio9-nsa1/report?orderNo="+opt.data;
                getPdfHandle(opt);
                // 写入日志到文件
                fs.appendFile(logFilePath, '-- 生成pdf success,文件编号为：'+ opt.data + '\n', (err) => {
                    if (err) {
                        console.error('写入日志文件出错: ', err);
                    } else {
                        console.log('日志已成功写入到文件: ', logFilePath);
                    }
                });
            }
        })
        .catch(function (error) {
            console.log(error);
        });

    // new Promise((r) => setTimeout(function () {
    //     generalPdf()
    // }, 10000));
}

/**
 * 系统执行入口
 */
setInterval(generalPdf,2000)
// generalPdf()
