* {
    margin: 0;
    padding: 0;
    -webkit-touch-callout: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

fieldset, img {
    border: 0 !important
}

address, caption, cite, code, dfn, em, strong, th, var {
    font-style: normal
}

li {
    list-style: none
}

caption, th {
    text-align: left
}

h1, h2, h3, h4, h5, h6 {
    font-size: 100%
}

q:after, q:before {
    content: ''
}

abbr, acronym {
    border: 0;
    font-variant: normal
}

sup {
    vertical-align: text-top
}

sub {
    vertical-align: text-bottom
}

input, select, textarea {
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    -webkit-user-select: auto
}

button, input, select {
    outline: 0;
    vertical-align: middle
}

input {
    outline: 0;
    -webkit-appearance: none
}

input::placeholder {
    color: #ACB4BF;
}

input:focus {
    border-color: #3a8bff !important;
}

input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

::-webkit-scrollbar {
    width: .25rem;
}

::-webkit-scrollbar-thumb {
    background-color: #DADFE5;
    border-radius: .25rem;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #ACB4BF;
    cursor: pointer;
}

legend {
    color: #000
}

article, aside, dialog, figure, footer, header, hgroup, menu, nav, section {
    display: block
}

body, html {
    width: 100%;
    text-align: center;
    font-family: "SF Pro Display", "Helvetica Neue", Helvetica, Tahoma, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    -webkit-font-smoothing: antialiased
}

a, a:hover, a:link, a:visited {
    text-decoration: none;
}

button[disabled] {
    cursor: default !important;
}

.my-msg-box {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #f1f1f1; /* 浅灰色背景 */
    color: #ff6b6b; /* 更醒目的文字颜色 */
    padding: 30px; /* 增加内边距 */
    border-radius: 8px; /* 边框圆角更大 */
    box-shadow: 0 4px 12px rgba(0, 3, 51, 0.16); /* 更大的阴影 */
    z-index: 1000;
    font-size: 18px; /* 更大的字体 */
    font-family: Arial, sans-serif;
    font-weight: bold; /* 字体加粗 */
    text-align: center;
    max-width: 90%; /* 限制最大宽度，防止过宽 */
    width: fit-content; /* 使宽度适应内容 */
    min-width: 300px; /* 最小宽度 */
    transition: opacity 0.3s ease-in-out;
}