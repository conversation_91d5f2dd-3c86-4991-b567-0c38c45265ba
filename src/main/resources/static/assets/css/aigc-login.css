body {
    width: 100vw;
    overflow-x: hidden;
    background: #e5ecf3 url(../img/login_bg2.png) no-repeat center center fixed;
    background-size: auto 100%;
    background-position: right bottom;
}

img.login_logo {
    width: 7.5rem;
    height: 1.25rem;
    position: absolute;
    top: 2rem;
    left: 5rem;
    z-index: 3;
}

img.login_title {
    width: 16.75rem;
    height: 3.3125rem;
    position: absolute;
    top: 5.9375rem;
    left: 5rem;
    z-index: 3;
}

img.login_bg {
    width: 39.1563rem;
    height: 30.0625rem;
    position: absolute;
    top: 3.5rem;
    left: 1rem;
    z-index: 2;
}

.login_box {
    width: 15.25rem;
    height: 17.1875rem;
    position: absolute;
    top: 8.2813rem;
    right: 5.3125rem;
    z-index: 3;
    border-radius: 10px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 10px 0px rgba(237, 238, 240, 0.5);
}

.login_box .login_phone {
    width: 100%;
    height: 100%;
    padding: 2rem;
    box-sizing: border-box;
    position: relative;
}

.login_box .login_phone .error_tips {
    display: none;
    font-size: .375rem;
    color: #FF5152;
    position: absolute;
    top: 8.3rem;
    left: 2rem;
    width: 100%;
    text-align: left;
}

.login_box .login_phone .login_box_title {
    width: 100%;
    height: 1.5625rem;
    line-height: 1rem;
    font-size: .75rem;
    font-weight: 500;
    color: #131B26;
    text-align: left;
}

.login_box .login_phone input {
    width: 100%;
    height: 1.5rem;
    border: 1px solid #D4D6D9;
    border-radius: 4px;
    margin-top: .75rem;
    padding: .4375rem 0 .4375rem 1.5625rem;
    box-sizing: border-box;
    font-size: .4375rem;
    color: #000;
}

.login_box .login_phone .login_box_input_text input {
    padding-right: .2rem;
    background: #fff url(../img/login_input1.png) no-repeat .75rem center;
    background-size: .5625rem .5625rem;
}

.login_box .login_phone .login_box_input_password input {
    padding-right: 4.2rem;
    background: #fff url(../img/login_input2.png) no-repeat .75rem center;
    background-size: .5625rem .5625rem;
}

.login_box .login_phone .login_box_input_password {
    position: relative;
}

.login_box .login_phone .login_box_input_password button {
    position: absolute;
    top: 1.2188rem;
    right: 3.4375rem;
    z-index: 2;
    width: .5625rem;
    height: .5625rem;
    border: none;
    background: url(../img/login_input3.png) no-repeat center center;
    background-size: .5625rem .5625rem;
    cursor: pointer;
}

.login_box .login_phone .login_box_input_password button.active {
    background: url(../img/login_input4.png) no-repeat center center;
    background-size: .5625rem .5625rem;
}

.login_box .login_phone .login_box_input_password .login_box_forget {
    font-size: .4375rem;
    font-weight: 400;
    color: #91A0B5;
    text-align: right;
    position: absolute;
    top: 1.2188rem;
    right: .75rem;
    z-index: 2;
}

.login_box .login_phone .login_box_btn {
    width: 100%;
    height: 1.5rem;
    line-height: 1;
    margin-top: 1.25rem;
    background: #297CF2;
    border-radius: 4px;
    font-size: .5rem;
    color: #fff;
    border: 0;
    text-align: center;
    cursor: pointer;
}

.login_box .login_phone .login_box_btn:hover {
    background: #4D97FF;
}

.login_box .login_phone .login_box_btn:active {
    background: #0E5DCE;
}

.login_box .login_phone .login_box_bottom {
    width: 100%;
    height: .75rem;
    line-height: 1;
    font-size: .4375rem;
    font-weight: 400;
    margin-top: .75rem;
}

.login_box .login_phone .login_box_bottom a {
    color: #297CF2;
}

.login_box .login_phone .login_box_bottom a:hover {
    color: #4D97FF;
}

.login_box .login_phone .login_box_bottom a:active {
    color: #0E5DCE;
}

.login_box .login_phone .login_box_bottom a:nth-of-type(1) {
    float: left;
    text-align: left;
}

.login_box .login_phone .login_box_bottom a:nth-of-type(2) {
    float: right;
    text-align: right;
}

.login_box .login_phone .login_box_bottom_more_box {
    width: 100%;
    height: 1.5625rem;
    line-height: 1;
    font-size: .4375rem;
    font-weight: 400;
    color: #A2ABB8;
    text-align: left;
    margin-top: 2.1875rem;
}

.login_box .login_phone .login_box_bottom_more_box a {
    margin-left: .625rem;
    color: #8A8B99;
}

.login_box .login_phone .login_box_bottom_more_box a:hover {
    color: #474C59;
}

.login_box .login_phone .login_box_bottom_more_box a:active {
    color: #131B26;
}

.login_box .login_phone .login_box_bottom_more_box a img {
    vertical-align: sub;
    margin-right: .1875rem;
    width: .625rem;
    height: .625rem;
}

.login_box .login_phone .login_tab,
.login_box .login_qr .login_tab {
    position: absolute;
    top: .125rem;
    right: .125rem;
    z-index: 2;
    width: 2.1875rem;
    height: 2.1875rem;
    cursor: pointer;
}

.login_box .login_phone .login_tab {
    background: url(../img/login_switch1.png) no-repeat center center;
    background-size: 2.1875rem 2.1875rem;
}

.login_box .login_phone .login_tab:hover {
    background: url(../img/login_switch3.png) no-repeat center center;
    background-size: 2.1875rem 2.1875rem;
}

.login_box .login_qr .login_tab {
    background: url(../img/login_switch2.png) no-repeat center center;
    background-size: 2.1875rem 2.1875rem;
}

.login_box .login_qr .login_tab:hover {
    background: url(../img/login_switch4.png) no-repeat center center;
    background-size: 2.1875rem 2.1875rem;
}

.login_box .login_qr {
    width: 100%;
    height: 100%;
    padding: 3.0313rem 4.0313rem 4.0313rem;
    box-sizing: border-box;
    display: none;
}

.login_box .login_qr .login_box_title {
    width: 100%;
    height: 1.4375rem;
    line-height: 1rem;
    font-size: .75rem;
    font-weight: 500;
    color: #131B26;
}

.login_box .login_qr > p {
    font-size: .4375rem;
    color: #474C59;
}

.login_box .login_qr .login_qr_code {
    margin-top: .875rem;
    width: 7.1875rem;
    height: 7.1875rem;
    border: 1px solid #F2F2F2;
    border-radius: 12px;
    position: relative;
}

.login_box .login_qr .login_qr_code img {
    width: 5.6875rem;
    height: 5.6875rem;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 2;
    transform: translate(-50%, -50%);
}