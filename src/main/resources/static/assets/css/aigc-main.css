body {
    width: 100vw;
    height: 100vh;
    overflow-x: hidden;
    background-image: linear-gradient(#f5f8ff, #f2f6fe);
}

.pretty-checkbox input[type="checkbox"] {
    display: none;
}

.pretty-checkbox .checkmark {
    display: inline-block;
    width: .4375rem;
    height: .4375rem;
    border: 1px solid #C3CCD9;
    background: #fff;
    border-radius: .125rem;
    position: relative;
    vertical-align: middle;
    margin: 0 .25rem;
    cursor: pointer;
}

.pretty-checkbox input[type="checkbox"]:checked+.checkmark {
    background: #297CF2;
}

.pretty-checkbox .checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: .125rem;
    top: .0313rem;
    width: .125rem;
    height: .2188rem;
    border: solid #fff;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.pretty-checkbox input[type="checkbox"]:checked+.checkmark:after {
    display: block;
}

.pretty-radio .radio_box {
    display: inline-block;
    margin: 0 .5625rem 0 .1875rem;
}

.pretty-radio input[type=radio] {
    display: none;
}

.pretty-radio .radiomark {
    width: .3125rem;
    height: .3125rem;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 0 0 .125rem #ffffff, 0 0 0 .1563rem #297CF2;
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    transition: border-color ease 0.2s;
    -moz-transition: border-color ease 0.2s;
    -o-transition: border-color ease 0.2s;
    -webkit-transition: border-color ease 0.2s;
    cursor: pointer;
}

.pretty-radio input[type=radio]:checked+.radiomark,
.radiomark.radio {
    background: #297CF2;
    border-color: #297CF2;
}

.pretty-radio .radio_box>span {
    margin-left: .3125rem;
    cursor: pointer;
}


.document_top {
    width: 100%;
    height: 2.25rem;
    background: #fff;
    padding: .5rem 4.375rem;
    box-sizing: border-box;
}

.document_top>img {
    float: left;
    width: 6.5313rem;
    height: 1.25rem;
}

.document_top .user_login {
    float: right;
    position: relative;
}

.document_top .user_login .user_btn {
    line-height: 1.25rem;
    font-size: .4375rem;
    color: #131B26;
    background: url(../img/document_icon2.png) no-repeat right center;
    background-size: .375rem .375rem;
    padding-right: .625rem;
    cursor: pointer;
}

.document_top .user_login .user_btn>img {
    display: inline-block;
    margin-left: .3125rem;
    width: .375rem;
    height: .375rem;
}

.document_top .user_login .quit_btn {
    display: none;
    width: 3.25rem;
    height: 1.25rem;
    line-height: 1.25rem;
    font-size: .4375rem;
    color: #474C59;
    text-align: center;
    border-radius: .1875rem;
    position: absolute;
    bottom: -1rem;
    right: 0;
    z-index: 2;
    box-shadow: 0px 2px 12px 0px #00255933;
    background: #fff;
}

.path_list {
    width: 100%;
    height: 2.6875rem;
    line-height: 2.6875rem;
}

.path_list .path_box,
.path_list>hr {
    display: inline-block;
}

.path_list .path_box i {
    display: inline-block;
    width: .875rem;
    height: .875rem;
    line-height: .875rem;
    font-style: normal;
    font-size: .4375rem;
    font-weight: 500;
    border-width: 1.5px;
    border-style: solid;
    border-radius: 50%;
    vertical-align: middle;
}

.path_list .path_box.path1 i {
    text-indent: -99999px;
    border-color: #297CF2;
    background: url(../img/document_icon5.png) no-repeat center center;
    background-size: .375rem .375rem;
}

.path_list .path_box.path2 i {
    border-color: #297CF2;
    background: #297CF2;
    color: #fff;
}

.path_list .path_box.path3 i {
    border-color: #ACB4BF;
    background: transparent;
    color: #ACB4BF;
}

.path_list .path_box span {
    display: inline-block;
    font-size: .5rem;
    vertical-align: middle;
    cursor: pointer;
}

.path_list .path_box.path1 span {
    color: #474C59;
}

.path_list .path_box.path2 span {
    font-weight: 500;
    color: #474C59;
}

.path_list .path_box.path3 span {
    color: #8A8B99;
}

.path_list>hr {
    width: 3rem;
    height: 0;
    border: 0;
    border-bottom: 1.5px solid #C4C7CC;
    vertical-align: middle;
    margin: 0 .25rem;
}

.document_content {
    display: flex;
}

.content_left,
.content_right {
    flex: 1;
    min-height: 18.5625rem;
    border-radius: .3125rem;
    background: #fff;
    box-shadow: 0px 2px 10px 0px #EDEEF080;
}

.content_left {
    flex-basis: 38.625rem;
    margin: 0 .875rem 0 4.375rem;
}

.content_left .form_top {
    width: 100%;
    height: 2.6875rem;
    padding: .875rem .875rem .5625rem .875rem;
    box-sizing: border-box;
    text-align: left;
}

.content_left .form_top>label {
    display: inline-block;
    font-size: .5rem;
    font-weight: 500;
    color: #474C59;
    vertical-align: middle;
}

.content_left .form_top .form_search {
    display: inline-block;
    margin-left: .375rem;
    position: relative;
}

.content_left .form_top .form_search input {
    width: 8.125rem;
    height: 1.25rem;
    border: 1px solid #D4D6D9;
    border-radius: 4px;
    padding: .25rem 1.25rem .25rem .4375rem;
    box-sizing: border-box;
    font-size: .4375rem;
    color: #000;
}

.content_left .form_top .form_search .form_search_btn {
    display: block;
    width: .5625rem;
    height: .5625rem;
    border: 0;
    position: absolute;
    right: .3125rem;
    top: 50%;
    z-index: 2;
    transform: translateY(-50%);
    background: url(../img/document_icon1.png) no-repeat center center;
    background-size: .5625rem .5625rem;
    cursor: pointer;
}

.content_left .form_top .form_search_a {
    float: right;
    display: block;
    width: 4.5625rem;
    height: 1.25rem;
    line-height: 1.25rem;
    background: #297CF2 url(../img/document_icon4.png) no-repeat .625rem center;
    background-size: .5625rem .5625rem;
    border-radius: 4px;
    font-size: .4375rem;
    font-weight: 500;
    text-indent: .875rem;
    color: #fff;
    border: 0;
    text-align: center;
    cursor: pointer;
}

.content_left .form_top .form_search_a:hover {
    background: #4D97FF url(../img/document_icon4.png) no-repeat .625rem center;
    background-size: .5625rem .5625rem;
}

.content_left .form_top .form_search_a:active {
    background: #0E5DCE url(../img/document_icon4.png) no-repeat .625rem center;
    background-size: .5625rem .5625rem;
}

.content_left .form_table {
    width: 100%;
    position: relative;
}

.content_left .form_table>p {
    display: block;
    width: auto;
    font-size: .4375rem;
    color: #ACB4BF;
    background: url(../img/document_icon3.png) no-repeat left center;
    background-size: .5625rem .5625rem;
    padding-left: .75rem;
    position: absolute;
    right: .875rem;
    top: .3125rem;
    z-index: 2;
}

.content_left .form_table .form_tab {
    display: block;
    width: 100%;
    height: 1.3125rem;
    line-height: 1.3125rem;
    font-size: .5rem;
    text-align: left;
    border-bottom: 1px solid #F2F2F2;
    padding: 0 .5rem;
    box-sizing: border-box;
}

.content_left .form_table .form_tab li {
    display: inline-block;
    height: calc(1.3125rem - 3px);
    margin: 0 .375rem;
    color: #ACB4BF;
    cursor: pointer;
}

.content_left .form_table .form_tab li.active {
    font-weight: 500;
    color: #297CF2;
    border-bottom: 2px solid #297CF2;
}

.content_left .form_table .form_tab li:hover {
    color: #297CF2;
}

.content_left .form_table .form_tab li.active:hover {
    color: #297CF2;
}

.content_left .form_table .form_table_box {
    width: 100%;
    padding: .875rem;
    box-sizing: border-box;
}

.content_left .form_table .form_table_box .form_table_box_item {
    width: 100%;
    min-height: 12.5rem;
    background-size: 6.875rem 6.3438rem;
}

.content_left .form_table .form_table_box .empty {
    background: #fff url(/assets/img/document_null.png) no-repeat center center;
}

.content_left .form_table .form_table_box .form_table_box_item table thead,
.content_left .detection_box .detection_table table thead {
    display: block;
    width: 100%;
}

.content_left .form_table .form_table_box .form_table_box_item table,
.content_left .detection_box .detection_table table {
    display: block;
    width: 100%;
    border-collapse: collapse;
    border: 0;
    font-size: .4375rem;
    color: #131B26;
    text-align: left;
}

.content_left .detection_box .detection_table table tbody {
    display: block;
    height: calc(100vh - 18rem);
    min-height: 10rem;
}

.content_left .form_table .form_table_box .form_table_box_item table tbody {
    display: block;
    min-height: 12.5rem;
}

.content_left .form_table .form_table_box .form_table_box_item table tr,
.content_left .detection_box .detection_table table tr {
    border: 0;
    height: 1.25rem;
    line-height: 1.25rem;
    display: table;
    width: 100%;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td,
.content_left .form_table .form_table_box .form_table_box_item table tr th,
.content_left .detection_box .detection_table table tr td,
.content_left .detection_box .detection_table table tr th {
    display: inline-block;
    border: 0;
    padding: 0 .5rem;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td:nth-of-type(1),
.content_left .form_table .form_table_box .form_table_box_item table tr th:nth-of-type(1) {
    width: 48px;
    text-align: center;
    padding: 0;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td:nth-of-type(2),
.content_left .form_table .form_table_box .form_table_box_item table tr th:nth-of-type(2) {
    width: calc(20% - 48px);
}

.content_left .form_table .form_table_box .form_table_box_item table tr td:nth-of-type(3),
.content_left .form_table .form_table_box .form_table_box_item table tr th:nth-of-type(3) {
    width: 10%;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td:nth-of-type(4),
.content_left .form_table .form_table_box .form_table_box_item table tr th:nth-of-type(4) {
    width: 15%;
}


.content_left .form_table .form_table_box .form_table_box_item table tr td:nth-of-type(5),
.content_left .form_table .form_table_box .form_table_box_item table tr th:nth-of-type(5) {
    width: 15%;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td:nth-of-type(6),
.content_left .form_table .form_table_box .form_table_box_item table tr th:nth-of-type(6) {
    width: 10%;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td:nth-of-type(7),
.content_left .form_table .form_table_box .form_table_box_item table tr th:nth-of-type(7) {
    width: 10%
}

.content_left .form_table .form_table_box .form_table_box_item table tr td:nth-of-type(8),
.content_left .form_table .form_table_box .form_table_box_item table tr th:nth-of-type(8) {
    width: 10%;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td:nth-of-type(9),
.content_left .form_table .form_table_box .form_table_box_item table tr th:nth-of-type(9) {
    width: 10%;
}

.content_left .detection_box .detection_table table tr td:nth-of-type(1),
.content_left .detection_box .detection_table table tr th:nth-of-type(1) {
    width: 25%;
}

.content_left .detection_box .detection_table table tr td:nth-of-type(2),
.content_left .detection_box .detection_table table tr th:nth-of-type(2) {
    width: 10%;
}

.content_left .detection_box .detection_table table tr td:nth-of-type(3),
.content_left .detection_box .detection_table table tr th:nth-of-type(3) {
    width: 20%;
}

.content_left .detection_box .detection_table table tr td:nth-of-type(4),
.content_left .detection_box .detection_table table tr th:nth-of-type(4) {
    width: 15%;
}

.content_left .detection_box .detection_table table tr td:nth-of-type(5),
.content_left .detection_box .detection_table table tr th:nth-of-type(5) {
    width: 15%;
}

.content_left .detection_box .detection_table table tr td:nth-of-type(6),
.content_left .detection_box .detection_table table tr th:nth-of-type(6) {
    width: 15%;
}

.content_left .form_table .form_table_box .form_table_box_item table tr th,
.content_left .detection_box .detection_table table tr th {
    height: 1.625rem;
    line-height: 1.625rem;
    background: #F2F4F7;
    position: sticky;
    top: 0;
    z-index: 10;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td {
    height: 2.0625rem;
    line-height: 2.0625rem;
    background: #fff;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td .form_table_btn {
    color: #297CF2;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td .form_table_btn:hover {
    color: #0E5DCE;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td>span {
    width: auto;
    padding-left: .5625rem;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td>span.state1 {
    background: url(../img/document_state1.png) no-repeat left center;
    background-size: .3125rem .3125rem;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td>span.state2 {
    background: url(../img/document_state2.png) no-repeat left center;
    background-size: .3125rem .3125rem;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td>span.state3 {
    background: url(../img/document_state3.png) no-repeat left center;
    background-size: .3125rem .3125rem;
}

.content_left .form_table .form_table_box .form_table_box_item table tr td>span.state4 {
    background: url(../img/document_state4.png) no-repeat left center;
    background-size: .3125rem .3125rem;
}

.content_left .upload_box {
    width: 100%;
    padding: .875rem;
    box-sizing: border-box;
    text-align: left;
}

.content_left .upload_box .form_item {
    width: 100%;
    font-size: .4375rem;
    position: relative;
}

.content_left .upload_box .form_item>label.item_name {
    display: block;
    width: 100%;
    height: 1rem;
    line-height: 1rem;
    font-weight: 500;
    color: #131B26;
}

.content_left .upload_box .form_item>label.item_name i {
    display: inline-block;
    font-size: .5rem;
    font-weight: 700;
    vertical-align: middle;
    font-style: normal;
    color: #F36161;
    margin-right: .125rem;
}

.content_left .upload_box .form_item>input {
    width: 100%;
    height: 1.125rem;
    border: 1px solid #D4D6D9;
    border-radius: 4px;
    padding: .25rem 1.875rem .25rem .4375rem;
    box-sizing: border-box;
    font-size: .4375rem;
    color: #000;
    margin: .125rem 0 .375rem 0;
}

.content_left .upload_box .form_item>p.charCount {
    position: absolute;
    right: .4375rem;
    bottom: .6875rem;
    z-index: 2;
    color: #ACB4BF;
}

.content_left .upload_box .form_item .pretty-radio {
    margin-top: .1875rem;
}

.content_left .upload_box .upload_item {
    width: 100%;
    height: 10rem;
    border: 1px dashed #ACB4BF;
    border-radius: .125rem;
    margin: .375rem 0;
}

.content_left .upload_box .text_area {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: .5rem .1875rem .5rem .5rem;
}

.content_left .upload_box .text_area textarea {
    width: 100%;
    height: 100%;
    line-height: .6875rem;
    border: 0;
    user-select: none;
    resize: none;
    outline: none;
    color: #131B26;
}

.content_left .upload_box .upload_item .drop_area {
    display: none;
    width: 100%;
    height: 100%;
    text-align: center;
}

.content_left .upload_box .upload_item .drop_area>img {
    width: 1.75rem;
    height: 1.75rem;
    margin-top: 2.8125rem;
}

.content_left .upload_box .upload_item .drop_area>div {
    font-size: .4375rem;
    color: #474C59;
}

.content_left .upload_box .upload_item .drop_area>div a {
    color: #297CF2;
}

.content_left .upload_box .upload_item .drop_area>p {
    font-size: .375rem;
    color: #ACB4BF;
    margin-top: .1875rem;
}

.content_left .upload_box .upload_item .drop_area input[type=file] {
    display: none;
}

.content_left .upload_box .form_item .pretty-checkbox {
    display: inline-block;
}

.content_left .upload_box .form_item .pretty-checkbox .checkmark {
    margin: 0 .3125rem 0 0;
}

.content_left .upload_box .form_item .pretty-checkbox input[type="checkbox"]:checked+.checkmark {
    border: 1px solid #297CF2;
}

.content_left .upload_box .form_item .upload_hint {
    display: inline-block;
    vertical-align: middle;
    color: #474C59;
}

.content_left .upload_box .form_item .upload_hint a {
    color: #297CF2;
}

.content_left .upload_box .form_item .btn_submit {
    display: block;
    width: 3.6875rem;
    height: 1.25rem;
    line-height: 1.25rem;
    background: #297CF2 url(../img/document_icon8.png) no-repeat .625rem center;
    background-size: .5625rem .5625rem;
    border-radius: 4px;
    font-size: .4375rem;
    font-weight: 500;
    text-indent: .875rem;
    color: #fff;
    border: 0;
    margin-top: .625rem;
    text-align: center;
    cursor: pointer;
}

.content_left .upload_box .form_item .btn_submit:hover {
    background: #4D97FF url(../img/document_icon8.png) no-repeat .625rem center;
    background-size: .5625rem .5625rem;
}

.content_left .upload_box .form_item .btn_submit:active {
    background: #0E5DCE url(../img/document_icon8.png) no-repeat .625rem center;
    background-size: .5625rem .5625rem;
}

.content_left .detection_box {
    width: 100%;
    padding: .875rem;
    box-sizing: border-box;
    text-align: left;
}

.content_left .detection_top {
    width: 100%;
}

.content_left .detection_top>div img:nth-of-type(1) {
    width: 1.125rem;
    height: 1.125rem;
    vertical-align: middle;
}

.content_left .detection_top>div img:nth-of-type(2) {
    width: 5.75rem;
    height: .7813rem;
    vertical-align: middle;
}

@keyframes rotate {
    0% {
        transform: rotateZ(0deg);
    }

    100% {
        transform: rotateZ(360deg);
    }
}

.content_left .detection_top>div.undone img:nth-of-type(1) {
    animation: rotate 2s linear infinite;
}

.content_left .detection_table>h1 {
    font-size: .5rem;
    font-weight: 500;
    color: #131B26;
    margin: 1rem 0 .5rem 0;
}

.content_left .detection_item a.btn_check,
.content_left .detection_item a.btn_back {
    display: inline-block;
    border-radius: 4px;
    font-size: .4375rem;
    font-weight: 500;
    text-indent: .875rem;
    margin: .875rem .1875rem .4375rem 0;
    text-align: center;
    cursor: pointer;
}

.content_left .detection_item a.btn_check {
    width: 4.5625rem;
    height: 1.25rem;
    line-height: 1.25rem;
    background: #297CF2 url(../img/document_icon9.png) no-repeat .625rem center;
    background-size: .5625rem .5625rem;
    color: #fff;
}

.content_left .detection_item a.btn_back {
    width: 3.6875rem;
    height: 1.1875rem;
    line-height: 1.1875rem;
    background: #fff url(../img/document_icon6.png) no-repeat .625rem center;
    color: #297CF2;
    background-size: .5625rem .5625rem;
    border: solid 1px #297CF2;
}

.content_left .detection_item a.btn_check:hover {
    background: #4D97FF url(../img/document_icon9.png) no-repeat .625rem center;
    background-size: .5625rem .5625rem;
}

.content_left .detection_item a.btn_check:active {
    background: #0E5DCE url(../img/document_icon9.png) no-repeat .625rem center;
    background-size: .5625rem .5625rem;
}

.content_left .detection_item>p {
    font-size: .4375rem;
    color: #131B26;
}

.content_left .detection_item>p s {
    text-decoration: none;
    color: #F33131;
}

.content_right {
    flex-basis: 11.75rem;
    margin: 0 4.375rem 0 0;
}

.content_right .content_right_top {
    width: 100%;
    height: 2.5313rem;
    background: url(../img/document_img1.png) no-repeat center center;
    background-size: 100% 100%;
}

.content_right .content_right_bottom {
    margin: 1.5rem 0;
}

.content_right .content_right_bottom>img {
    width: 11.75rem;
    height: 14.5rem;
}

.bg_pop {
    display: none;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    position: fixed;
    filter: Alpha(opacity=50);
    opacity: 0.5;
    background: #000;
    z-index: 99998;
}

.box_pop {
    display: none;
    position: fixed;
    width: 20rem;
    height: 12.5rem;
    border-radius: .3125rem;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    z-index: 99998;
}

.box_pop .box_pop_top {
    position: relative;
}

.box_pop .box_pop_top h1 {
    display: block;
    width: 100%;
    padding: .5rem .9375rem;
    box-sizing: border-box;
    font-size: .5625rem;
    font-weight: 500;
    color: #131B26;
    text-align: left;
}

.box_pop .box_pop_top .btn_close {
    position: absolute;
    right: .625rem;
    top: .625rem;
    z-index: 2;
    text-indent: -99999px;
    width: .5625rem;
    height: .5625rem;
    background: url(../img/document_icon7.png) no-repeat center center;
    background-size: .5625rem .5625rem;
    cursor: pointer;
}

.box_pop .box_pop_bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    padding: .5313rem 0;
}

.box_pop .box_pop_bottom .btn_cancel {
    display: block;
    width: 2.875rem;
    height: 1.125rem;
    line-height: 1.125rem;
    background: #297CF2;
    font-size: .4375rem;
    font-weight: 500;
    color: #fff;
    text-align: center;
    border-radius: .125rem;
    margin: 0 auto;
    cursor: pointer;
}

.box_pop .box_pop_bottom .btn_cancel:hover {
    background: #4D97FF;
}

.box_pop .box_pop_bottom .btn_cancel:active {
    background: #0E5DCE;
}

.box_pop .box_pop_content {
    width: 100%;
    padding: .75rem .9375rem;
    box-sizing: border-box;
    text-align: left;
}

.box_pop .box_pop_content p {
    line-height: 1rem;
    font-size: .5rem;
    color: #000;
}

.pagination {
    width: 100%;
    padding: .375rem 0;
    box-sizing: border-box;
    margin: 0 auto;
    color: #262626;
}

.pagination .total,
.pagination>button,
.pagination .jump,
.pagination .pager,
.pagination .pager li {
    display: inline-block;
    vertical-align: middle;
    font-size: 0;
}

.pagination .total {
    margin-right: .625rem;
}

.pagination .total_box,
.pagination>button,
.pagination .jump,
.pagination .pager li,
.pagination .jump .jump_input input[type="number"] {
    font-size: .375rem;
}

.pagination .total span {
    margin: 0 .125rem;
}

.pagination .pager,
.pagination .btn_next,
.pagination .btn_jump {
    margin-left: -0.3125rem;
}

.pagination .btn_prev {
    border-radius: .125rem 0 0 .125rem;
}

.pagination .btn_next {
    border-radius: 0 .125rem .125rem 0;
}

.pagination .pager li {
    height: .9375rem;
    line-height: .9375rem;
    border: 1px solid #D4D6D9;
    padding: 0 .5rem;
    background: #fff;
    margin-left: -1px;
    cursor: pointer;
}

.pagination .pager .active {
    background: #297CF2;
    color: #fff;
    border-color: #297CF2;
}

.pagination .pager .more {
    cursor: default;
}

.pagination>button {
    height: 1rem;
    line-height: 1rem;
    border: 1px solid #D4D6D9;
    padding: 0 .5rem;
    background: #fff;
    cursor: pointer;
}

.pagination>button:hover,
.pagination .number:hover {
    background: #F0F6FF;
}

.pagination>button[disabled]:hover {
    background: #fff;
}

.pagination .active:hover {
    background: #297CF2;
}

.pagination .jump {
    height: .9375rem;
    line-height: .9375rem;
    margin-left: .625rem;
    padding: 0 .25rem;
    border: 1px solid #D4D6D9;
    border-radius: .125rem 0 0 .125rem;
    color: #ACB4BF;
}

.pagination .jump .jump_input input[type="number"] {
    width: .75rem;
    border: 0;
    height: .875rem;
    line-height: .9375rem;
    text-align: center;
    color: #131B26;
}

.pagination .btn_jump {
    border-radius: 0 .125rem .125rem 0;
}