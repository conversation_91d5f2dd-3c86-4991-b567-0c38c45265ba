body {
    font-family: '<PERSON><PERSON>', serif;
    background: #E7EBF4;
}

.report_main {
    width: 35rem;
    min-height: 50rem;
    margin: 1rem auto;
    padding: 1.75rem 0 1.25rem 0;
    box-sizing: border-box;
    background: #f6f7f9 url(../img/report_bg.png) no-repeat center top;
    background-size: 100% auto;
    box-shadow: 0px 2px 10px 0px #EDEEF080;
}

.report_top {
    width: calc(100% - 1.25rem);
    ;
    height: 12.3438rem;
    margin: 0 .625rem;
    background: url(../img/report_img.png) no-repeat center bottom;
    background-size: 100% auto;
    position: relative;
}

.report_top>img {
    width: 8.9688rem;
    height: 1.75rem;
    position: absolute;
    top: 0;
    left: .625rem;
    z-index: 2;
}

.report_top .report_num {
    position: absolute;
    right: .625rem;
    top: .1875rem;
    z-index: 2;
    font-size: .4375rem;
    font-weight: 700;
    color: #474C59;
    text-align: left;
}

.report_top>h1 {
    line-height: 13.9063rem;
    font-size: 2rem;
    font-weight: 700;
    color: #131B26;
}

.report_box {
    width: 100%;
    padding: 0 1.25rem;
    box-sizing: border-box;
    margin-top: -0.4375rem;
    position: relative;
    z-index: 2;
}

.report_box>div {
    width: 100%;
    min-height: 2rem;
    border-radius: .625rem;
    margin-bottom: .75rem;
    padding: .5rem 1.25rem;
    box-sizing: border-box;
    background: #fff;
    box-shadow: 0px 2px 10px 0px #EDEEF080;
    text-align: left;
}

.report_box .report_info {
    border-radius: .125rem .125rem .625rem .625rem;
}

.report_box .report_info li {
    line-height: 1.6563rem;
    font-size: .5625rem;
}

.report_box .report_info li label {
    color: #474C59;
}

.report_box .report_info li span {
    font-weight: 700;
    color: #131B26;
}

.report_box .result_title {
    width: 100%;
}

.report_box .result_title>i {
    display: inline-block;
    width: .1875rem;
    height: .75rem;
    margin-right: .0625rem;
    border-radius: .1875rem;
    background: #297CF2;
    vertical-align: middle;
}

.report_box .result_title>span {
    font-size: .75rem;
    font-weight: 700;
    color: #131B26;
    vertical-align: middle;
}

.ring_wrap {
    position: relative;
    width: 7.5rem;
    height: 7.5rem;
    border-radius: 50%;
    background: #D9D9D9;
    text-align: center;
    margin: .5rem auto;
}

.ring_right-part {
    width: 7.5rem;
    height: 7.5rem;
    position: absolute;
    clip: rect(0px, auto, auto, 3.125rem)
}

.ring_right {
    width: 7.5rem;
    height: 7.5rem;
    position: absolute;
    border-radius: 50%;
    clip: rect(0px, auto, auto, 3.125rem);
}

.ring_r-shadow {
    width: 6.25rem;
    height: 6.25rem;
    border-radius: 50%;
    background: #fff;
    position: absolute;
    left: .625rem;
    top: .625rem;
}

.ring_left-part {
    width: 7.5rem;
    height: 7.5rem;
    position: absolute;
    clip: rect(0px, 3.125rem, auto, 0px)
}

.ring_left {
    width: 7.5rem;
    height: 7.5rem;
    position: absolute;
    border-radius: 50%;
    clip: rect(0px, 3.125rem, auto, 0px);
}

.ring_l-shadow {
    width: 6.25rem;
    height: 6.25rem;
    border-radius: 50%;
    background: #fff;
    position: absolute;
    left: .625rem;
    top: .625rem;
}

.ring_desc {
    display: inline-block;
    width: 7.5rem;
    height: 7.5rem;
    line-height: 7.5rem;
    font-size: .5625rem;
    position: absolute;
    left: 0;
}

.result_desc {
    display: inline-flex;
    width: 100%;
}

.result_item {
    display: inline-block;
    width: 50%;
    text-align: center;
}

.result_item>label {
    display: block;
    font-size: .5625rem;
    color: #474C59;
    margin: .5rem auto;
}

.result_item>h2 {
    font-size: 1.75rem;
}

.original_content {
    display: inline-flex;
    width: 100%;
    font-size: .5625rem;
    line-height: 1.125rem;
    margin: .5rem 0;
    background: #F7F8FA;
}

.parent {
    display: grid;
    grid-template-columns: 80% 20%;
    /* 将父元素分为两列，第一列占据剩余空间，第二列宽度为内容宽度 */
    grid-column-gap: .125rem;
    /* 设置两列之间的间距，可以根据需要调整 */
    width: 100%;
    /* 确保父元素占据100%宽度 */
}

/*.original_content .original_left {*/
/*    display: inline-block;*/
/*    width: 24.4375rem;*/
/*}*/

.original_content .original_left.content {
    display: inline-block;
    width: 100%;
    padding-right: 1.0rem;
    padding-top: 12px;
    box-sizing: border-box;
    color: #131B26;
    background: #fff;
}

.original_content .original_left.content.suspected {
    color: #F33131;
}

.original_content .original_right.aigc_value {
    display: grid;
    place-items: center;
    width: 4.875rem;
    margin: .375rem;
    background: #fff;
    color: #F33131;
    text-align: center;
    border-left: .125rem solid #F33131;
}

.original_content .original_right.aigc_value>div {
    align-items: center;
}

.report_notice .notice_left {
    display: inline-block;
    width: 20.9375rem;
    margin-top: .625rem;
    font-size: .5625rem;
}

.report_notice .notice_left>label {
    color: #474C59;
}

.report_notice .notice_left ul {
    margin-top: .125rem;
}

.report_notice .notice_left li {
    line-height: 1.125rem;
    font-weight: 700;
    color: #131B26;
}

.report_notice .notice_right {
    display: inline-block;
    width: 8rem;
    margin-top: .625rem;
    font-size: .5625rem;
}

.report_notice .notice_right li {
    line-height: 1.5625rem;
    color: #474C59;
}

.report_notice .notice_right li span {
    font-weight: 700;
    color: #131B26;
}