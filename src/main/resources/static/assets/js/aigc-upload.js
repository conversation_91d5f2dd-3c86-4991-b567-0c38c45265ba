// 输入框字符数限制
const inputTitle = document.getElementById('inputTitle');
const charCountTitle = document.getElementById('charCountTitle');

inputTitle.addEventListener('input', function () {
    const value = inputTitle.value;
    const length = value.length;
    charCountTitle.textContent = `${length}/300`;

    inputTitle.value = value.slice(0, Math.min(length, 300));
    charCountTitle.textContent = `${length}/300`;

    if (length >= 300) {
        // alert('输入的字符数已达到300个，无法再输入更多字符。');
    }
});

const inputAuthor = document.getElementById('inputAuthor');
const charCountAuthor = document.getElementById('charCountAuthor');

inputAuthor.addEventListener('input', function () {
    const value = inputAuthor.value;
    const length = value.length;
    charCountAuthor.textContent = `${length}/200`;

    inputAuthor.value = value.slice(0, Math.min(length, 200));
    charCountAuthor.textContent = `${length}/200`;

    if (length >= 200) {
        // alert('输入的字符数已达到200个，无法再输入更多字符。');
    }
});

const inputOrganize = document.getElementById('inputOrganize');
const charCountOrganize = document.getElementById('charCountOrganize');

inputOrganize.addEventListener('input', function () {
    const value = inputOrganize.value;
    const length = value.length;
    charCountOrganize.textContent = `${length}/20`;

    inputOrganize.value = value.slice(0, Math.min(length, 20));
    charCountOrganize.textContent = `${length}/20`;

    if (length >= 20) {
        // alert('输入的字符数已达到20个，无法再输入更多字符。');
    }
});

// 单选框切换
document.querySelectorAll('.radio_box span').forEach(span => {
    span.addEventListener('click', function (event) {
        const input = event.target.previousElementSibling;
        input.click();
    });
});

document.getElementById('radio1').addEventListener('change', function () {
    document.getElementById('textArea').style.display = 'block';
    document.getElementById('dropArea').style.display = 'none';
});

document.getElementById('radio2').addEventListener('change', function () {
    document.getElementById('textArea').style.display = 'none';
    document.getElementById('dropArea').style.display = 'block';
});


// 上传文件
function drop(event) {
    event.preventDefault();

    const file = event.dataTransfer.files[0];

    if (file) {
        const fileName = file.name;
        const fileSize = file.size;
        const fileType = file.type;

        if (isValidFile(fileName, fileSize, fileType)) {
            // 上传文件
            uploadFile(file);
            $("#fileOriginName").val(fileName)
        } else {
            showMsg('文件格式或大小不符合要求');
        }
    }
}

function isValidFile(fileName, fileSize, fileType) {
    //'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    const validFileTypes = ['text/plain'];
    const maxFileSize = 50 * 1024 * 1024;

    return validFileTypes.includes(fileType) && fileSize <= maxFileSize;
}

function uploadFile(file) {
    const formData = new FormData();
    formData.append('uploadFile', file);
    $("#fileOriginName").val(file.name);
    fetch('/usts', {
        method: 'POST',
        body: formData
    })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                // 处理错误
                throw new Error('服务器返回的响应不是一个有效的JSON格式');
            }
        })
        .then(data => {
            if (data.code == 1) {
                showMsg('文件上传成功');
                $("#uuidName").val(data.data);
            } else {
                showMsg('文件上传失败');
            }
        })
        .catch(error => {
            console.error('上传文件时发生错误:', error);
        });
}

document.getElementById('uploadFile').addEventListener('click', function () {
    document.getElementById('uploadFileInput').click();
});

function fileChange(input) {
    const file = input.files[0];
    if (file) {
        // 上传文件
        uploadFile(file);
    }
}

// 弹窗
function pupopen() {
    document.querySelector('.bg_pop').style.display = 'block';
    document.querySelector('.box_pop').style.display = 'block';
}

function pupclose() {
    document.querySelector('.bg_pop').style.display = 'none';
    document.querySelector('.box_pop').style.display = 'none';
}