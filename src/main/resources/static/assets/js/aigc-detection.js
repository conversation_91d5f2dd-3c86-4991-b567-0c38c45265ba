// 检测结果对应颜色
function progressBar(percentage) {
    var p = Math.round(percentage * 100);
    var deg = p * 3.6;
    var right = document.getElementById("rightRing"),
        left = document.getElementById("leftRing"),
        desc = document.getElementById("descRing");
    if (p > 100 || p < 0) p = 100;
    if (desc.innerText) {
        desc.innerText = p + "%";
    }
    if (desc.textContent) {
        desc.textContent = p + "%";
    }
    if (p >= 0 && p < 20) {
        desc.style.color = "#52CC5C";
        left.style.cssText = "background: #D9D9D9;";
        right.style.cssText = "background:#52CC5C;transform:rotate(" + (deg - 180) + "deg);";
    } else if (p >= 20 && p < 40) {
        desc.style.color = "#A0DC36";
        left.style.cssText = "background: #D9D9D9;";
        right.style.cssText = "background:#A0DC36;transform:rotate(" + (deg - 180) + "deg);";
    } else if (p >= 40 && p < 50) {
        desc.style.color = "#FC9208";
        left.style.cssText = "background: #D9D9D9;";
        right.style.cssText = "background:#FC9208;transform:rotate(" + (deg - 180) + "deg);";
    } else if (p >= 50 && p < 60) {
        desc.style.color = "#FC9208";
        left.style.cssText = "background: #FC9208;transform:rotate(" + (deg - 360) + "deg);";
        right.style.cssText = "background:#FC9208;";
    } else if (p >= 60 && p < 80) {
        desc.style.color = "#FF5502";
        left.style.cssText = "background:#FF5502;transform:rotate(" + (deg - 360) + "deg);";
        right.style.cssText = "background:#FF5502;";
    } else if (p >= 80 && p <= 100) {
        desc.style.color = "#F33131";
        left.style.cssText = "background:#F33131;transform:rotate(" + (deg - 360) + "deg);";
        right.style.cssText = "background:#F33131;";
    }
}