// 表格Tab切换
// const lis = document.querySelectorAll('.form_tab li');

// function updateTable(data) {
//     // 根据获取到的数据更新表格内容
//     // ...
// }


// 表格多选框
function selectAll() {
    const selectAllCheckbox = document.querySelector('input[name="selectAll"]');
    const personCheckboxes = document.querySelectorAll('input[name="person"]');

    personCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    updateSelectAll();
}addEventListener

function updateSelectAll() {
    const selectAllCheckbox = document.querySelector('input[name="selectAll"]');
    const personCheckboxes = document.querySelectorAll('input[name="person"]');
    const allChecked = Array.from(personCheckboxes).every(checkbox => checkbox.checked);
    selectAllCheckbox.checked = allChecked;
}

function deselectAll() {
    const selectAllCheckbox = document.querySelector('input[name="selectAll"]');
    const personCheckboxes = document.querySelectorAll('input[name="person"]');
    personCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectAll();
}

// 翻页
// document.addEventListener('DOMContentLoaded', function () {
//     const numbers = document.querySelectorAll('.number');
//     const btnPrev = document.querySelector('.btn_prev');
//     const btnNext = document.querySelector('.btn_next');
//
//     const isFirstActive = numbers[0].classList.contains('active');
//     const isLastActive = numbers[numbers.length - 1].classList.contains('active');
//
//     if (isFirstActive) {
//         btnPrev.disabled = true;
//     } else {
//         btnPrev.disabled = false;
//     }
//
//     if (isLastActive) {
//         btnNext.disabled = true;
//     } else {
//         btnNext.disabled = false;
//     }
//
//     numbers.forEach((number, index) => {
//
//         number.addEventListener('click', function () {
//             numbers.forEach(otherNumber => {
//                 if (otherNumber !== number) {
//                     otherNumber.classList.remove('active');
//                 }
//             });
//
//             number.classList.add('active');
//
//             const newIsFirstActive = number === numbers[0];
//             const newIsLastActive = index === numbers.length - 1;
//
//             if (newIsFirstActive) {
//                 btnPrev.disabled = true;
//             } else {
//                 btnPrev.disabled = false;
//             };
//             if (newIsLastActive) {
//                 btnNext.disabled = true;
//             } else {
//                 btnNext.disabled = false;
//             }
//         });
//     });
// });