// 登录退出弹窗
document.getElementById('userBtn').addEventListener('click', function () {
    document.querySelector('.quit_btn').style.display = 'block';
});

document.addEventListener('click', function (event) {
    if (!event.target.matches('.quit_btn') && !event.target.matches('#userBtn')) {
        document.querySelector('.quit_btn').style.display = 'none';
    }
});


function showMsg(message) {
    const msgBox = document.getElementById('msgBox');
    msgBox.textContent = message;
    msgBox.style.display = 'block';
    msgBox.style.opacity = 1;

    setTimeout(() => {
        msgBox.style.opacity = 0;
        setTimeout(() => {
            msgBox.style.display = 'none';
            if(message.indexOf('登录后使用') != -1){
                window.location.href='/login';
            }
        }, 200);
    }, 2000);
}