// 通用的验证函数
function validate() {
    const phoneNumber = document.getElementById('phoneNumber').value;
    const password = passwordInput.value;
    // 匹配手机号码
    const regexPhone = /^1[3-9]\d{9}$/;
    // 验证密码是否至少6位
    const regexPassword = /^[\w\d]{6,}$/;

    const errorTips = document.getElementById('errorTips');

    if (!regexPhone.test(phoneNumber)) {
        errorTips.textContent = '手机号码输入错误，请重新输入！';
        errorTips.style.display = 'block';
        return;
    }

    if (password.trim() === '') {
        errorTips.textContent = '请输入密码！';
        errorTips.style.display = 'block';
        return;
    }

    if (!regexPassword.test(password)) {
        errorTips.textContent = '密码输入错误，请重新输入！';
        errorTips.style.display = 'block';
        return;
    }

    errorTips.style.display = 'none';
}


// $().ready(function () {
//     jquery.min.js
// })
