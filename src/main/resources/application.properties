# \u6570\u636E\u5E93\u914D\u7F6E
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.url=jdbc:mysql://************:30000/dasai?serverTimezone=Asia/Shanghai&allowMultiQueries=true
spring.datasource.username=yanfa
spring.datasource.password=3tf4wFUfvlvfhxNeRD
spring.datasource.druid.initial-size=2
spring.datasource.druid.minIdle=1
spring.datasource.druid.maxActive=10
spring.datasource.druid.maxWait=60000
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
spring.datasource.druid.minEvictableIdleTimeMillis=300000
spring.datasource.druid.validationQuery=SELECT 'x'
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
spring.datasource.druid.poolPreparedStatements=true
spring.datasource.druid.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.druid.async-init=true
# \u5E94\u7528\u914D\u7F6E
spring.application.name=@project.artifactId@
server.port=9006
server.servlet.context-path=/
server.servlet.session.tracking-modes=cookie
spring.resources.chain.strategy.content.enabled=true
# \u6A21\u677F\u5F15\u64CE
spring.thymeleaf.cache=false
spring.thymeleaf.mode=HTML
# tomcat
server.tomcat.uri-encoding=UTF-8
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true
spring.messages.encoding=UTF-8
server.tomcat.threads.max=800
server.tomcat.accept-count=1200
server.tomcat.threads.min-spare=20
server.tomcat.max-http-form-post-size=10MB
spring.main.allow-bean-definition-overriding=true
server.tomcat.connection-timeout=30000
# mybatis
mybatis.mapper-locations=classpath*:/com/**/mapper/xml/*Mapper.xml
## redis
spring.redis.host=************
spring.redis.port=30004
spring.redis.database=15
spring.redis.password=Uk0L0ldxmU1aJCSzykR
# redis lettuce\u5BA2\u6237\u7AEF
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.pool.max-idle=4
spring.redis.lettuce.pool.max-wait=-1ms
spring.redis.lettuce.pool.max-active=6
spring.redis.lettuce.shutdown-timeout=100ms