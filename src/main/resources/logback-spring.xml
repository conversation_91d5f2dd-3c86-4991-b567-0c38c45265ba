<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="FALSE" scan="TRUE" scanPeriod="6000">

    <springProperty scope="context" name="appName" source="spring.application.name"/>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <property name="LOG_HOME" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}}"/>

    <!-- 最大保存历史日志天数 -->
    <property name="LOG_MAX_HISTORY" value="15"/>
    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="FILE_LOG_PATTERN" value="${FILE_LOG_PATTERN:-%d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>


    <logger name="sun" level="INFO"/>
    <logger name="java" level="INFO"/>
    <logger name="javax" level="INFO"/>
    <logger name="io.netty" level="INFO"/>
    <logger name="io.lettuce" level="INFO"/>
    <logger name="com.netflix.discovery.DiscoveryClient" level="INFO"/>
    <logger name="com.sun" level="INFO"/>
    <logger name="org.apache.tomcat" level="INFO"/>
    <logger name="org.apache.coyote" level="INFO"/>
    <logger name="org.apache.catalina" level="INFO"/>
    <logger name="org.apache.http" level="INFO"/>
    <logger name="org.aspectj" level="INFO" additivity="false"/>
    <logger name="freemarker.cache" level="INFO" additivity="false"/>
    <logger name="reactor.ipc.netty.channel.ChannelOperations" level="DEBUG" additivity="false"/>
    <logger name="reactor.ipc.netty.http.client.HttpClient" level="INFO" additivity="false"/>
    <logger name="reactor.ipc.netty.http.server.HttpServer" level="INFO" additivity="false"/>
    <logger name="org.thymeleaf.standard.expression" level="DEBUG" additivity="false"/>
    <logger name="org.thymeleaf.spring4.expression.SPELVariableExpressionEvaluator" level="DEBUG" additivity="false"/>
    <logger name="org.thymeleaf.spring5.expression.SPELVariableExpressionEvaluator" level="DEBUG" additivity="false"/>
    <logger name="org.thymeleaf.TemplateEngine.CONFIG" level="INFO" additivity="false"/>
    <logger name="org.thymeleaf.TemplateEngine.TIMER" level="INFO" additivity="false"/>
    <logger name="org.springframework.boot.autoconfigure" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.boot.autoconfigure.logging.AutoConfigurationReportLoggingInitializer" level="INFO" additivity="false"/>
    <logger name="org.springframework.boot.bind" level="INFO" additivity="false"/>
    <logger name="org.springframework.boot.context" level="INFO" additivity="false"/>
    <logger name="org.springframework.boot.web.filter" level="INFO" additivity="false"/>

    <logger name="org.mybatis.spring.SqlSessionFactoryBean" level="INFO" additivity="false"/>
    <logger name="org.mybatis.spring.SqlSessionUtils" level="INFO" additivity="false"/>
    <logger name="org.mybatis.spring.mapper.ClassPathMapperScanner" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator" level="INFO" additivity="false"/>
    <logger name="org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper" level="INFO" additivity="false"/>
    <logger name="org.springframework.aop.framework.autoproxy.BeanNameAutoProxyCreator" level="INFO" additivity="false"/>
    <logger name="org.springframework.aop.framework.autoproxy.InfrastructureAdvisorAutoProxyCreator" level="INFO" additivity="false"/>
    <logger name="org.springframework.aop.framework.Cglib2AopProxy" level="INFO" additivity="false"/>
    <logger name="org.springframework.aop.framework.CglibAopProxy" level="INFO" additivity="false"/>
    <logger name="org.springframework.aop.framework.JdkDynamicAopProxy" level="INFO" additivity="false"/>
    <logger name="org.springframework.beans.AbstractNestablePropertyAccessor" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping" level="INFO" additivity="false"/>
    <logger name="org.springframework.boot.actuate.endpoint.web.servlet.ControllerEndpointHandlerMapping" level="INFO" additivity="false"/>
    <logger name="org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener" level="INFO" additivity="false"/>
    <logger name="org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter" level="INFO" additivity="false"/>
    <logger name="org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.beans.CachedIntrospectionResults" level="INFO" additivity="false"/>
    <logger name="org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.beans.factory.annotation.InjectionMetadata" level="INFO" additivity="false"/>
    <logger name="org.springframework.beans.factory.support.DefaultListableBeanFactory" level="INFO" additivity="false"/>
    <logger name="org.springframework.beans.factory.xml" level="INFO" additivity="false"/>
    <logger name="org.springframework.beans.factory.xml.PluggableSchemaResolver" level="INFO" additivity="false"/>
    <logger name="org.springframework.beans.TypeConverterDelegate" level="INFO" additivity="false"/>
    <logger name="org.springframework.cache.interceptor.CacheInterceptor" level="ERROR" additivity="false"/>
    <logger name="org.springframework.cache.annotation.AnnotationCacheOperationSource" level="INFO" additivity="false"/>
    <logger name="org.springframework.context.annotation.ConfigurationClassEnhancer" level="INFO" additivity="false"/>
    <logger name="org.springframework.context.annotation.ConfigurationClassPostProcessor" level="INFO" additivity="false"/>
    <logger name="org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader" level="INFO" additivity="false"/>
    <logger name="org.springframework.context.annotation.ClassPathBeanDefinitionScanner" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider" level="INFO" additivity="false"/>
    <logger name="org.springframework.context.event.EventListenerMethodProcessor" level="INFO" additivity="false"/>
    <logger name="org.springframework.core.env.PropertySourcesPropertyResolver" level="INFO" additivity="false"/>
    <logger name="org.springframework.core.env.StandardEnvironment" level="INFO" additivity="false"/>
    <logger name="org.springframework.core.env.MutablePropertySources" level="INFO" additivity="false"/>
    <logger name="org.springframework.core.env.SystemEnvironmentPropertySource" level="INFO" additivity="false"/>
    <logger name="org.springframework.core.io.support.PathMatchingResourcePatternResolver" level="INFO" additivity="false"/>
    <logger name="org.springframework.core.type.classreading.AnnotationAttributesReadingVisitor" level="INFO" additivity="false"/>
    <logger name="org.springframework.data.redis.core.RedisConnectionUtils" level="INFO" additivity="false"/>
    <logger name="org.springframework.jdbc.core.JdbcTemplate" level="INFO" additivity="false"/>
    <logger name="org.springframework.jdbc.core.StatementCreatorUtils" level="INFO" additivity="false"/>
    <logger name="org.springframework.jdbc.datasource.DataSourceTransactionManager" level="INFO" additivity="false"/>
    <logger name="org.springframework.jdbc.datasource.DataSourceUtils" level="INFO" additivity="false"/>
    <logger name="org.springframework.jndi" level="INFO" additivity="false"/>
    <logger name="org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor" level="INFO" additivity="false"/>
    <logger name="org.springframework.session.data.redis.RedisOperationsSessionRepository" level="INFO" additivity="false"/>
    <logger name="org.springframework.session.data.redis.RedisSessionExpirationPolicy" level="INFO" additivity="false"/>
    <logger name="org.springframework.session.web.http.OnCommittedResponseWrapper" level="INFO" additivity="false"/>
    <logger name="org.springframework.session.web.http.SessionRepositoryFilter" level="INFO" additivity="false"/>
    <logger name="org.springframework.transaction.interceptor.RuleBasedTransactionAttribute" level="INFO" additivity="false"/>
    <logger name="org.springframework.transaction.support.TransactionSynchronizationManager" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.context.support.StandardServletEnvironment" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.context.support.XmlWebApplicationContext" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.method.HandlerMethod" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.web.method.support.HandlerMethodArgumentResolverComposite" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.servlet.DispatcherServlet" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.servlet.handler.SimpleUrlHandlerMapping" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.servlet.view.ContentNegotiatingViewResolver" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.servlet.view.freemarker" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.servlet.resource.CachingResourceResolver" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.web.servlet.resource.VersionResourceResolver" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.web.servlet.resource.ResourceHttpRequestHandler" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.web.servlet.resource.PathResourceResolver" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.web.servlet.resource.ResourceUrlProvider" level="INFO" additivity="false"/>
    <logger name="org.springframework.web.servlet.resource.CachingResourceTransformer" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.web.servlet.resource.CssLinkResourceTransformer" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.web.socket.server.support.WebSocketHandlerMapping" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.web.socket.sockjs.transport.handler.HtmlFileTransportHandler" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.web.socket.sockjs.transport.handler.XhrStreamingTransportHandler" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.web.socket.sockjs.transport.session.WebSocketServerSockJsSession" level="DEBUG" additivity="false"/>
    <logger name="org.springframework.ui.freemarker.SpringTemplateLoader" level="INFO" additivity="false"/>
    <logger name="com.github.mapper" level="INFO" additivity="false"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!--<withJansi>true</withJansi>-->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
            <!--<pattern>${CONSOLE_LOG_PATTERN}</pattern>-->
            <pattern>%clr(%d{yyyy-MM-dd HH:mm:ss}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}</pattern>
            <!--<pattern><pattern>[%-5p][%d{yyyy-MM-dd HH:mm:ss}]%c[%F]:%L - &lt;%m&gt;%n</pattern></pattern>-->
        </encoder>
    </appender>

    <!-- 消息日志，记录项目所有消息记录 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 如果指定了file属性，当天的文件名为file属性值 -->
        <file>logs/portal-spring.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名 -->
            <FileNamePattern>logs/portal-spring.%d{yyyy-MM-dd}.part_%i.log</FileNamePattern>
            <maxHistory>${LOG_MAX_HISTORY}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- maxFileSize:这是活动文件的大小，默认值是10MB,本篇设置为1KB，只是为了演示 -->
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

<!--    <appender name="stash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">-->
<!--        &lt;!&ndash;  这是是logstash服务器地址 端口&ndash;&gt;-->
<!--        <destination>127.0.0.1:4561</destination>-->
<!--        &lt;!&ndash;输出的格式，推荐使用这个&ndash;&gt;-->
<!--        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">-->
<!--            <providers>-->
<!--                <pattern>-->
<!--                    <pattern>-->
<!--                        &lt;!&ndash;输出日志可自定义，可根据自己需要配置&ndash;&gt;-->
<!--                        {-->
<!--                        &lt;!&ndash;应用名称 &ndash;&gt;-->
<!--                        "appName": "portal",-->
<!--                        &lt;!&ndash;打印时间 &ndash;&gt;-->
<!--                        "timestamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}",-->
<!--                        &lt;!&ndash;线程名称 &ndash;&gt;-->
<!--                        "thread": "%thread",-->
<!--                        &lt;!&ndash;日志级别 &ndash;&gt;-->
<!--                        "level": "%level",-->
<!--                        &lt;!&ndash;日志名称 &ndash;&gt;-->
<!--                        "loggerName": "%logger",-->
<!--                        &lt;!&ndash;日志信息 &ndash;&gt;-->
<!--                        "message": "%msg",-->
<!--                        &lt;!&ndash;日志堆栈 &ndash;&gt;-->
<!--                        "stackTrace": "%exception{10}"-->
<!--                        }-->
<!--                    </pattern>-->
<!--                </pattern>-->
<!--            </providers>-->
<!--        </encoder>-->
<!--    </appender>-->

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
<!--        <appender-ref ref="stash"/>-->
    </root>

</configuration>





