<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <title>AI 辅助工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }

        #app {
            display: flex;
            height: 90%;
            flex-direction: row;
            padding: 20px;
        }

        .left-panel {
            flex: 2;
            margin-right: 20px;
            position: relative;
            min-width: 300px;
        }

        .iframe-container {
            height: calc(100% - 42px);
            background-color: #e6f1f1;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            border: 1px solid #0057f7;
        }

        .iframe-header {
            padding: 10px 15px;
        }

        .iframe-content {
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .iframe-content iframe {
            width: 100%;
            height: 100%;
        }

        .pending-text {
            font-size: 48px;
            color: #aaa;
        }

        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 300px;
        }

        .reference-style {
            text-align: right;
            margin-bottom: 10px;
        }

        .reference-style-box {
            display: inline-block;
            border: 1px solid #ddd;
            padding: 5px;
            border-radius: 4px;
        }

        .reference-image {
            width: 40px;
            height: 30px;
        }

        .right-reference-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .chat-box {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: #fff;
            margin-bottom: 15px;
            max-height: 75%;
        }

        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
        }

        .input-container {
            display: flex;
            flex-direction: column;
        }

        .input-area {
            border: none;
            padding: 15px;
            resize: none;
            background-color: #fff;
            font-family: inherit;
            height: 50%;
        }

        .button-group {
            display: flex;
            justify-content: space-between;
        }

        .action-button {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fff;
            cursor: pointer;
            margin: 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .action-button:first-child {
            margin-left: 0;
        }

        .action-button:last-child {
            margin-right: 0;
        }

        .action-button:hover {
            background-color: #f5f5f5;
        }

        .blue-btn {
            color: #2468f2;
        }

        .blue-btn i {
            margin-right: 5px;
        }

        .green-btn {
            color: #0c8918;
        }

        .reference-example {
            margin-top: 15px;
            display: flex;
            font-size: 12px;
            color: #666;
            padding: 15px;
            height: 50%;
        }

        .reference-example img {
            width: 100%;
            height: 100%;
        }

        .reference-example-icon {
            align-self: center;
            margin-bottom: 10px;
            width: 24px;
            height: 24px;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            #app {
                flex-direction: column;
                height: auto;
                min-height: 100%;
            }

            .left-panel {
                height: 40vh;
                margin-right: 0;
                margin-bottom: 20px;
                min-width: 100%;
            }

            .right-panel {
                min-width: 100%;
                height: 60vh;
            }

            .button-group {
                flex-direction: column;
            }

            .action-button {
                margin: 5px 0;
            }
        }

        /* For IE9 compatibility */
        .ie9-display-flex {
            display: block \9;
        }

        .ie9-float-left {
            float: left \9;
        }

        .ie9-float-right {
            float: right \9;
        }

        .ie9-width-50 {
            width: 49% \9;
        }

        .ie9-height-100 {
            height: 100% \9;
        }
    </style>
    <!-- HTML5 shim for IE9 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script th:src="@{/assets/js/html5shiv.js}"></script>
    <script th:src="@{/assets/js/respond.min.js}"></script>
    <![endif]-->
</head>
<body>
<div id="app" class="ie9-display-flex">
    <div class="left-panel ie9-float-left ie9-width-50 ie9-height-100">
        <div class="iframe-header">当前样式预览</div>
        <div class="iframe-container">
            <div class="iframe-content">
                <div class="pending-text"
                     v-show="engineStyleId == null">待生成
                </div>
                <div class="pending-text"
                     v-show="engineStyleId != null && (previewIframeUrl == null || previewIframeUrl == '')">
                    正在生成......，预计需要两分钟，请耐心等待。
                </div>
                <iframe id="preview-iframe"
                        v-show="engineStyleId != null  && previewIframeUrl != null && previewIframeUrl != ''"
                        :src="previewIframeUrl"
                        frameborder="0"></iframe>
            </div>
        </div>
    </div>

    <div class="right-panel ie9-float-right ie9-width-50">
        <div class="reference-style">
            <span>参考样式：</span>
            <div class="reference-style-box">
                <img class="reference-image"
                     src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCAxMDAgODAiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAiIGhlaWdodD0iODAiIGZpbGw9IiMyNDY4ZjIiLz48cmVjdCB4PSI1IiB5PSI1IiB3aWR0aD0iOTAiIGhlaWdodD0iMjAiIGZpbGw9IiNmZmZmZmYiLz48cmVjdCB4PSI1IiB5PSIzMCIgd2lkdGg9IjkwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjZmZmZmZmIi8+PHJlY3QgeD0iNSIgeT0iNTUiIHdpZHRoPSI5MCIgaGVpZ2h0PSIyMCIgZmlsbD0iI2ZmZmZmZiIvPjwvc3ZnPg=="
                     alt="参考样式">
            </div>
            <div class="right-reference-text">图文列表-样式1</div>
        </div>

        <div class="chat-box" id="chatContainer">
            <div class="reference-example">
                <!-- 隐藏的文件选择控件 -->
                <input
                        type="file"
                        ref="fileInput"
                        @change="handleFileChange"
                        accept="image/*"
                        style="display: none"
                >
                <img class="reference-example-icon" v-if="imageUrl" :src="imageUrl" alt="参考图">
            </div>
            <textarea class="input-area" v-model="userInput" placeholder="请输入详细的图片描述内容..."
                      id="userInput"></textarea>

        </div>
        <div class="input-container">
            <div class="button-group" v-show="engineStyleId == null || !isGenerating">
                <!--                <button class="action-button blue-btn" id="polishBtn">
                                    <i>↺</i> 一键润色
                                </button>-->
                <button class="action-button" id="referenceBtn" @click="$refs.fileInput.click()">
                    替换参考图
                </button>
                <button class="action-button green-btn" id="generateBtn" @click="startGenerateHtml">
                    开始生成
                </button>
            </div>
            <div class="button-group"
                 v-show="engineStyleId != null && (previewIframeUrl == null || previewIframeUrl == '')">
                <button class="action-button green-btn" @click="startGenerateHtml">
                    生成中......
                </button>
            </div>
        </div>

    </div>
</div>

<script th:src="@{/assets/js/vue.min.js}"></script>
<script th:src="@{/assets/js/axios.min.js}"></script>
<script type="text/javascript" th:inline="text">
    var ctx = "[[@{/}]]";
    ctx = "/" == ctx ? "" : ctx;
    ctx = ctx.endsWith("/") ? ctx.substr(0, ctx.length - 1) : ctx;
</script>
<script th:inline="javascript">
    // Vue application
    var app = new Vue({
        el: '#app',
        data: {
            imageFile: null,
            imageUrl: '',
            userInput: '',
            engineStyleId: null,
            isGenerating: false,
            previewIframeUrl: '',
            timer: null,
        },
        mounted: function () {

        },
        methods: {
            checkGenerateEnd: function () {
                var $this = this;
                if ($this.timer != null) {
                    clearInterval($this.timer);
                }
                //定时器 5秒检查一次 检查样式是否生成完毕 生成完毕后 关闭定时器
                $this.timer = setInterval(() => {
                    axios.get(ctx + '/style/ai/status?styleId=' + this.engineStyleId).then(response => {
                        if (response.data.code == '1') {
                            clearInterval($this.timer);
                            this.isGenerating = false;
                            this.previewIframeUrl = window.location.origin + '/page/ai_single_module_preview/index.html?jsonId=1&aiStyleId=' + $this.engineStyleId;
                        }
                    });
                }, 5000);
            },
            startGenerateHtml: function () {
                if (this.imageFile == null) {
                    alert('请选择图片');
                    return;
                }
                if (this.isGenerating) {
                    alert('正在生成，请勿重复操作');
                    return;
                }
                const formData = new FormData();
                formData.append('file', this.imageFile);
                formData.append('picDesc', this.userInput);
                try {
                    axios.post(ctx + '/style/ai/generate', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        }
                    }).then(response => {
                        if (response.data.code == '1') {
                            console.log('上传成功', response.data);
                            this.isGenerating = true;
                            this.engineStyleId = response.data.data;
                            this.checkGenerateEnd();
                        }
                    });
                } catch (error) {
                    console.error('上传失败', error);
                }
            },
            handleFileChange(event) {
                const file = event.target.files[0];
                if (file) {
                    this.imageFile = file;
                    // 创建预览URL
                    this.imageUrl = URL.createObjectURL(file);
                }
            },
        },

    });
</script>
</body>
</html>