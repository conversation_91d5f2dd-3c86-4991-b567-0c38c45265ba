<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>参数非法</title>
</head>
<style>
    * {
        margin: 0;
        padding: 0;
    }

    body {
        background: #F7F9FD;
    }

    .error-info {
        position: absolute;
        top: 15%;
        left: 50%;
        z-index: 2;
        width: 400px;
        margin-left: -200px;
        text-align: center;
    }

    .error-info strong {
        font-weight: 600;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.8);
    }

    .error-info p {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.6);
        line-height: 42px;
    }

    .error-info a {
        text-decoration: none;
        font-size: 16px;
        color: #3D82F2;
    }

    .error-info a:hover {
        text-decoration: underline;
    }
</style>

<body>
<div class="error-info">
    <strong>很抱歉，你访问的页面参数非法</strong>
    <p>请检查你输入的网址是否正确，或者点击链接继续浏览</p>
    <a href="/">返回主页</a>
</div>

</body>

</html>