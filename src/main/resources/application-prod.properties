spring.application.name=@project.artifactId@
server.port=8101
server.servlet.context-path=/
server.servlet.session.tracking-modes=cookie
spring.resources.chain.strategy.content.enabled=true
spring.thymeleaf.cache=true
spring.thymeleaf.mode=HTML
spring.thymeleaf.excluded-view-names=view/index
spring.thymeleaf.template-resolver-order=0

server.tomcat.uri-encoding=UTF-8
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true
spring.messages.encoding=UTF-8
server.tomcat.threads.max=800
server.tomcat.accept-count=1200
server.tomcat.threads.min-spare=20
server.tomcat.max-http-form-post-size=10MB

spring.boot.admin.client.url=http://*************:8300
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=ALWAYS
spring.boot.admin.client.instance.name=portal-1.0
spring.boot.admin.client.instance.prefer-ip=true
spring.boot.admin.client.instance.service-url=http://*************:8101
# å±è½æå¯¹æä¸ä¸ªç»ä»¶ççæ§çæ§
management.endpoint.health.elasticsearch.enabled=false
management.endpoint.health.elasticsearchRest.enabled=false
spring.boot.admin.client.username=root_cx
spring.boot.admin.client.password=leo_1234A

spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.url=*************************************************************
spring.datasource.username=yanfa
spring.datasource.password=3tf4wFUfvlvfhxNeRD
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=10
spring.datasource.druid.max-active=20
spring.datasource.druid.max-wait=60000
spring.datasource.druid.time-between-eviction-runs-millis=10000
spring.datasource.druid.remove-abandoned=true
spring.datasource.druid.validation-query=SELECT 'x'
spring.datasource.druid.test-on-return=false
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-while-idle=true
#spring.datasource.druid.pool-prepared-statements=true
#spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.druid.async-init=true
spring.datasource.druid.keep-alive=true
spring.datasource.druid.stat-view-servlet.enabled=false
#spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
#spring.datasource.druid.connection-properties=config.decrypt=true
#spring.datasource.druid.filter.config.enabled=true

mybatis.mapper-locations=classpath*:/com/**/mapper/xml/*Mapper.xml

# \u5FC5\u987B\u662F\u5B8C\u6574\u57DF\u540D, \u7ED3\u5C3E\u65E0\u9700/
portal.domain-name=http://mh.chaoxing.com
engine-inner.domain-name=http://engine-svc:8202

## redis
spring.redis.host=************
spring.redis.port=30004
spring.redis.database=15
spring.redis.password=Uk0L0ldxmU1aJCSzykR

spring.redis.sentinel.master=mymaster
spring.redis.sentinel.nodes=*************:16379,*************:16380,*************:16381
#spring.redis.cluster.nodes=*************:6380,*************:6381,*************:6382,*************:6383,*************:6384,*************:6385
spring.redis.thread=16

# redis lettuce\u5BA2\u6237\u7AEF
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.pool.max-idle=20
spring.redis.lettuce.pool.max-wait=-1ms
spring.redis.lettuce.pool.max-active=20
spring.redis.lettuce.shutdown-timeout=100ms

redis.default.expiration=5
select.cache.timeout=5

# \u6D4B\u8BD5\u503C\uFF0C\u65B9\u4FBF\u5F3A\u5236\u5237\u65B0\u6D4B\u8BD5
select.cache.refresh=2

# \u4E0A\u4F20\u6587\u4EF6\u5927\u5C0F\u8BBE\u7F6E
spring.web.upload-location=${java.io.tmpdir}
#spring.web.upload-location=/Users/<USER>/docker-test/img/
spring.servlet.multipart.max-file-size=512MB
spring.servlet.multipart.max-request-size=512MB
spring.resources.static-locations=classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/static_dynamic/,classpath:/public/,file:${spring.web.upload-location}


spring.main.allow-bean-definition-overriding=true

spring.favicon.enabled=false
