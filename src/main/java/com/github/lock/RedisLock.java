package com.github.lock;

import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 *
 */
public class RedisLock {

    private static Logger logger = LoggerFactory.getLogger(RedisLock.class);

    private Redisson redisson;

    /**
     * 调用set后的返回值
     */
    public static final String OK = "OK";

    /**
     * 默认锁的有效时间(s)
     */
    public static final int EXPIRE = 60;

    /**
     * 锁标志对应的key
     */
    private String lockKey;


    /**
     * 锁的有效时间(s)
     */
    private int expireTime = EXPIRE;

    /**
     * 请求锁的超时时间(s)
     */
    private long timeOut = 0;

    private RLock lock;

    /**
     * 锁标记
     */
    private volatile boolean locked = false;

    /**
     * 使用默认的锁过期时间和请求锁的超时时间
     *
     * @param redisson
     * @param lockKey  锁的key（Redis的Key）
     */
    public RedisLock(Redisson redisson, String lockKey) {
        this.redisson = redisson;
        this.lockKey = lockKey + "_lock";
    }

    /**
     * 使用默认的请求锁的超时时间，指定锁的过期时间
     *
     * @param redisson
     * @param lockKey    锁的key（Redis的Key）
     * @param expireTime 锁的过期时间(单位：秒)
     */
    public RedisLock(Redisson redisson, String lockKey, int expireTime) {
        this(redisson, lockKey);
        this.expireTime = expireTime;
    }

    /**
     * 使用默认的锁的过期时间，指定请求锁的超时时间
     *
     * @param redisson
     * @param lockKey  锁的key（Redis的Key）
     * @param timeOut  请求锁的超时时间(单位：毫秒)
     */
    public RedisLock(Redisson redisson, String lockKey, long timeOut) {
        this(redisson, lockKey);
        this.timeOut = timeOut;
    }

    /**
     * 锁的过期时间和请求锁的超时时间都是用指定的值
     *
     * @param redisson
     * @param lockKey    锁的key（Redis的Key）
     * @param expireTime 锁的过期时间(单位：秒)
     * @param timeOut    请求锁的超时时间(单位：毫秒)
     */
    public RedisLock(Redisson redisson, String lockKey, int expireTime, long timeOut) {
        this(redisson, lockKey, expireTime);
        this.timeOut = timeOut;
    }

    /**
     * redis 锁机制，默认情况下。 单个redis可以支持500+/s的一个处理量。
     * 尝试获取锁 超时返回，如果timeout为0则直接返回
     *
     * @return
     */
    public boolean tryLock() {
        lock = redisson.getLock(lockKey);

        try {
            boolean isLock = lock.tryLock(timeOut, expireTime, TimeUnit.SECONDS);
            locked = isLock;
        } catch (InterruptedException e) {
            e.printStackTrace();
            locked = false;
        }
        // 上锁成功结束请求
        return locked;
    }

    /**
     * 等待尝试获取锁 没拿到锁之前，一直等待
     *
     * @return 是否成功获得锁
     */
    public boolean lock() {
        lock = redisson.getLock(lockKey);
        try {
            lock.lock(expireTime, TimeUnit.SECONDS);
            locked = true;
        } catch (Exception e) {
            locked = false;
        }
        return locked;
    }

    /**
     * 解锁
     */
    public Boolean unlock() {
        // 只有加锁成功并且锁还有效才去释放锁
        if (locked) {
            lock.forceUnlock();
        }
        return true;
    }

    public boolean isLock() {
        return locked;
    }

    public int getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(int expireTime) {
        this.expireTime = expireTime;
    }

    public long getTimeOut() {
        return timeOut;
    }

    public void setTimeOut(long timeOut) {
        this.timeOut = timeOut;
    }
}