package com.github.service;

import com.github.claude.AiImg2Style;
import com.github.lock.RedisLock;
import com.github.model.EngineStyle;
import com.github.model.EngineStyleHtml;
import org.apache.commons.lang.StringUtils;
import org.redisson.Redisson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * @author: liban
 * @description ai样式
 */
@Service
public class AiStyleService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private EngineStyleService engineStyleService;
    @Resource
    private EngineStyleHtmlService engineStyleHtmlService;
    @Resource
    private JdbcTemplate jdbcTemplate;
    @Resource
    private Redisson redisson;
    @Resource
    private AiImg2Style aiImg2Style;

    /**
     * 生成样式返回styleId, 预览iframe根据id获取预览样式
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer createEngineStyle(String imgUrl) {
        //id生成加锁
        RedisLock redisLock = new RedisLock(redisson, "ai_create_engine_style_id_lock");
        try {
            if (redisLock.tryLock()) {
                Integer indexNo = engineStyleService.getMaxNextId();
                //确保样式编号从100_000 开始
                if (indexNo < 100000) {
                    indexNo = 100000;
                }
                // 样式编号
                EngineStyle engineStyle = new EngineStyle();
                //预生成
                engineStyle.setId(indexNo);
                engineStyle.setName("样式" + indexNo);
                engineStyle.setCode(indexNo);
                engineStyle.setEngineType("aipic");
                engineStyle.setType(EngineStyle.STYLE_TYPE);
                engineStyle.setWfwfid(0);
                engineStyle.setOrder(indexNo);
                engineStyle.setStatus(0);
                //TODO 如果基于现有样式，直接复用样式缩略图
                //engineStyle.setImgUrl("/upload/engine/template/graphic/template_" + indexNo + ".jpg");
                //云盘
                engineStyle.setImgUrl(imgUrl);
                engineStyleService.add(engineStyle);
                return indexNo;
            }
        } finally {
            redisLock.unlock();
        }
        return null;
    }


    /**
     * 异步生成html样式
     *
     * @param base64Image base64图片
     * @param imgDesc     图片描述
     * @param indexNo     样式编号
     */
    @Async("taskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void asyncGenerateStyle(String base64Image, String imgDesc, Integer indexNo) {
        try {
            logger.info("开始调用AI生成样式");
            String result = aiImg2Style.createStyle(base64Image, imgDesc, indexNo);
            logger.info("调用AI生成样式结束");
            // 拆分result ```html 开始到```结束范围的都是样式html代码
            String resultHtml = result.substring(result.indexOf("```html") + 7);
            resultHtml = resultHtml.substring(0, resultHtml.indexOf("```"));
            // ```sql 开始到``` 结束的是待执行的sql语句
            String resultSql = result.substring(result.indexOf("```sql") + 6);
            resultSql = resultSql.substring(0, resultSql.indexOf("```"));
            logger.info("调用AI生成样式解析结束");
            logger.info(resultHtml);
            EngineStyleHtml engineStyleHtml = new EngineStyleHtml();
            engineStyleHtml.setHtml(resultHtml);
            engineStyleHtml.setStyleId(indexNo.toString());
            engineStyleHtmlService.add(engineStyleHtml);
            logger.info("调用AI生成样式入库");
            //更新预生成样式
            EngineStyle engineStyle = engineStyleService.get(indexNo);
            //engineStyle.setImgUrl("/upload/engine/template/graphic/template_" + indexNo + ".jpg");
            engineStyle.setStatus(1);
            engineStyleService.update(engineStyle);
            logger.info(resultSql);
            if (StringUtils.isNotBlank(resultSql)) {
                String[] sqlArray = resultSql.split(";");
                // 清除掉注释sql
                sqlArray = (String[]) Arrays.stream(sqlArray).filter(s -> !s.trim().startsWith("--")).toArray(String[]::new);
                if(sqlArray.length > 0 ){
                    jdbcTemplate.batchUpdate(sqlArray);
                }
            }
            logger.info("调用AI生成样式sql脚本执行结束");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


}
