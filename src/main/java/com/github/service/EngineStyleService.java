package com.github.service;

import com.github.mapper.EngineStyleMapper;
import com.github.model.EngineStyle;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version ver 1.0
 * @className EngineStyleService
 * @description
 * @blame xhl
 * @date 2020-05-18 19:26:24
 */
@Service
public class EngineStyleService {

    @Resource
    private EngineStyleMapper engineStyleMapper;

    public EngineStyle get(Integer id) {
        return engineStyleMapper.get(id);
    }

    public void add(EngineStyle engineStyle) {
        engineStyleMapper.add(engineStyle);
    }

    public void update(EngineStyle engineStyle) {
        engineStyleMapper.update(engineStyle);
    }

    public Integer getMaxNextId() {
        return engineStyleMapper.getMaxNextId();
    }

}
