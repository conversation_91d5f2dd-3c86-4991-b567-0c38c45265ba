/*
 */
package com.github.service;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.github.model.Zuoping;
import com.github.mapper.ZuopingMapper;


@Service
public class ZuopingService {

    @Resource private ZuopingMapper zuopingMapper;

    /** 添加 */
    @CachePut(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#zuoping.id)")
    public Zuoping add(Zuoping zuoping) {
        this.zuopingMapper.add(zuoping);
        return zuoping;
    }
    /** 删除 */
    @CacheEvict(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)")
    public void delete(Integer id) {
        this.zuopingMapper.delete(id);
    }
    /** 修改 */
    @CachePut(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#zuoping.id)")
    public Zuoping update(Zuoping zuoping) {
        this.zuopingMapper.update(zuoping);
        return zuoping;
    }
    /** 查看 - 从Cache中获取对象 */
    @Cacheable(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)", unless="#result eq null")
    public Zuoping get(Integer id) {
        return this.zuopingMapper.get(id);
    }
    /** 获取列表 */
    public List<Zuoping> getList() {
        return this.zuopingMapper.getList();
    }
    /** 获取条件列表 */
    public List<Zuoping> getZuopingList(Zuoping zuoping) {
        return this.zuopingMapper.getZuopingList(zuoping);
    }

    /**
     * 查询分组
     * @param
     * @return
     */
    public List<Zuoping> getFenzuList() {
        return this.zuopingMapper.getFenzuList();
    }
    public List<Zuoping> getSaiquList() {
        return this.zuopingMapper.getSaiquList();
    }
    /* --------------------------------------------------- */
}

