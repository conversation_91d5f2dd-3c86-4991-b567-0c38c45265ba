/*
 */
package com.github.service;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.github.model.Zhuanjia;
import com.github.mapper.ZhuanjiaMapper;


@Service
public class ZhuanjiaService {

    @Resource private ZhuanjiaMapper zhuanjiaMapper;

    /** 添加 */
    @CachePut(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#zhuanjia.id)")
    public Zhuanjia add(Zhuanjia zhuanjia) {
        this.zhuanjiaMapper.add(zhuanjia);
        return zhuanjia;
    }
    /** 删除 */
    @CacheEvict(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)")
    public void delete(Integer id) {
        this.zhuanjiaMapper.delete(id);
    }
    /** 修改 */
    @CachePut(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#zhuanjia.id)")
    public Zhuanjia update(Zhuanjia zhuanjia) {
        this.zhuanjiaMapper.update(zhuanjia);
        return zhuanjia;
    }
    /** 查看 - 从Cache中获取对象 */
    @Cacheable(value="objectCache", key="(#root.targetClass.getSimpleName()).concat(':id:').concat(#id)", unless="#result eq null")
    public Zhuanjia get(Integer id) {
        return this.zhuanjiaMapper.get(id);
    }
    /** 获取列表 */
    public List<Zhuanjia> getList() {
        return this.zhuanjiaMapper.getList();
    }
    /** 获取条件列表 */
    public List<Zhuanjia> getZhuanjiaList(Zhuanjia zhuanjia) {
        return this.zhuanjiaMapper.getZhuanjiaList(zhuanjia);
    }
    /* --------------------------------------------------- */
}

