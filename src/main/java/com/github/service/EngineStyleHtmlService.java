package com.github.service;

import com.github.mapper.EngineStyleHtmlMapper;
import com.github.model.EngineStyleHtml;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version ver 1.0
 * @className EngineStyleService
 * @description
 * @blame xhl
 * @date 2020-05-18 19:26:24
 */
@Service
public class EngineStyleHtmlService {

    @Resource
    private EngineStyleHtmlMapper engineStyleHtmlMapper;

    public EngineStyleHtml get(Integer id) {
        return engineStyleHtmlMapper.get(id);
    }
    public EngineStyleHtml getByStyleId(Integer styleId) {
        return engineStyleHtmlMapper.getByStyleId(styleId);
    }

    public void add(EngineStyleHtml engineStyleHtml) {
        engineStyleHtmlMapper.add(engineStyleHtml);
    }

    public void update(EngineStyleHtml engineStyleHtml) {
        engineStyleHtmlMapper.update(engineStyleHtml);
    }
}
