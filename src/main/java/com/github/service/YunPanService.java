package com.github.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.util.NullCheckUtils;
import com.github.util.RegexUtil;
import com.github.util.VerifyFileType;
import com.github.util.exception.BusinessException;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;

/**
 * @author: liban
 * @description 描述
 */
@Service
public class YunPanService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String ERROR_BAD_TYPE_MSG = "非法文件上传";
    @Resource
    private RestTemplate restTemplate;
    private static final String HTTPS_CS_ANANAS = "https://cs.ananas.chaoxing.com";
    private static final String YUNPAN_STATUS = "/status/%s";

    /**
     * 上传图片，返回图片地址
     *
     * @param uploadFile
     * @param uid
     * @param ip
     * @return
     */
    public String uploadImg(MultipartFile uploadFile, String uid, String ip) {
        String url = HTTPS_CS_ANANAS + "/upload?uid={uid}&prdid=40&uploadtype=normal";
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

        String filename = uploadFile.getOriginalFilename();
        if (!RegexUtil.isAccordFile(filename)) {
            throw new BusinessException(ERROR_BAD_TYPE_MSG);
        }

        try {
            InputStream is = uploadFile.getInputStream();
            String fileOfType = VerifyFileType.getFileType(is);
            if (StringUtils.isNotBlank(fileOfType) && !fileOfType.matches("jpg|jpeg|png|bmp|gif|zip|rar|mp4|flv|avi|rm|rmvb|wmv|doc|docx|xlsx|7z|epub|txt|log|ppt|xls|pdf|sql|mp3|webm|mpg|wav|wps|mov")) {
                throw new BusinessException(ERROR_BAD_TYPE_MSG);
            }
            if (!VerifyFileType.checkFile(is)) {
                throw new BusinessException(ERROR_BAD_TYPE_MSG);
            }

            body.add("file", new MultipartInputStreamFileResource(uploadFile.getInputStream(), uploadFile.getOriginalFilename()));
        } catch (IOException e) {
            e.printStackTrace();
        }

        body.add("clientip", ip);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(body, headers);

        String responseContent = restTemplate.postForObject(url, httpEntity, String.class, uid);
        logger.error("云盘上传返回结果：{}", responseContent);
        JSONObject resultObject = JSON.parseObject(responseContent);

        if (resultObject.getIntValue("result") == 1) {
            String objectId = resultObject.getString("objectid");
            logger.info("云盘上传objectId：{}", objectId);
            JSONObject fileStatus = getYunpanStatus(objectId);
            //图片地址
            String httpUrl = fileStatus.getString("http");
            logger.info("云盘上传httpUrl, url:{} ", httpUrl);
            return httpUrl;
        } else {
            logger.error("上传文件异常：" + responseContent);
            return "";
        }
    }

    public JSONObject getYunpanStatus(String objectId) {
        NullCheckUtils.isNull(objectId);
        String responseContent = restTemplate.getForObject(String.format(HTTPS_CS_ANANAS + YUNPAN_STATUS, objectId), String.class);
        return JSON.parseObject(responseContent);
    }

    /**
     * Works with {@link org.springframework.http.converter.ResourceHttpMessageConverter handlingInputStreams} to forward input stream from
     * file-uploads without reading everything into memory.
     * fix bug: https://jira.spring.io/browse/SPR-13571
     */
    private static class MultipartInputStreamFileResource extends InputStreamResource {

        private final String filename;

        public MultipartInputStreamFileResource(InputStream inputStream, String filename) {
            super(inputStream);
            this.filename = filename;
        }

        @Override
        public String getFilename() {
            return this.filename;
        }

        @Override
        public long contentLength() throws IOException {
            return -1; // we do not want to generally read the whole stream into memory ...
        }
    }

}
