package com.github.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.com.viapi.FileUtils;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.imageenhan.model.v20190930.GenerateImageWithTextAndImageRequest;
import com.aliyuncs.imageenhan.model.v20190930.GenerateImageWithTextAndImageResponse;
import com.aliyuncs.imageenhan.model.v20190930.GetAsyncJobResultRequest;
import com.aliyuncs.imageenhan.model.v20190930.GetAsyncJobResultResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.github.dto.ImageToImageParamDTO;
import com.github.enums.ImagToImageFunctionEnum;
import com.github.util.Constants;
import com.github.util.RestResponse;
import com.google.gson.Gson;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.*;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.rmi.ServerException;

/** 阿里图文生图包装
 * <AUTHOR>
 * @version ver 1.0
 * @className ImageToImageController
 * @description
 * @blame gaojian
 * @date 2025-03-26 15:08:57
 */
@RestController
@RequestMapping("/ai/image-to-image")
public class ImageToImageController {

    @Resource()
    private RestTemplate restTemplate;
    private static final String URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis";
    private static final String TASK_URL = "https://dashscope.aliyuncs.com/api/v1/tasks/";
    private static final String MODEL = "wanx2.1-imageedit";
    private final IAcsClient client = new DefaultAcsClient(DefaultProfile.getProfile("cn-shanghai", Constants.ACCESS_KEY_ID, Constants.ACCESS_KEY_SECRET));

    /**
     * 图生图
     *
     * @param sseRequest
     * @return RestResponse
     * @Description url只支持原始地址，不支持代理地址，
     * 图像格式：JPG、JPEG、PNG、BMP、TIFF、WEBP。
     *
     * 图像分辨率：图像的宽度和高度范围为[512, 4096]像素。
     *
     * 图像大小：不超过10MB。
     *
     * URL地址中不能包含中文字符。
     * <AUTHOR>
     * @Date 2025-03-26 05:47:39
     */
    @RequestMapping()
    public RestResponse imageToImage(@RequestBody String sseRequest) {
        JSONObject json = JSONObject.parseObject(sseRequest);
        String sign = json.getString("sign");
        Long time = json.getLongValue("time");
        String text = json.getString("text");
        String function = json.getString("function");
        String baseImageUrl = json.getString("baseImageUrl");
        String maskImageUrl = json.getString("maskImageUrl");
        Integer page = json.getInteger("page");
        Boolean watermark = json.getBoolean("watermark");
        Integer upscaleFactor = json.getInteger("upscaleFactor");
        Float topScale = json.getFloat("top_scale");
        Float bottomScale = json.getFloat("bottom_scale");
        Float leftScale = json.getFloat("left_scale");
        Float rightScale = json.getFloat("right_scale");
        if (page == null) {
            page = 1;
        }
        if (watermark == null) {
            watermark = false;
        }
        if (StringUtils.isEmpty(text) || StringUtils.isEmpty(function) || StringUtils.isEmpty(baseImageUrl)) {
            return RestResponse.error("参数错误");
        }
        String thisSign = DigestUtils.sha256Hex(text + function + baseImageUrl + Constants.COOKIE_KEY_SIGN3 + time);
        if (sign.equals(thisSign)) {
            // 超过十分钟，拒绝
            if (System.currentTimeMillis() - time > Constants.EFFECTIVE_TIME) {
                return RestResponse.error("请求超期");
            }
        } else {
            return RestResponse.error("验签失败");
        }
        ImageToImageParamDTO imageToImageParamDTO = ImageToImageParamDTO.builder()
                .input(ImageToImageParamDTO.Input.builder()
                        .base_image_url(baseImageUrl)
                        .function(function)
                        .prompt(text)
                        .build())
                .model(MODEL)
                .parameters(ImageToImageParamDTO.Parameters.builder()
                        .n(page)
                        .watermark(watermark)
                        .bottom_scale(null)
                        .build())
                .build();
        // 如果是局部重绘，则将需要涂抹的区域图片url封装
        if (ImagToImageFunctionEnum.DESCRIPTION_EDIT_WITH_MASK.getValue().equals(function)) {
            if (StringUtils.isEmpty(maskImageUrl)) {
                return RestResponse.error("请传入需要涂抹区域地址");
            }
            imageToImageParamDTO.getInput().setMask_image_url(maskImageUrl);
        } else if (ImagToImageFunctionEnum.SUPER_RESOLUTION.getValue().equals(function)) {
            if (upscaleFactor != null) {
                upscaleFactor = upscaleFactor < 1 ? 1 : upscaleFactor;
                upscaleFactor = upscaleFactor > 4 ? 4 : upscaleFactor;
                imageToImageParamDTO.getParameters().setUpscale_factor(upscaleFactor);
            }
        } else if (ImagToImageFunctionEnum.EXPAND.getValue().equals(function)) {
            if (topScale != null) {
                topScale = topScale < 1.0f ? 1.0f : topScale;
                topScale = topScale > 2.0f ? 2.0f : topScale;
                imageToImageParamDTO.getParameters().setTop_scale(topScale);
            }
            if (bottomScale != null) {
                bottomScale = bottomScale < 1.0f ? 1.0f : bottomScale;
                bottomScale = bottomScale > 2.0f ? 2.0f : bottomScale;
                imageToImageParamDTO.getParameters().setBottom_scale(bottomScale);
            }
            if (leftScale != null) {
                leftScale = leftScale < 1.0f ? 1.0f : leftScale;
                leftScale = leftScale > 2.0f ? 2.0f : leftScale;
                imageToImageParamDTO.getParameters().setLeft_scale(leftScale);
            }
            if (rightScale != null) {
                rightScale = rightScale < 1.0f ? 1.0f : rightScale;
                rightScale = rightScale > 2.0f ? 2.0f : rightScale;
                imageToImageParamDTO.getParameters().setRight_scale(rightScale);
            }
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + Constants.apiKey);
        headers.add("X-DashScope-Async", "enable");
        String paramStr = JSONObject.toJSONString(imageToImageParamDTO);
        HttpEntity httpEntity = new HttpEntity(paramStr, headers);
        try {
            String html = restTemplate.postForObject(URL, httpEntity, String.class);
            return RestResponse.ok(JSONObject.parseObject(html));
        } catch (RestClientException e) {
            e.printStackTrace();
        }
        return RestResponse.error("服务器连接超时，请稍后再试");
    }

    /**
     * 根据taskid获取图生图的图片地址
     *
     * @param taskId
     * @param time
     * @param sign
     * @return RestResponse
     * @Description
     * <AUTHOR>
     * @Date 2025-03-26 05:47:03
     */
    @GetMapping("tasks")
    public RestResponse tasks(String taskId, Long time, String sign) {
        if (StringUtils.isEmpty(taskId) || time == null || StringUtils.isEmpty(sign)) {
            return RestResponse.error("参数错误");
        }
        String thisSign = DigestUtils.sha256Hex(taskId + Constants.COOKIE_KEY_SIGN3 + time);
        if (sign.equals(thisSign)) {
            // 超过十分钟，拒绝
            if (System.currentTimeMillis() - time > Constants.EFFECTIVE_TIME) {
                return RestResponse.error("请求超期");
            }
        } else {
            return RestResponse.error("验签失败");
        }
        String url = TASK_URL + taskId;
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer " + Constants.apiKey);
        HttpEntity httpEntity = new HttpEntity(headers);
        try {
            String html = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class).getBody();
            return RestResponse.ok(JSONObject.parseObject(html));
        } catch (RestClientException e) {
            e.printStackTrace();
        }
        return RestResponse.error("服务器连接超时，请稍后再试");
    }


    /** 通用图文生图(SDK v2版本会有jar包冲突，改用 v1版本)
    * @Description
    * @param
    * @return RestResponse
    * <AUTHOR>
    * @Date 2025-03-27 04:41:10
    */
    @RequestMapping("custom")
    public RestResponse customImageToImage(@RequestBody String sseRequest) {
        JSONObject json = JSONObject.parseObject(sseRequest);
        String text = json.getString("text");
        String refImageUrl = json.getString("refImageUrl");
        String resolution = json.getString("resolution");
        Integer number = json.getInteger("number");
        Float similarity = json.getFloat("similarity");
        String aspectRatioMode = json.getString("aspectRatioMode");
        Long time = json.getLong("time");
        String sign = json.getString("sign");
        if (StringUtils.isEmpty(text) || StringUtils.isEmpty(refImageUrl) || time == null || StringUtils.isEmpty(sign)) {
            return RestResponse.error("参数错误");
        }
        String thisSign = DigestUtils.sha256Hex(text + refImageUrl + Constants.COOKIE_KEY_SIGN4 + time);
        if (sign.equals(thisSign)) {
            if (System.currentTimeMillis() - time > Constants.EFFECTIVE_TIME) {
                return RestResponse.error("请求超期");
            }
        } else {
            return RestResponse.error("验签失败");
        }
        if (!refImageUrl.startsWith("http")) {
            refImageUrl = "https://hd.chaoxing.com/hd/cloud-proxy/image/" + refImageUrl;
        }
        //上传的地址应该是云盘的下载地址
        refImageUrl = getImagUrl(refImageUrl);
        if (StringUtils.isEmpty(refImageUrl)) {
            return RestResponse.error("图片处理失败");
        }
        //先将云盘地址上传到阿里云服务器，再进行下一步操作
        GenerateImageWithTextAndImageRequest request = new GenerateImageWithTextAndImageRequest();
        request.setText(text);
        request.setRefImageUrl(refImageUrl);
        if (StringUtils.isNotEmpty(resolution)) {
            request.setResolution(resolution);
        }
        if (number != null) {
            number = number < 1 ? 1 : number;
            number = number > 4 ? 4 : number;
            request.setNumber(number);
        }
        if (similarity != null) {
            similarity = similarity < 0f ? 0f : similarity;
            similarity = similarity > 1f ? 1f : similarity;
            request.setSimilarity(similarity);
        }
        if (StringUtils.isNotEmpty(aspectRatioMode) && "resize".equals(aspectRatioMode)) {
            request.setAspectRatioMode(aspectRatioMode);
        }
        try {
            GenerateImageWithTextAndImageResponse response = client.getAcsResponse(request);
            return RestResponse.ok(response);
        } catch (ClientException e) {
            return RestResponse.error(e.getErrMsg());
        }
    }

    /** 通用图生图异步获取结果
    * @Description
    * @param taskId
    * @return RestResponse
    * <AUTHOR>
    * @Date 2025-03-28 03:45:00
    */
    @GetMapping("customTasks")
    public RestResponse customTasks(String taskId,Long time,String sign) {
        if (StringUtils.isEmpty(taskId) || time == null || StringUtils.isEmpty(sign)) {
            return RestResponse.error("参数错误");
        }
        String thisSign = DigestUtils.sha256Hex(taskId + Constants.COOKIE_KEY_SIGN4 + time);
        if (sign.equals(thisSign)) {
            // 超过十分钟，拒绝
            if (System.currentTimeMillis() - time > Constants.EFFECTIVE_TIME) {
                return RestResponse.error("请求超期");
            }
        } else {
            return RestResponse.error("验签失败");
        }
        GetAsyncJobResultRequest request = new GetAsyncJobResultRequest();
        request.setJobId(taskId);
        try {
            GetAsyncJobResultResponse response = client.getAcsResponse(request);
            return RestResponse.ok(response);
        } catch (ClientException e) {
            return RestResponse.error(e.getErrMsg());
        }
    }

    /** 将图片上传到阿里云服务器，qpi要求这样做
    * @Description
    * @param url
    * @return String
    * <AUTHOR>
    * @Date 2025-03-28 05:11:07
    */
    private String getImagUrl(String url) {
        try {
            FileUtils fileUtils = FileUtils.getInstance(Constants.ACCESS_KEY_ID, Constants.ACCESS_KEY_SECRET);
            return fileUtils.upload(url);
        } catch (ClientException | IOException e) {
            e.printStackTrace();
        }
        return null;
    }

}
