package com.github.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @className IndexController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/2 21:30
 * @Version 1.0
 **/
@Controller
@RequestMapping
public class IndexController {

    /**
     * 首页
     * @return
     */
    @RequestMapping
    public String index(){
        return "index";
    }

    /**
     * 用户-文档列表 首页
     * @return
     */
    @RequestMapping("index")
    public String index(ModelAndView view){

        return "aigc-document";
    }

    /**
     * 访问错误
     * @return
     */
    @RequestMapping("ai-error")
    public String error(ModelAndView view){
        return "error/50x";
    }

    /**
     * 未授权
     * @return
     */
    @RequestMapping("no-auth")
    public String noAuth(ModelAndView view){
        return "error/401";
    }

}
