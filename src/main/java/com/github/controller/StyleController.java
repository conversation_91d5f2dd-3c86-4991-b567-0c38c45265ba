package com.github.controller;

import com.github.model.EngineStyle;
import com.github.service.AiStyleService;
import com.github.service.EngineStyleService;
import com.github.service.YunPanService;
import com.github.util.CookieUtils;
import com.github.util.IPUtils;
import com.github.util.RestResponse;
import com.github.util.SpringContextHolder;
import com.github.util.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Base64;
import java.util.Objects;

/**
 * @className StyleController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/8 11:12
 * @Version 1.0
 **/
@RestController
@RequestMapping("style/ai")
public class StyleController {

    @Resource
    private AiStyleService aiStyleService;
    @Resource
    private EngineStyleService engineStyleService;
    @Resource
    private YunPanService yunPanService;


    /**
     * 传入的图片和描述，其中描述可以为空，但是图片必须传入。
     *
     * @param file
     * @param picDesc
     * @return
     */
    @RequestMapping("generate")
    public RestResponse generateAiStyle(@RequestParam("file") MultipartFile file, String picDesc, HttpServletRequest request) {
        if (Objects.isNull(file)) {
            return RestResponse.error("请传入图片");
        }
        String base64Image;
        try {
            base64Image = Base64.getEncoder().encodeToString(file.getBytes());
        } catch (IOException e) {
            throw new BusinessException("图片转换失败");
        }
        String coverUrlPath = "";
        if (SpringContextHolder.isProd()) {
            String uid = CookieUtils.getCookie(request, CookieUtils.UID);
            String ip = IPUtils.getRequestClientRealIP(request);
            //样式缩略图存云盘 上传云盘
            coverUrlPath = yunPanService.uploadImg(file, uid, ip);
            if (StringUtils.isBlank(coverUrlPath)) {
                return RestResponse.error("样式缩略图上传云盘失败");
            }
        }
        Integer engineStyle = aiStyleService.createEngineStyle(coverUrlPath);
        if (Objects.isNull(engineStyle) || engineStyle <= 100_000) {
            return RestResponse.error("样式编号生成失败");
        }
        aiStyleService.asyncGenerateStyle(base64Image, picDesc, engineStyle);
        return RestResponse.ok(engineStyle);
    }


    /**
     * 样式是否生成完成 判断样式表的状态字段即可
     *
     * @param styleId
     * @return
     */
    @RequestMapping("status")
    public RestResponse getAiStyleStatus(@RequestParam("styleId") Integer styleId) {
        if (Objects.isNull(styleId)) {
            return RestResponse.error("请传入styleId");
        }
        if (styleId < 100_000) {
            return RestResponse.error("styleId格式错误");
        }
        EngineStyle engineStyle = engineStyleService.get(styleId);
        if (Objects.isNull(engineStyle)) {
            return RestResponse.error("样式不存在");
        }
        if (engineStyle.getStatus() == 0) {
            return RestResponse.error("样式生成中");
        }
        return RestResponse.ok("样式生成完成");
    }


}
