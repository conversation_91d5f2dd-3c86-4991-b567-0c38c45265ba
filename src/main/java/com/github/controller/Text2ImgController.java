package com.github.controller;

import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesis;
import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesisParam;
import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesisResult;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson.JSONObject;
import com.github.util.Constants;
import com.github.util.RestResponse;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;

/**
 * @className Text2ImgController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 11:11
 * @Version 1.0
 * 文档地址 https://help.aliyun.com/zh/model-studio/developer-reference/text-to-image-api-reference?spm=a2c4g.11186623.help-menu-2400256.d_3_3_1_0.4ef944ceXYyrrg&scm=20140722.H_2712483._.OR_help-T_cn#DAS#zh-V_1
 **/
@RestController
@RequestMapping("/ai/text-to-img")
public class Text2ImgController {

    @RequestMapping
    public RestResponse handleRequest(@RequestBody String sseRequest){

        JSONObject json = JSONObject.parseObject(sseRequest);
        String sign = json.getString("sign");
        Long time = json.getLongValue("time");
        String text = json.getString("text");
        String thisSign = DigestUtils.sha256Hex(text + Constants.COOKIE_KEY_SIGN + time);
        if (sign.equals(thisSign)) {
            // 超过十分钟，拒绝
            if (System.currentTimeMillis() - time > 10 * 60 * 1000) {
                return RestResponse.error("请求超期");
            }
        } else {
            return RestResponse.error("验签失败");
        }

        ImageSynthesisParam param =
                ImageSynthesisParam.builder()
                        .apiKey(System.getenv("DASHSCOPE_API_KEY"))
                        .model(ImageSynthesis.Models.WANX_V1)
                        .prompt(text)
//                        .style("<watercolor>")
                        .n(1)
                        .size("1280*720")
                        .apiKey(Constants.apiKey)
                        .build();

        ImageSynthesis imageSynthesis = new ImageSynthesis();
        ImageSynthesisResult result = null;
        try {
            result = imageSynthesis.call(param);
            return RestResponse.ok(result.getOutput().getResults());
        } catch (ApiException | NoApiKeyException e){
            throw new RuntimeException(e.getMessage());
        }
    }


    @RequestMapping("custom")
    public RestResponse handleRequestCustom(@RequestBody String sseRequest){

        JSONObject json = JSONObject.parseObject(sseRequest);
        String sign = json.getString("sign");
        Long time = json.getLongValue("time");
        String text = json.getString("text");
        String size = json.getString("size");
        String style = json.getString("style");
        String thisSign = DigestUtils.sha256Hex(text + size + style + Constants.COOKIE_KEY_SIGN2 + time);
        if (sign.equals(thisSign)) {
            // 超过十分钟，拒绝
            if (System.currentTimeMillis() - time > 10 * 60 * 1000) {
                return RestResponse.error("请求超期");
            }
        } else {
            return RestResponse.error("验签失败");
        }

        if(StringUtils.isBlank(size)){
            size = "1280*720";
        }
        if(StringUtils.isNotBlank(style)){
            style = "<" + style + ">";
        }else{
            style = "<auto>";
        }

        ImageSynthesisParam param =
                ImageSynthesisParam.builder()
                        .apiKey(System.getenv("DASHSCOPE_API_KEY"))
                        .model(ImageSynthesis.Models.WANX_V1)
                        .prompt(text)
                        .style(style)
//                        .style("<watercolor>")
                        .n(2)
                        .size(size)
                        .apiKey(Constants.apiKey)
                        .build();

        ImageSynthesis imageSynthesis = new ImageSynthesis();
        ImageSynthesisResult result = null;
        try {
            result = imageSynthesis.call(param);
            return RestResponse.ok(result.getOutput().getResults());
        } catch (ApiException | NoApiKeyException e){
            throw new RuntimeException(e.getMessage());
        }
    }

}
