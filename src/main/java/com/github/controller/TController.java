package com.github.controller;

import com.github.model.Zhuanjia;
import com.github.model.Zuoping;
import com.github.service.ZhuanjiaService;
import com.github.service.ZuopingService;
import com.github.util.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version v1.0
 * @author: leolin
 * @since: 12/05/2020 5:49 PM
 * @description :类描述
 */
@Controller
@RequestMapping("t")
public class TController {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private UuidUtils uuidUtils;
    @Resource
    private RestTemplateBuilder restTemplateBuilder;

    @Resource private ZuopingService zuopingService;
    @Resource private ZhuanjiaService zhuanjiaService;

    Logger logger = LoggerFactory.getLogger(TController.class);

    @RequestMapping
    @ResponseBody
    public String test(){
        return "test";
    }

    @ResponseBody
    @RequestMapping("zj")
    public void dealZhuanjia(String fenzuInput, @RequestParam(defaultValue = "17") Integer chooseNum, HttpServletRequest request, HttpServletResponse response) {
        System.out.println(String.format("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s", "评审分组", "姓名", "手机号", "赛区", "工作单位", "所属学科门类", "第一推荐", "第二推荐", "专业方向", "专家来源"));
        List<Map> dataList = new ArrayList<>();
        List<Zuoping> fenzuListAll = zuopingService.getFenzuList();
        List<Zuoping> saiquListAll = zuopingService.getSaiquList();

        // 根据分组信息，查询到不重复的分组  分组需要剔除掉最后2位
        List<String> fenzuList = fenzuListAll.stream().map(Zuoping::getFenzu2).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<String> saiquList = saiquListAll.stream().map(Zuoping::getSaiqu).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        // 建立一个全局的专家set集合，用以用来保存，已经筛选出来的专家信息
        Set<Zhuanjia> zhuanjiaSetTop = new HashSet<>();

        // 根据分组，查询到专家信息
        for (String fenzu : fenzuList) {
            // 指定分组进行筛选。有数据重复风险。【不清楚一套抽取执行多少次，那些应该去重，最好是整体一次性抽取完】
            if(StringUtils.isNotBlank(fenzuInput)){
                if(! fenzu.equals(fenzuInput)){
                    continue;
                }
            }

            List<Zhuanjia> zhuanjiaList = zhuanjiaService.getZhuanjiaListDytj(fenzu);

            // 假定第一推荐下的用户足够
            // 根据fenzu 从fenzuListAll 当中，找到以fenzu开头的所有分组列表
            List<Zuoping> zuopingList = fenzuListAll.stream().filter(zuoping -> zuoping.getFenzu() != null && zuoping.getFenzu().startsWith(fenzu)).collect(Collectors.toList());
            // 当前分组 查到的分组1组，分组2组等数据
            List<String> fenzuTmpList = zuopingList.stream().map(Zuoping::getFenzu).filter(Objects::nonNull).distinct().collect(Collectors.toList());

            // 根据作品信息的分组，查询出来所有的作品
            List<Zuoping> listZuopingTmp = zuopingService.getListByFenzu(fenzu);

            // 比较专家列表和作品列表里面的工作单位数据，有相同的工作单位的专家做剔除处理
            List<Zhuanjia> listZhuanjiaTmp = new ArrayList<>();
            for (Zhuanjia zhuanjia : zhuanjiaList) {
                boolean match = false;
                for (Zuoping zuoping : listZuopingTmp) {
                    if (zhuanjia.getGzdw() != null && zuoping.getGzdw() != null && zhuanjia.getGzdw().equals(zuoping.getGzdw())) {
                        match=true;
                        break;
                    }
                }
                if(!match) {
                    listZhuanjiaTmp.add(zhuanjia);
                }
            }

            // 从专家列表里面，选出dept字段包含"教育学"的专家
            List<Zhuanjia> listZhuanjiaTmpJiaoyu = listZhuanjiaTmp.stream().filter(zhuanjia -> zhuanjia.getDept() != null && zhuanjia.getDept().contains("教育学")).collect(Collectors.toList());

            // 从专家列表里面，宣传zjly字段包含"行业企业"的专家
            List<Zhuanjia> listZhuanjiaTmpCjrh = listZhuanjiaTmp.stream().filter(zhuanjia -> zhuanjia.getZjly() != null && zhuanjia.getZjly().contains("行业企业")).collect(Collectors.toList());

            // listZhuanjiaTmp 里面剔除掉 listZhuanjiaTmp2 的专家
            listZhuanjiaTmp.removeAll(listZhuanjiaTmpJiaoyu);
            listZhuanjiaTmp.removeAll(listZhuanjiaTmpCjrh);

            // 得到的专家信息里面，按照赛区进行分组，然后各赛区的数据保存到一个list当中
            Map<String, List<Zhuanjia>> saiquMap = listZhuanjiaTmp.stream().filter(zhuanjia -> zhuanjia.getSaiqu() != null).collect(Collectors.groupingBy(Zhuanjia::getSaiqu));

            // 从 fenzuTmpList 拿出fenzu信息，然后从各个赛区轮流选择17个专家出来，这17个专家里面必须有一个dept字段包含"教育学"的专家，且选出来的专家将从专家库listZhuanjiaTmp中移除
            for (int i=0; i<fenzuTmpList.size(); i++) {
                String fenzuTmp = fenzuTmpList.get(i);

                Set<String> saiquChoolsed = new HashSet<>();
                List<Zhuanjia> zTmp = new ArrayList<>();
                if(listZhuanjiaTmpJiaoyu.size() > (i+1)) {
                    zTmp.add(listZhuanjiaTmpJiaoyu.get(listZhuanjiaTmpJiaoyu.size() - (i + 1)));
                    zhuanjiaSetTop.add(listZhuanjiaTmpJiaoyu.get(listZhuanjiaTmpJiaoyu.size() - (i + 1)));
                    saiquChoolsed.add(listZhuanjiaTmpJiaoyu.get(listZhuanjiaTmpJiaoyu.size() - (i + 1)).getSaiqu());
                }

                // 如果是产教融合,17个专家里面，至少需要保留一个“行业企业”的专家
                if (fenzuTmp.contains("产教融合") && listZhuanjiaTmpCjrh.size() > (i+1)) {
                    zTmp.add(listZhuanjiaTmpCjrh.get(listZhuanjiaTmpCjrh.size() - (i+1)));
                    zhuanjiaSetTop.add(listZhuanjiaTmpCjrh.get(listZhuanjiaTmpCjrh.size() - (i+1)));
                    saiquChoolsed.add(listZhuanjiaTmpCjrh.get(listZhuanjiaTmpCjrh.size() - (i+1)).getSaiqu());
                }

                // 从listZuopingTmp 中拿到当前分组里面，所有的学科门户dept
                Set<String> deptSet = listZuopingTmp.stream().filter(zuoping -> zuoping.getFenzu().equals(fenzuTmp)).map(Zuoping::getDept).filter(Objects::nonNull).distinct().collect(Collectors.toSet());
//                System.out.println("学科门类列表："+JSONObject.toJSONString(deptSet));
                // saiquList 做乱序处理
                Collections.shuffle(saiquList);

                // 1. 只取deptSet 里面匹配的门类的专家
                for (String saiqu : saiquList) {
                    // 不要和教育学和产教融合已经选择的数据，赛区重复
                    if(saiquChoolsed.contains(saiqu)){
                        continue;
                    }
                    List<Zhuanjia> zhuanjiaListTmp2 = saiquMap.get(saiqu);
                    if (!CollectionUtils.isEmpty(zhuanjiaListTmp2)) {
                        // 从赛区专家里面，选择专家出来,选出来的专家需要从原集合里面剔除掉
                        Collections.shuffle(zhuanjiaListTmp2);
                        for(Zhuanjia zhuanjia : zhuanjiaListTmp2) {
                            if (zhuanjiaSetTop.contains(zhuanjia)) {
                                continue;
                            }

                            // 优先选择dept 在deptSet里面的专家
                            if (zhuanjia.getDept() != null && deptSet.contains(zhuanjia.getDept())) {
                                zhuanjiaSetTop.add(zhuanjia);
                                zTmp.add(zhuanjia);
                                deptSet.remove(zhuanjia.getDept()); // 匹配一个剔除一个
                                break;
                            }

                            if (zTmp.size() >= chooseNum) {
                                break;
                            }
//                            zhuanjiaSetTop.add(zhuanjia);
//                            zTmp.add(zhuanjia);
//                            break;
                        }
                    }
                    if (zTmp.size() >= chooseNum) {
                        break;
                    }
                }
                //  2. deptSet 处理完之后，还没匹配到17. 从saiquList 里面再取一次。
                for (String saiqu : saiquList) {
                    // 不要和教育学和产教融合已经选择的数据，赛区重复
                    if(saiquChoolsed.contains(saiqu)){
                        continue;
                    }
                    List<Zhuanjia> zhuanjiaListTmp2 = saiquMap.get(saiqu);
                    if (!CollectionUtils.isEmpty(zhuanjiaListTmp2)) {
                        // 从赛区专家里面，选择专家出来,选出来的专家需要从原集合里面剔除掉
                        Collections.shuffle(zhuanjiaListTmp2);
                        for(Zhuanjia zhuanjia : zhuanjiaListTmp2) {
                            if (zhuanjiaSetTop.contains(zhuanjia)) {
                                continue;
                            }

                            if (zTmp.size() >= chooseNum) {
                                break;
                            }
                            zhuanjiaSetTop.add(zhuanjia);
                            zTmp.add(zhuanjia);
                            break;
                        }
                    }
                    if (zTmp.size() >= chooseNum) {
                        break;
                    }
                }

                // 3.如果最后处理完，专家数量不足17个，从第二推荐里面随机挑选
                if (zTmp.size() < chooseNum) {
                    List<Zhuanjia> listZhuanjiaTmp2 = zhuanjiaService.getZhuanjiaListDetj(fenzuTmp.substring(0,fenzuTmp.length()-2));
                    Collections.shuffle(listZhuanjiaTmp2);
                    for (Zhuanjia zhuanjia : listZhuanjiaTmp2) {
                        if (zhuanjiaSetTop.contains(zhuanjia)) {
                            continue;
                        }
                        if (zTmp.size() >= chooseNum) {
                            break;
                            }
                        if (!zTmp.contains(zhuanjia)) {
                            zTmp.add(zhuanjia);
                            zhuanjiaSetTop.add(zhuanjia);
                        }
                    }
                }

                // 输出完成后，打印
//                System.out.println(fenzuTmp);
                for (Zhuanjia zhuanjia : zTmp) {
                    String xingm = zhuanjia.getXingm() != null ? zhuanjia.getXingm() : "";
                    String phone = zhuanjia.getPhone() != null ? zhuanjia.getPhone() : "";
                    String saiqu = zhuanjia.getSaiqu() != null ? zhuanjia.getSaiqu() : "";
                    String gzdw = zhuanjia.getGzdw() != null ? zhuanjia.getGzdw() : "";
                    String dept = zhuanjia.getDept() != null ? zhuanjia.getDept() : "";
                    String dytj = zhuanjia.getDytj() != null ? zhuanjia.getDytj() : "";
                    String detj = zhuanjia.getDetj() != null ? zhuanjia.getDetj() : "";
                    String zyfx = zhuanjia.getZyfx() != null ? zhuanjia.getZyfx() : "";
                    String zjly = zhuanjia.getZjly() != null ? zhuanjia.getZjly() : "";
//                    System.out.println(xingm+","+phone+","+saiqu+","+gzdw+","+dept+","+dytj+","+detj+","+zyfx+","+zjly);
                    // 整理输出内容，可以直接贴入excel表格中，每个单元格一个字段显示的格式
                    Map<String,String> mtmp = new HashMap<>();
                    mtmp.put("1",fenzuTmp);
                    mtmp.put("2",xingm);
                    mtmp.put("3",phone);
                    mtmp.put("4",saiqu);
                    mtmp.put("5",gzdw);
                    mtmp.put("6",dept);
                    mtmp.put("7",dytj);
                    mtmp.put("8",detj);
                    mtmp.put("9",zyfx);
                    mtmp.put("10",zjly);
                    dataList.add(mtmp);
                    System.out.println(String.format("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s", fenzuTmp, xingm, phone, saiqu, gzdw, dept, dytj, detj, zyfx, zjly));
                }
            }
        }
        if(StringUtils.isBlank(fenzuInput)){
            fenzuInput = "all";
        }
        String fileName = String.format("expert_%s.xlsx",fenzuInput);
        OutputStream os = null;
        try {
            response.reset();
            response.setContentType("application/force-download");// 设置强制下载不打开
            response.addHeader("Content-Disposition",
                    "attachment;fileName=" + fileName);// 设置文件名

            List<Map<String, String>> headList = new ArrayList();
            String sheetName = "专家分配";

            // "评审分组", "姓名", "手机号", "赛区", "工作单位", "所属学科门类", "第一推荐", "第二推荐", "专业方向", "专家来源"
            headList.add(new HashMap() {{
                put("key", "1");
                put("name", "评审分组");
            }});

            headList.add(new HashMap() {{
                put("key", "2");
                put("name", "姓名");
            }});
            headList.add(new HashMap() {{
                put("key", "3");
                put("name", "手机号");
            }});
            headList.add(new HashMap() {{
                put("key", "4");
                put("name", "赛区");
            }});

            headList.add(new HashMap() {{
                put("key", "5");
                put("name", "工作单位");
            }});

            headList.add(new HashMap() {{
                put("key", "6");
                put("name", "所属学科门类");
            }});
            headList.add(new HashMap() {{
                put("key", "7");
                put("name", "第一推荐");
            }});
            headList.add(new HashMap() {{
                put("key", "8");
                put("name", "第二推荐");
            }});
            headList.add(new HashMap() {{
                put("key", "9");
                put("name", "专业方向");
            }});
            headList.add(new HashMap() {{
                put("key", "10");
                put("name", "专家来源");
            }});

            SXSSFWorkbook xss = ExcelUtil.writeExcel(headList, dataList, sheetName);

            os = response.getOutputStream();
            xss.write(os);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                }
            }
        }
    }


    public static void main(String[] args) throws ParseException {
//        System.out.println(DateUtils.string2Date("2020-05-25" + " 16:01:01", "yyyy-MM-dd").getTime());
        Long time = System.currentTimeMillis();
        String text = "创建一个校园图书馆大厦外部近景图片，图书馆大厦是现代建筑风格，约莫有5层楼高，侧面有学生进出，具有学术氛围。天空晴朗，整体环境宁静和谐，体现出学习与自然共存的美好画面。";
        String size = "1280*720";
        String style = "";
//        String text = "北京著名景点";
        String thisSign = DigestUtils.sha256Hex(text + size + style + Constants.COOKIE_KEY_SIGN2 + time);
        System.out.println(time);
        System.out.println(thisSign);
    }


}
