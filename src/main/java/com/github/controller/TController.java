package com.github.controller;

import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesis;
import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesisParam;
import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesisResult;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson.JSONObject;
import com.github.model.Zhuanjia;
import com.github.model.Zuoping;
import com.github.service.ZhuanjiaService;
import com.github.service.ZuopingService;
import com.github.util.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @version v1.0
 * @author: leolin
 * @since: 12/05/2020 5:49 PM
 * @description :类描述
 */
@Controller
@RequestMapping("t")
public class TController {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private UuidUtils uuidUtils;
    @Resource
    private RestTemplateBuilder restTemplateBuilder;

    @Resource private ZuopingService zuopingService;
    @Resource private ZhuanjiaService zhuanjiaService;

    Logger logger = LoggerFactory.getLogger(TController.class);

    public void dealZhuanjia() {
        List<Zuoping> fenzuListAll = zuopingService.getFenzuList();
        List<Zuoping> saiquListAll = zuopingService.getSaiquList();

        // 根据分组信息，查询到不重复的分组  分组需要剔除掉最后2位
        List<String> fenzuList = fenzuListAll.stream().map(Zuoping::getFenzu2).distinct().collect(Collectors.toList());
        List<String> saiquList = fenzuListAll.stream().map(Zuoping::getSaiqu).distinct().collect(Collectors.toList());


        // 根据分组，查询到专家信息
        for (String fenzu : fenzuList) {
            List<Zhuanjia> zhuanjiaList = zhuanjiaService.getZhuanjiaListDytj(fenzu);

            // 假定第一推荐下的用户足够
            // 根据fenzu 从fenzuListAll 当中，找到以fenzu开头的所有分组列表
            List<Zuoping> zuopingList = fenzuListAll.stream().filter(zuoping -> zuoping.getFenzu().startsWith(fenzu)).collect(Collectors.toList());
            // 当前分组 查到的分组1组，分组2组等数据
            List<String> fenzuTmpList = zuopingList.stream().map(Zuoping::getFenzu).distinct().collect(Collectors.toList());

            // 根据作品信息的分组，查询出来所有的作品
            List<Zuoping> listZuopingTmp = zuopingService.getListByFenzu(fenzu);

            // 比较专家列表和作品列表里面的工作单位数据，有相同的工作单位的专家做剔除处理
            List<Zhuanjia> listZhuanjiaTmp = new ArrayList<>();
            for (Zhuanjia zhuanjia : zhuanjiaList) {
                boolean match = false;
                for (Zuoping zuoping : listZuopingTmp) {
                    if (zhuanjia.getGzdw().equals(zuoping.getGzdw())) {
                        match=true;
                        break;
                    }
                }
                if(!match) {
                    listZhuanjiaTmp.add(zhuanjia);
                }
            }

            // 从专家列表里面，选出dept字段包含"教育学"的专家
            List<Zhuanjia> listZhuanjiaTmpJiaoyu = listZhuanjiaTmp.stream().filter(zhuanjia -> zhuanjia.getDept().contains("教育学")).collect(Collectors.toList());

            // listZhuanjiaTmp 里面剔除掉 listZhuanjiaTmp2 的专家
            listZhuanjiaTmp.removeAll(listZhuanjiaTmpJiaoyu);

            // 得到的专家信息里面，按照赛区进行分组，然后各赛区的数据保存到一个list当中
            Map<String, List<Zhuanjia>> saiquMap = listZhuanjiaTmp.stream().collect(Collectors.groupingBy(Zhuanjia::getSaiqu));

            // 从 fenzuTmpList 拿出fenzu信息，然后从各个赛区轮流选择17个专家出来，这17个专家里面必须有一个dept字段包含"教育学"的专家，且选出来的专家将从专家库listZhuanjiaTmp中移除
            for (int i=0; i<fenzuTmpList.size(); i++) {
                String fenzuTmp = fenzuTmpList.get(i);

                List<Zhuanjia> zTmp = new ArrayList<>();
                zTmp.add(listZhuanjiaTmpJiaoyu.get(listZhuanjiaTmpJiaoyu.size() - (i+1)));

                // saiquList 做乱序处理
                Collections.shuffle(saiquList);
                for (String saiqu : saiquList) {
                    List<Zhuanjia> zhuanjiaListTmp2 = saiquMap.get(saiqu);
                    if (!CollectionUtils.isEmpty(zhuanjiaListTmp2)) {
                        for (Zhuanjia zhuanjia : zhuanjiaListTmp2) {
                            if (zTmp.size() >= 17) {
                                break;
                            }
                            // 如果是产教融合
                            if (fenzuTmp.contains("产教融合")) {

                            }else {
                                zTmp.add(zhuanjia);
                            }
                        }
                    }
                    if (zTmp.size() >= 17) {
                        break;
                    }
                }

                // 输出完成后，打印
                System.out.print(fenzuTmp);
                for (Zhuanjia zhuanjia : zTmp) {
                    System.out.println(zhuanjia.getXingm()+","+zhuanjia.getSaiqu()+","+zhuanjia.getDept());
                }
            }


            // 根据专家信息，查询到专家的详细信息
        }

    }


    public static void main(String[] args) throws ParseException {
//        System.out.println(DateUtils.string2Date("2020-05-25" + " 16:01:01", "yyyy-MM-dd").getTime());
        Long time = System.currentTimeMillis();
        String text = "创建一个校园图书馆大厦外部近景图片，图书馆大厦是现代建筑风格，约莫有5层楼高，侧面有学生进出，具有学术氛围。天空晴朗，整体环境宁静和谐，体现出学习与自然共存的美好画面。";
        String size = "1280*720";
        String style = "";
//        String text = "北京著名景点";
        String thisSign = DigestUtils.sha256Hex(text + size + style + Constants.COOKIE_KEY_SIGN2 + time);
        System.out.println(time);
        System.out.println(thisSign);
    }


}
