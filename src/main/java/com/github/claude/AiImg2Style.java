package com.github.claude;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.util.TemplateConstants;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

/**
 * @className Main
 * @Description TODO  claude 功能测试
 * <AUTHOR>
 * @Date 2025/4/29 16:48
 * @Version 1.0
 **/
@Service
public class AiImg2Style {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    // 2233.ai 中转的key，非claude原始key，防止国内服务器直接调用被封号
    public static final String KEY = "sk-8xfc6eaa6033ca1f8438e41593f5b892966ee9da7d2xY80h";

    // 读取本地图片，转换成base64格式
    public static String getBase64Image(String imgPath) {
        byte[] data = null;
        // 读取图片字节数组
        try {
            InputStream in = new FileInputStream(imgPath);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Base64.getEncoder().encodeToString(data);
    }

    public static void main(String[] args) {
//        String base64Image = getBase64Image("/Users/<USER>/java/wkspace/ai-ali-llm4code/imgs/69ce461f-3792-4f11-8007-c95fa6cafe36.png");
//        String base64Image = getBase64Image("/Users/<USER>/java/wkspace/ai-ali-llm4code/imgs/1152d566-14c3-4969-96a3-9a7679f21d68.png");
//        String base64Image = getBase64Image("/Users/<USER>/java/wkspace/ai-ali-llm4code/imgs/a089a67672c916932bdcb2dc2f2710d6.png");
//        String base64Image = getBase64Image("/Users/<USER>/java/wkspace/ai-ali-llm4code/imgs/f592948c40521684405f684ed38ed866.png");
        String base64Image = getBase64Image("/Users/<USER>/java/wkspace/ai-ali-llm4code/imgs/29e2c909-fc72-4781-b981-baf64271aba3.png");
        String imgDesc = "";
//        String imgDesc = "模块名称为场馆活动，已网状结构显示图片，上面部分显示封面以及当前活动的状态标签，下半部分活动标题，开始时间-结束时间，活动地址\n" +
//                "数据条目至少3条信息，附件上的小icon也需要表达显示 ";
//        String imgDesc = "模块为行为评价，上面的图表使用echart4表示,数据内容参考图片使用纯静态数据，下面的列表数中，显示学科名称（等同于标题字段）有颜色样式的显示，是否交作业的状态，显示时间。\n" +
//                "数据条目至少5条信息 ";
//        String imgDesc = "模块为课程安排，以列表布局，每条数据显示时间，课程名称，教师，上课地点，同一行里面（时间字段的显示无背景，其他字段显示都有背相同的背景色），且课程名称颜色有随机性区别；顶部周一至周五当做分类处理\n" +
//                "数据条目至少5条信息 ";
//        String imgDesc = "模块以网格布局，每条数据其中头像，名称，职称放到当前内容块的上半部分且居中，简介放下半部分； \n" +
//                "数据条目至少3个教师信息 ，有左右切换的轮播滚动效果\n";

        Long start = System.currentTimeMillis();
        // 样式编号
        Integer indexNo = 100007;
        try {
            String result = new AiImg2Style().createStyle(base64Image, imgDesc, indexNo);
            System.out.println(result);
            // 拆分result ```html 开始到```结束范围的都是样式html代码
            String resultHtml = result.substring(result.indexOf("```html") + 7);
            resultHtml = resultHtml.substring(0, resultHtml.indexOf("```"));
            // ```sql 开始到``` 结束的是待执行的sql语句
            String resultSql = result.substring(result.indexOf("```sql") + 6);
            resultSql = resultSql.substring(0, resultSql.indexOf("```"));

            System.out.println(resultHtml);
            System.out.println(resultSql);

            // 问题描述
            // sql执行的时候，需要先执行插入样式，拿到样式的ID字段，再把ID字符赋值到动态字段记录，执行动态字段的添加。
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("耗时：" + (System.currentTimeMillis() - start));
    }

    public String createStyle(String base64Image, String imgDesc, Integer indexNo) throws Exception {
        URL url = new URL("https://api.gptsapi.net/v1/messages");
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestProperty("x-api-key", KEY);
        connection.setRequestProperty("anthropic-version", "2023-06-01");
        connection.setRequestProperty("content-type", "application/json; charset=UTF-8");
        connection.setRequestMethod("POST");
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        connection.setDoOutput(true);

        JSONObject reqBody = new JSONObject();
        // 使用最新模型
//        reqBody.put("model", "claude-3-7-sonnet-20250219");
        reqBody.put("model", "claude-sonnet-4-20250514");
        reqBody.put("max_tokens", 12000);
        reqBody.put("stream", true);
        JSONArray jsonArray = new JSONArray();

        JSONObject userRequest = new JSONObject();
        userRequest.put("role", "user");
        JSONArray contents = new JSONArray();

        // 优先添加图片
        JSONObject img = new JSONObject();
        img.put("type", "image");
        JSONObject source = new JSONObject();
        source.put("type", "base64");
        source.put("media_type", "image/jpeg");
        source.put("data", base64Image);
        img.put("source", source);
        contents.add(img);


        // 添加文本描述和要求的提示词及模板
        /**
         * 关键布局信息不能缺少，否则图片识别的有问题！！！！！！
         * "模块为名师工作室模块，以网格布局，每条数据显示教师头像、名称和职称，其中头像，名称，职称放到当前内容块的上半部分且居中，简介放下半部分； \n" +
         *                 "数据条目至少3个教师信息 ，整个模块显示内容部分可以左右轮动切换显示更多教师信息\n" +
         *                 "- 每个老师显示基本信息：姓名、职称，简介 \n" +
         */
        JSONObject text = new JSONObject();
        if(StringUtils.isEmpty(imgDesc)){
            imgDesc = StringUtils.EMPTY;
        }
        logger.info("imgDesc:{}", imgDesc);
        text.put("text", "你是一名专家级前端工程师，请使用html+css+js+thymeleaf3 编写一个网站首页html当中的一个局部模块的html代码，要求如下 \n" +
                // 描述为空的时候，使用默认描述进行图片生成，不为空则直接使用描述
                (StringUtils.isBlank(imgDesc)?
                "请参考上传的附件图片，识别其中的模块名称，元素位置及相对布局信息，字段信息，颜色信息，每行展示几条数据，展示几行,有无轮播等\n" +
                "- 本模块提供\"更多\"链接跳转至列表页。\n" : imgDesc + "\n") +

                "** 生成样式代码编号为 indexNo=" + indexNo + ",\n" +
                "** 最外层div的class 命名 eng-graphic-style+indexNo,如果有tab切换，tab切换的class统一class为eng-tabs，\n" +
                "** js对象的命名规范：graphicTemp+indexNo，\n" +
                "** 如果有轮播效果，请使用swiper2的代码写法，同时如果有左右箭头切换数据，该左右箭头需要正确显示和有点击切换的效果,swiper-slide class加上 box-sizing:border-box 属性\n" +
                "** 如果有封面图，封面图片要加上图片裁剪，加上class js-crop-img 比如：\n" +
                "<div class=\"img-box js-crop-img\"> <img src=\"g-banner.png\" alt=\"\"> </div> .img-box 的css要加上overflow: hidden\n" +
                "** 标题、更多、以及tab切换的地方，需要按照图片附件效果实现样式。\n" +
                "** 模块标题的html编写需要使用th:utext进行渲染html代码方式\n" +
                "** 如果样式有表格，请使用tr td方式进行编写\n" +
                "** 最后需要加上\n" +
                "function engineInitCallback(elem) {\n" +
                "        graphicTempX.init(elem);\n" +
                "        return graphicTempX;\n" +
                "    }的回调方法 \n" +
                "** 兼容性要求是至少兼容IE9及以上版本\n" +
                "** 需要有移动端适配,在移动端情况下非图标，一条数据尽量占一行\n" +
                "** 数据需要跳转到详情页的时候，最好使用示例代码中的detail方法 动态绑定click事件的方式 \n"+
//                "** 需要用到图片的地方可以是19-25数字编号+.jpg\n" +
                "** 分类对象的取值顺序如下：\n" +
                "name 分类名称,alias 分类别称简介，url 分类链接\n" +
                "** 数据对象数据取值顺序如下：\n" +
                "0 图片，1 标题，2 子标题，3 作者，4简介说明，5 数字内容，6发布时间 10移动端封面  100开始自定义字段\n" +
                "如果参考样式附件图片，发现有不满足的字段，需要用到101开始的字段进行扩充。sql中flag和sequence的值保持一致，且都往后加1的，如果是满足的字段则不用新生成sql\n" +
                "按照如下格式生成替换indexNo的sql，\n" +
                //这条sql不需要了  样式已经提前预生成  后续直接更新即可
                //"INSERT INTO `engine`.`t_engine_style`(`id`,`name`, `img_url`, `code`, `engine_type`, `type`, `wfwfid`, `order`, `status`) VALUES (indexNo, '样式+indexNo', '/upload/engine/template/graphic/template_+indexNo+.jpg', indexNo, 'aipic', 0, 0, indexNo, 1);\n" +
                "有字段不满足的情况下，需要扩充字段，需要生成添加字段sql。\n" +
                "INSERT INTO `engine`.`t_extra_general_field`(`style`, `type`, `name`, `description`, `sequence`, `data_type`, `flag`, `style_id`) VALUES (indexNo, 'aipic', 'field0', '借用方式', 100, 'text', 100, indexNo);\n" +
                "把最终生成的sql 输出\n" +
                "** 最终只输出html代码和sql语句，其他内容不做输出，html用 ```html，``` 包裹，sql用 ```sql,``` 包裹\n"+
                "** 特别注意一：全局不要使用flex布局\n" +
                "** 特别注意二：整个模块的高度和宽度不要固定写死，需要自适应高度宽度，且最大高度不超过100vh\n" +
                "** 特别注意三：如果附件图片上的数据只显示一行，请控制数据的展示不会出现第二行\n" +
                "已有实现的基础代码如附件，请在此风格上进行扩写和完善图片对应的样式和布局，thymeleaf3模版渲染的时候，循环及子循环里面都需要进行改写，数据字段填充的时候需要做判空处理，防止thymeleaf渲染报空指针异常；代码示例如下：" + TemplateConstants.content);
        text.put("type", "text");
        contents.add(text);

        userRequest.put("content", contents);
        jsonArray.add(userRequest);
        reqBody.put("messages", jsonArray);

        String requestBody = reqBody.toJSONString();
//        System.out.println("Request Body: " + requestBody); // 打印请求体以便调试
        try (OutputStream outputStream = connection.getOutputStream()) {
            outputStream.write(requestBody.getBytes("UTF-8"));
            outputStream.flush();
        }

        int responseCode = connection.getResponseCode();
        StringBuilder responseBody = new StringBuilder();
        if (responseCode == 200) {
            System.out.println("Successfully connected to Ali SSE API");
            try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                String inputLine;
                while ((inputLine = in.readLine()) != null) {
                    if (StringUtils.isNotEmpty(inputLine) && inputLine.startsWith("data:")) {
                        // 接收到结果后再分发本次结果
                        String value = inputLine.substring(5);
                        JSONObject jresult = JSONObject.parseObject(value);
                        JSONObject delta = jresult.getJSONObject("delta");
                        if (null != delta && !delta.isEmpty()) {
                            String responseText = delta.getString("text");
                            responseBody.append(responseText);
                        }
                    }
                }
                // 添加结束标记 ，这里有滞后性， 会导致输出顺序错乱
            }
        } else {
            System.out.println("Failed to connect to Ali SSE API, response code: " + responseCode);
        }
        return responseBody.toString();
    }

}
