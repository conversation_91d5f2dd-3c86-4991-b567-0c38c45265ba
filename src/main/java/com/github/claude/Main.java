package com.github.claude;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.model.TChatCompletion;
import com.github.util.TemplateConstants;
import org.apache.commons.lang.StringUtils;
import sun.misc.BASE64Encoder;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * @className Main
 * @Description TODO  claude 功能测试
 * <AUTHOR>
 * @Date 2025/4/29 16:48
 * @Version 1.0
 **/
public class Main {
    // 2233.ai 中转的key，非claude原始key，防止国内服务器直接调用被封号
    public static final String KEY = "sk-8xfc6eaa6033ca1f8438e41593f5b892966ee9da7d2xY80h";

    // 读取本地图片，转换成base64格式
    public static String getBase64Image(String imgPath) {
        byte[] data = null;
        // 读取图片字节数组
        try {
            InputStream in = new FileInputStream(imgPath);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 对字节数组Base64编码
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(data);
    }

    public static void main(String[] args) throws Exception {
        URL url = new URL("https://api.gptsapi.net/v1/messages");
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
        connection.setRequestProperty("x-api-key", KEY);
        connection.setRequestProperty("anthropic-version", "2023-06-01");
        connection.setRequestProperty("Accept-Charset", "UTF-8");
        connection.setDoOutput(true);

        String base64Image = getBase64Image("/Users/<USER>/java/wkspace/ai-ali-sse-proxy/imgs/5f60d926-05e6-427c-9ce2-834b764115dc.png");

        JSONObject reqBody = new JSONObject();
        reqBody.put("model", "claude-3-7-sonnet-20250219");
        reqBody.put("max_tokens", "10240");
        JSONArray jsonArray = new JSONArray();

        JSONObject userRequest = new JSONObject();
        userRequest.put("role", "user");
        JSONArray contents = new JSONArray();

        JSONObject img = new JSONObject();
        img.put("type", "image");
        img.put("source", "{\n" +
                "              \"type\": \"base64\",\n" +
                "              \"media_type\": \"image/jpeg\",\n" +
                "              \"data\": \""+base64Image+"\"\n" +
                "            }");

        JSONObject text  = new JSONObject();
        text.put("content", "使用html+css+js+thymeleaf3 编写一个网站首页html当中的一个局部模块的html代码，要求如下 \n" +
                "模块为名师工作室模块，以网格布局，每条数据显示教师头像、名称和职称，其中头像，名称，职称放到当前内容块的上半部分且居中，简介放下半部分； \n" +
                "数据条目至少3个教师信息 ，整个模块显示内容部分可以左右轮动切换显示更多教师信息\n" +
                "- 每个老师显示基本信息：姓名、职称，简介 \n" +
                "- 本模块提供\"更多\"链接跳转至列表页。\n" +
                "更详细要求请参考上传的附件图片\n" +
                "** 生成样式代码编号为 indexNo=928\n" +
                "** 最外层div的class 命名 eng-graphic-style+indexNo,如果有tab切换，tab切换的class统一class为eng-tabs，\n" +
                "** js对象的命名规范：graphicTemp+indexNo，\n" +
                "** 如果有轮播效果，请使用swiper，swiper版本用的swiper2，\n" +
                "** 如果有封面图，封面图片要加上图片裁剪，加上class js-crop-img 比如：\n" +
                "<div class=\"img-box js-crop-img\"> <img src=\"g-banner.png\" alt=\"\"> </div> .img-box 的css要加上overflow: hidden\n" +
                "** 标题、更多、以及tab切换的地方，需要按照图片附件效果实现样式。\n" +
                "** 最后需要加上\n" +
                "function engineInitCallback(elem) {\n" +
                "        graphicTempX.init(elem);\n" +
                "        return graphicTempX;\n" +
                "    }的回调方法 \n" +
                "** 兼容性要求是至少兼容IE9及以上版本\n" +
                "** 需要有移动端适配\n" +
                "** 需要用到图片的地方可以是19-25数字编号+.jpg\n" +
                "** 对象数据取值顺序如下：\n" +
                "0 图片，1 标题，2 子标题，3 作者，4简介说明，5 数字内容，6发布时间 10移动端封面  100开始自定义字段\n" +
                "如果参考样式附件图片，发现有不满足的字段，需要用到101开始的字段进行扩充。\n" +
                "按照如下格式生成替换indexNo的sql，\n" +
                "INSERT INTO `engine`.`t_engine_style`(`name`, `img_url`, `code`, `engine_type`, `type`, `wfwfid`, `order`, `status`) VALUES ('样式+indexNo', '/upload/engine/template/graphic/template_+indexNo+.jpg', indexNo, 'graphic', 0, 0, indexNo, 1);\n" +
                "有字段不满足的情况下，需要扩充字段，需要生成添加字段sql。\n" +
                "INSERT INTO `engine`.`t_extra_general_field`(`style`, `type`, `name`, `description`, `sequence`, `data_type`, `flag`) VALUES (indexNo, 'graphic', 'field0', '借用方式', 100, 'text', 100);\n" +
                "把最终生成的sql输出\n" +
                "已有实现的基础代码如附件，请在此风格上进行扩写和完善图片对应的样式和布局，示例代码如下：\n" + TemplateConstants.content);
        text.put("type","text");
        contents.add(text);

        reqBody.put("messages", jsonArray);

        String requestBody = reqBody.toJSONString();

        try (OutputStream outputStream = connection.getOutputStream()) {
            outputStream.write(requestBody.getBytes("UTF-8"));
            outputStream.flush();
        }

        int responseCode = connection.getResponseCode();
        if (responseCode == 200) {
            try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                String inputLine;
                String value = "";
                while ((inputLine = in.readLine()) != null) {
                    System.out.println(value);
                    if (StringUtils.isNotEmpty(inputLine) && inputLine.startsWith("data:")) {
                        // 接收到结果后再分发本次结果
//                        value = inputLine.substring(5);
                    }
                }
                // 添加结束标记 ，这里有滞后性， 会导致输出顺序错乱
            }
        } else {
            System.out.println("Failed to connect to Ali SSE API, response code: " + responseCode);
        }
    }

}
