package com.github.claude;

/**
 * @className TestController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/8 11:41
 * @Version 1.0
 **/
public class TestStrSplit {
    public static void main(String[] args) {
        String result = "\n" +
                "Here's my implementation for the requested module:\n" +
                "\n" +
                "```html\n" +
                "<style>\n" +
                "    /*图文 样式927 */\n" +
                "    .eng-graphic-style927 {\n" +
                "        padding: 20px 0;\n" +
                "        background-color: #f1f7ff;\n" +
                "        position: relative;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .item-engine-title {\n" +
                "        display: flex;\n" +
                "        justify-content: space-between;\n" +
                "        align-items: center;\n" +
                "        margin-bottom: 30px;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .title-t {\n" +
                "        font-size: 24px;\n" +
                "        font-weight: bold;\n" +
                "        color: #123876;\n" +
                "        position: relative;\n" +
                "        padding-bottom: 10px;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .title-t:after {\n" +
                "        content: \"\";\n" +
                "        position: absolute;\n" +
                "        bottom: 0;\n" +
                "        left: 50%;\n" +
                "        transform: translateX(-50%);\n" +
                "        width: 40px;\n" +
                "        height: 3px;\n" +
                "        background-color: #f5a623;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .more {\n" +
                "        color: #123876;\n" +
                "        font-size: 14px;\n" +
                "        display: flex;\n" +
                "        align-items: center;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .more i {\n" +
                "        margin-left: 5px;\n" +
                "        transform: rotate(90deg);\n" +
                "    }\n" +
                "    .eng-graphic-style927 .eng-tabs-info {\n" +
                "        position: relative;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .swiper-container {\n" +
                "        padding: 0 30px;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .swiper-slide {\n" +
                "        background: #fff;\n" +
                "        border-radius: 8px;\n" +
                "        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n" +
                "        overflow: hidden;\n" +
                "        height: 350px;\n" +
                "        display: flex;\n" +
                "        flex-direction: column;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .teacher-header {\n" +
                "        padding: 20px;\n" +
                "        text-align: center;\n" +
                "        border-bottom: 1px solid #eaeaea;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .avatar-box {\n" +
                "        width: 80px;\n" +
                "        height: 80px;\n" +
                "        border-radius: 50%;\n" +
                "        overflow: hidden;\n" +
                "        margin: 0 auto 10px;\n" +
                "        border: 2px solid #3D82F2;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .avatar-box img {\n" +
                "        width: 100%;\n" +
                "        height: 100%;\n" +
                "        object-fit: cover;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .teacher-name {\n" +
                "        font-size: 18px;\n" +
                "        font-weight: bold;\n" +
                "        color: #333;\n" +
                "        margin-bottom: 5px;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .teacher-title {\n" +
                "        font-size: 14px;\n" +
                "        color: #666;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .teacher-intro {\n" +
                "        padding: 20px;\n" +
                "        font-size: 14px;\n" +
                "        color: #666;\n" +
                "        line-height: 1.6;\n" +
                "        flex: 1;\n" +
                "        overflow: hidden;\n" +
                "        text-overflow: ellipsis;\n" +
                "        display: -webkit-box;\n" +
                "        -webkit-line-clamp: 7;\n" +
                "        -webkit-box-orient: vertical;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .swiper-button-prev,\n" +
                "    .eng-graphic-style927 .swiper-button-next {\n" +
                "        width: 40px;\n" +
                "        height: 40px;\n" +
                "        border-radius: 50%;\n" +
                "        background-color: #fff;\n" +
                "        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n" +
                "        color: #3D82F2;\n" +
                "        display: flex;\n" +
                "        align-items: center;\n" +
                "        justify-content: center;\n" +
                "        position: absolute;\n" +
                "        top: 50%;\n" +
                "        transform: translateY(-50%);\n" +
                "        z-index: 10;\n" +
                "        cursor: pointer;\n" +
                "        transition: all 0.3s;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .swiper-button-prev {\n" +
                "        left: -5px;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .swiper-button-next {\n" +
                "        right: -5px;\n" +
                "    }\n" +
                "    .eng-graphic-style927 .swiper-button-prev:hover,\n" +
                "    .eng-graphic-style927 .swiper-button-next:hover {\n" +
                "        background-color: #3D82F2;\n" +
                "        color: #fff;\n" +
                "    }\n" +
                "    \n" +
                "    /*布局适配*/\n" +
                "    .layout-content2_1_2 .eng-graphic-style927 .swiper-slide {\n" +
                "        width: 280px;\n" +
                "    }\n" +
                "    .layout-content3_1_3 .eng-graphic-style927 .swiper-slide,\n" +
                "    .layout-content2_1_3 .eng-graphic-style927 .swiper-slide {\n" +
                "        width: calc(100% - 32px);\n" +
                "    }\n" +
                "    .layout-content2_2_3 .eng-graphic-style927 .swiper-slide {\n" +
                "        width: 300px;\n" +
                "    }\n" +
                "    \n" +
                "    @media only screen and (min-width: 1px) and (max-width: 926px) {\n" +
                "        .eng-graphic-style927 .title-t {\n" +
                "            font-size: 20px;\n" +
                "        }\n" +
                "        .eng-graphic-style927 .swiper-container {\n" +
                "            padding: 0 20px;\n" +
                "        }\n" +
                "        .eng-graphic-style927 .swiper-slide {\n" +
                "            width: 100% !important;\n" +
                "            height: auto;\n" +
                "            min-height: 320px;\n" +
                "        }\n" +
                "        .eng-graphic-style927 .avatar-box {\n" +
                "            width: 60px;\n" +
                "            height: 60px;\n" +
                "        }\n" +
                "        .eng-graphic-style927 .teacher-name {\n" +
                "            font-size: 16px;\n" +
                "        }\n" +
                "        .eng-graphic-style927 .teacher-title {\n" +
                "            font-size: 12px;\n" +
                "        }\n" +
                "        .eng-graphic-style927 .teacher-intro {\n" +
                "            padding: 15px;\n" +
                "            font-size: 13px;\n" +
                "            -webkit-line-clamp: 6;\n" +
                "        }\n" +
                "    }\n" +
                "</style>\n" +
                "\n" +
                "<th:block th:if=\"${isConfigType && !isMobile}\">\n" +
                "    <style>\n" +
                "        [(|${typeStyle}|)]\n" +
                "    </style>\n" +
                "</th:block>\n" +
                "<th:block th:if=\"${isConfigType && isMobile}\">\n" +
                "    <style>\n" +
                "        @media only screen and (min-width: 1px) and (max-width: 926px) {\n" +
                "            [(|${typeStyle}|)]\n" +
                "        }\n" +
                "    </style>\n" +
                "</th:block>\n" +
                "\n" +
                "<div class=\"eng-graphic-style927\" th:classappend=\"|style-${milliSecond}|\" th:id=\"|graphic-template-927-${milliSecond}|\">\n" +
                "    <div class=\"item-engine-title clear\" th:if=\"${engineInstance.header || engineInstance.more}\">\n" +
                "        <span class=\"title-t\" th:utext=\"${engineInstance.name}\" th:if=\"${engineInstance.header}\">名师工作室</span>\n" +
                "        <a th:data-href=\"${engineInstance.moreUrl}\" th:id=\"|more-${milliSecond}|\" th:classappend=\"${moreStyleType=='1'?'more-type-text':(moreStyleType=='2'?'more-type-image':'')}\"\n" +
                "           th:styleappend=\"${style1}\" th:if=\"${engineInstance.more}\"\n" +
                "           class=\"more fr\" target=\"_blank\">\n" +
                "            <span th:styleappend=\"${style2}\" class=\"mcgMhLan\" th:if=\"${moreStyleType=='1' || moreStyleType=='0'}\">\n" +
                "                [[${#strings.isEmpty(engineInstance.moreTitle)} ? '更多 ' : ${engineInstance.moreTitle}]]\n" +
                "            </span>\n" +
                "            <span class=\"mcgMhLan\" th:unless=\"${moreStyleType=='1' || moreStyleType=='0' || moreStyleType=='2'}\">更多 →</span>\n" +
                "            <img th:styleappend=\"${style3}\" th:src=\"${moreImgUrl}\" alt=\"\" th:if=\"${moreStyleType=='2'}\">\n" +
                "            <i th:styleappend=\"${style3}\" th:classappend=\"${class}\" th:if=\"${(moreStyleType=='1' && moreShowIcon) || moreStyleType=='0'}\" class=\"icon-up\"></i>\n" +
                "        </a>\n" +
                "    </div>\n" +
                "    \n" +
                "    <div class=\"muchimgs-wrap\">\n" +
                "        <div class=\"eng-null-data\" th:data-id=\"|no-data-${milliSecond}|\"\n" +
                "             th:hidden=\"${datas != null && #lists.size(datas) > 0}\">\n" +
                "            <img th:src=\"@{/assets/images/style/engine/img_null_data.png}\">\n" +
                "            <p class=\"tip\" th:text=\"${emptyStr}\">暂无数据</p>\n" +
                "        </div>\n" +
                "        \n" +
                "        <div class=\"eng-tabs-info clear\"\n" +
                "             th:style=\"|color: ${(engineInstance.fontColor == null || #strings.isEmpty(engineInstance.fontColor)) ? '#333333' : engineInstance.fontColor}|\">\n" +
                "            <div class=\"swiper-container teacher-carousel\"\n" +
                "                 th:classappend=\"|style-${milliSecond}|\" th:id=\"|graphic-swiper-927-${milliSecond}|\"\n" +
                "                 th:style=\"${engineInstance.backgroudType==0}?(${engineInstance.backgroudColor!=null and !#strings.isEmpty(engineInstance.backgroudColor)}?'background: '+${engineInstance.backgroudColor}+'':'background: transparent'):'background: url('+${engineInstance.backgroudImgUrl}+') no-repeat;background-size: 100% 100%;'\">\n" +
                "                <div class=\"swiper-wrapper\">\n" +
                "                    <div class=\"swiper-slide\" th:each=\"data, iterStat : ${datas}\">\n" +
                "                        <div class=\"teacher-header\">\n" +
                "                            <div class=\"avatar-box js-crop-img\" th:if=\"${data['0'] != null}\">\n" +
                "                                <img th:src=\"${data['0'].value}\"\n" +
                "                                     onerror=\"this.onerror=null;this.src='/engine2/assets/images/on_error.png'\">\n" +
                "                            </div>\n" +
                "                            <h3 class=\"teacher-name\" th:if=\"${data['1'] != null}\" th:text=\"${data['1'].value}\" th:classappend=\"${#strings.isEmpty(engineInstance.dataTitleFontStyle)}?'':('titlefs'+${engineInstance.dataTitleFontStyle})\">胡海燕</h3>\n" +
                "                            <p class=\"teacher-title\" th:if=\"${data['2'] != null}\" th:text=\"${data['2'].value}\">胡海燕名师工作室</p>\n" +
                "                        </div>\n" +
                "                        <div class=\"teacher-intro\" th:if=\"${data['4'] != null}\" th:utext=\"${data['4'].value}\">\n" +
                "                            胡海燕初中英语名师工作室是庆阳市教育局命名的市级工作室。胡海燕同志担任工作室领衔人。工作室现有成员16名，其中基地校（庆化学校）6名成员...\n" +
                "                        </div>\n" +
                "                    </div>\n" +
                "                </div>\n" +
                "                <div class=\"swiper-button-prev\">←</div>\n" +
                "                <div class=\"swiper-button-next\">→</div>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "</div>\n" +
                "\n" +
                "<script th:inline=\"javascript\">\n" +
                "    var graphicTemp927 = {\n" +
                "        data: {\n" +
                "            openTarget : [[${openTarget}]],\n" +
                "            ctx: [[${ctx}]],\n" +
                "            appId: [[${appId}]],\n" +
                "            engineInstance: [[${engineInstance}]],\n" +
                "            engineExternalId: [[${engineExternalId}]],\n" +
                "            wfwfid: [[${wfwfid}]],\n" +
                "            milliSecond: [[${milliSecond}]],\n" +
                "            types: [[${types}]],\n" +
                "            datas: [],\n" +
                "            selectedTypeIndex: 0,\n" +
                "            swiperObj: null,\n" +
                "            ele: \"\"\n" +
                "        },\n" +
                "        init: function (ele) {\n" +
                "            var $this = this;\n" +
                "            $this.data.ele = ele;\n" +
                "            \n" +
                "            $('body').on('click', '#more-' + $this.data.milliSecond, function () {\n" +
                "                var url = $(\"#more-\" + $this.data.milliSecond).attr(\"data-href\");\n" +
                "                if (!$this.isEmpty(url)) {\n" +
                "                    mhTUrlOrMore(url, $this.data.openTarget);\n" +
                "                }\n" +
                "            });\n" +
                "            \n" +
                "            if ($this.isEmpty($this.data.types)) {\n" +
                "                $this.data.types = [];\n" +
                "                $this.data.types.push({});\n" +
                "            }\n" +
                "            \n" +
                "            $($this.data.types).each(function (i) {\n" +
                "                if (i == 0) {\n" +
                "                    this.dataLoaded = true;\n" +
                "                    this.datas = [[${datas}]];\n" +
                "                    $this.data.datas = this.datas;\n" +
                "                } else {\n" +
                "                    this.dataLoaded = false;\n" +
                "                    this.datas = [];\n" +
                "                }\n" +
                "            });\n" +
                "            \n" +
                "            $this.initSwiper(ele);\n" +
                "            $this.initGridEvents();\n" +
                "        },\n" +
                "        getData: function (index, dom) {\n" +
                "            var $this = this;\n" +
                "            var type = $this.data.types[index];\n" +
                "            if ($this.isEmpty(type)) {\n" +
                "                return [];\n" +
                "            }\n" +
                "            if (type.dataLoaded) {\n" +
                "                return type.datas;\n" +
                "            }\n" +
                "            $this.loadData(index, dom);\n" +
                "            return null;\n" +
                "        },\n" +
                "        loadData: function (index, dom) {\n" +
                "            var $this = this;\n" +
                "            var type = $this.data.types[index];\n" +
                "            if ($this.isEmpty(type.engineInstanceId)) {\n" +
                "                type.engineInstanceId = $this.data.engineInstance.id;\n" +
                "            }\n" +
                "            var url = \"/engine2/general/\" + $this.data.appId + \"/type/datas?engineInstanceId=\" + $this.data.engineInstance.id + \"&typeId=\" + type.id;\n" +
                "            $.get(url, function (data) {\n" +
                "                if (data.code == 1) {\n" +
                "                    data = data.data;\n" +
                "                    var typeId = data.typeId;\n" +
                "                    var datas = data.datas.datas;\n" +
                "                    var typeIndex = null;\n" +
                "                    $($this.data.types).each(function (i) {\n" +
                "                        if (this.id == typeId) {\n" +
                "                            typeIndex = i;\n" +
                "                            return false;\n" +
                "                        }\n" +
                "                    });\n" +
                "                    if (typeIndex != null) {\n" +
                "                        $this.data.types[typeIndex].dataLoaded = true;\n" +
                "                        $this.data.types[typeIndex].datas = datas;\n" +
                "                        if ($this.data.selectedTypeIndex == typeIndex) {\n" +
                "                            $this.data.datas = datas;\n" +
                "                            // 渲染\n" +
                "                            $this.replaceSlides($this.generateSlides(datas), dom);\n" +
                "                        }\n" +
                "                    }\n" +
                "                } else {\n" +
                "                    var errorMessage = data.message;\n" +
                "                    if ($this.isEmpty(errorMessage)) {\n" +
                "                        errorMessage = \"加载分类:\" + type.id + \"下数据失败\";\n" +
                "                    }\n" +
                "                    console.log(errorMessage);\n" +
                "                }\n" +
                "            }).fail(function () {\n" +
                "                console.log(\"加载分类:\" + type.id + \"下数据失败\");\n" +
                "            });\n" +
                "        },\n" +
                "        initSwiper: function () {\n" +
                "            var $this = this;\n" +
                "            var swiperContainerId = \"#graphic-swiper-927-\" + $this.data.milliSecond;\n" +
                "            \n" +
                "            var slidesPerView = 3;\n" +
                "            if(isMobileDevice()) {\n" +
                "                slidesPerView = 1;\n" +
                "            }\n" +
                "            \n" +
                "            $this.data.swiperObj = new Swiper(swiperContainerId, {\n" +
                "                slidesPerView: slidesPerView,\n" +
                "                spaceBetween: 20,\n" +
                "                loop: true,\n" +
                "                autoplay: $this.data.engineInstance.switchSpeed == null ? 3000 : $this.data.engineInstance.switchSpeed * 1000,\n" +
                "                watchActiveIndex: true,\n" +
                "                autoplayDisableOnInteraction: false,\n" +
                "                calculateHeight: isMobileDevice() ? true : false,\n" +
                "                observer: true,\n" +
                "                observeParents: true,\n" +
                "                onSlideClick: function (swiper) {\n" +
                "                    // 详情\n" +
                "                    var index = Math.abs(swiper.activeLoopIndex);\n" +
                "                    $($this.data.datas).each(function (i) {\n" +
                "                        if (i == index) {\n" +
                "                            $this.detail(this);\n" +
                "                        }\n" +
                "                    });\n" +
                "                }\n" +
                "            });\n" +
                "            \n" +
                "            $(swiperContainerId + ' .swiper-button-prev').on('click', function(e){\n" +
                "                e.preventDefault();\n" +
                "                $this.data.swiperObj.swipePrev();\n" +
                "            });\n" +
                "            \n" +
                "            $(swiperContainerId + ' .swiper-button-next').on('click', function(e){\n" +
                "                e.preventDefault();\n" +
                "                $this.data.swiperObj.swipeNext();\n" +
                "            });\n" +
                "            \n" +
                "            cropImgFun($this.data.ele);\n" +
                "        },\n" +
                "        destroySwiper: function () {\n" +
                "            var curSlide = 0;\n" +
                "            if (this.data.swiperObj) {\n" +
                "                this.data.swiperObj.destroy(true);\n" +
                "            }\n" +
                "            this.initSwiper();\n" +
                "            if (this.data.swiperObj) {\n" +
                "                this.data.swiperObj.swipeTo(curSlide, 100, false);\n" +
                "            }\n" +
                "        },\n" +
                "        initGridEvents: function () {\n" +
                "            var $this = this;\n" +
                "            //拖拽后\n" +
                "            $('.grid-stack').off('gsresizestop').on('gsresizestop', function (event, elem) {\n" +
                "                if (!$this.isEmpty($this.data.swiperObj)) {\n" +
                "                    var slide = $this.data.swiperObj.activeLoopIndex;\n" +
                "                    $this.data.swiperObj.reInit();\n" +
                "                    $this.data.swiperObj.swipeTo(slide, 100, false);\n" +
                "                }\n" +
                "            });\n" +
                "        },\n" +
                "        generateSlides: function (datas) {\n" +
                "            var $this = this;\n" +
                "            var html = \"\";\n" +
                "            if (datas == null || datas.length < 1) {\n" +
                "                // 显示无数据\n" +
                "                $(\"div[data-id='no-data-\" + $this.data.milliSecond + \"']\").hide();\n" +
                "                $(\"div[data-id='no-data-\" + $this.data.milliSecond + \"']\").show();\n" +
                "            } else {\n" +
                "                $(\"div[data-id='no-data-\" + $this.data.milliSecond + \"']\").hide();\n" +
                "            }\n" +
                "            $(datas).each(function () {\n" +
                "                html += '<div class=\"swiper-slide\">';\n" +
                "                html += '<div class=\"teacher-header\">';\n" +
                "                \n" +
                "                if (!$this.isEmpty(this['0']) && !$this.isEmpty(this['0'].value)) {\n" +
                "                    html += '<div class=\"avatar-box js-crop-img\">' +\n" +
                "                        '<img src=\"' + this['0'].value + '\" onerror=\"this.onerror=null;this.src=\\'/engine2/assets/images/on_error.png\\'\">' +\n" +
                "                        '</div>';\n" +
                "                }\n" +
                "                \n" +
                "                if (!$this.isEmpty(this['1'])) {\n" +
                "                    html += '<h3 class=\"teacher-name '+($this.data.engineInstance.dataTitleFontStyle==null?'':('titlefs'+$this.data.engineInstance.dataTitleFontStyle))+'\">' + this['1'].value + '</h3>';\n" +
                "                }\n" +
                "                \n" +
                "                if (!$this.isEmpty(this['2'])) {\n" +
                "                    html += '<p class=\"teacher-title\">' + this['2'].value + '</p>';\n" +
                "                }\n" +
                "                \n" +
                "                html += '</div>';\n" +
                "                \n" +
                "                if (!$this.isEmpty(this['4'])) {\n" +
                "                    html += '<div class=\"teacher-intro\">' + this['4'].value + '</div>';\n" +
                "                }\n" +
                "                \n" +
                "                html += '</div>';\n" +
                "            });\n" +
                "            return html;\n" +
                "        },\n" +
                "        replaceSlides: function (html, ele) {\n" +
                "            $(ele).find('.swiper-wrapper').html(html);\n" +
                "            this.destroySwiper();\n" +
                "            cropImgFun(ele);\n" +
                "        },\n" +
                "        detail: function (data) {\n" +
                "            var $this = this;\n" +
                "            if (!$this.isEmpty(data)) {\n" +
                "                if (typeof (dataPopupWindowPreHandle) == 'undefined' || !dataPopupWindowPreHandle($this.data.engineInstance, data)) {\n" +
                "                    var url = data.url;\n" +
                "                    if (!$this.isEmpty(url)) {\n" +
                "                        mhDetail(url, [[${engineInstance.urlType}]])\n" +
                "                    }\n" +
                "                }\n" +
                "            }\n" +
                "        },\n" +
                "        isEmpty: function (str) {\n" +
                "            return typeof(str) == 'undefined' || str == null || \"\" === $.trim(str);\n" +
                "        },\n" +
                "        replaceUrlParam: function (url, param, value) {\n" +
                "            var $this = this;\n" +
                "            if (!$this.isEmpty(url)) {\n" +
                "                var re = new RegExp(\"[?&](\" + param + \"=[^&]*)\", \"gi\");\n" +
                "                var replaceStr = param + \"=\" + value;\n" +
                "                return re.test(url) ? url.replace(RegExp.$1, replaceStr) : url.indexOf('?') > -1 ? url + \"&\" + replaceStr : url + \"?\" + replaceStr;\n" +
                "            } else {\n" +
                "                return \"\";\n" +
                "            }\n" +
                "        }\n" +
                "    };\n" +
                "    \n" +
                "    function engineInitCallback(elem) {\n" +
                "        graphicTemp927.init(elem);\n" +
                "        return graphicTemp927;\n" +
                "    }\n" +
                "</script>\n" +
                "```\n" +
                "\n" +
                "And here's the SQL for the style:\n" +
                "\n" +
                "```sql\n" +
                "INSERT INTO `engine`.`t_engine_style`(`name`, `img_url`, `code`, `engine_type`, `type`, `wfwfid`, `order`, `status`) VALUES ('样式927', '/upload/engine/template/graphic/template_927.jpg', 927, 'aipic', 0, 0, 927, 1);\n" +
                "\n" +
                "-- 需要添加子标题字段，用于存储教师职称信息\n" +
                "INSERT INTO `engine`.`t_extra_general_field`(`style`, `type`, `name`, `description`, `sequence`, `data_type`, `flag`) VALUES (927, 'aipic', 'subTitle', '职称', 101, 'text', 100);\n" +
                "```\n" +
                "\n" +
                "This implementation creates a responsive carousel that:";
        String resultHtml = result.substring(result.indexOf("```html") + 7);
        resultHtml = resultHtml.substring(0, resultHtml.indexOf("```"));
        // ```sql 开始到``` 结束的是待执行的sql语句
        String resultSql = result.substring(result.indexOf("```sql") + 6);
        resultSql = resultSql.substring(0, resultSql.indexOf("```"));
        System.out.println(resultHtml);
        System.out.println(resultSql);
    }

}
