package com.github.claude;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

/**
 * @className Main
 * @Description TODO  claude 功能测试
 * <AUTHOR>
 * @Date 2025/4/29 16:48
 * @Version 1.0
 **/
public class AiImg2Style2 {
    // 2233.ai 中转的key，非claude原始key，防止国内服务器直接调用被封号
    public static final String KEY = "sk-8xfc6eaa6033ca1f8438e41593f5b892966ee9da7d2xY80h";

    // 读取本地图片，转换成base64格式
    public static String getBase64Image(String imgPath) {
        byte[] data = null;
        // 读取图片字节数组
        try {
            InputStream in = new FileInputStream(imgPath);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Base64.getEncoder().encodeToString(data);
    }

    public static void main(String[] args) {
        String base64Image = getBase64Image("/Users/<USER>/Desktop/1.png");
        String imgDesc = "左侧是一个iframe内容块，后续会动态替换内容，右侧上方是一个ai聊天框，支持多轮聊天，右侧下方支持输入内容，右侧最下方有三个按钮，一键润色，参考替换图，开始生成";
        Long start = System.currentTimeMillis();
        try {
            String result = createHtml(base64Image, imgDesc);
            // 拆分result ```html 开始到```结束范围的都是样式html代码
            String resultHtml = result.substring(result.indexOf("```html") + 7);
            resultHtml = resultHtml.substring(0, resultHtml.indexOf("```"));
            System.out.println(resultHtml);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("耗时：" + (System.currentTimeMillis() - start));
    }

    public static String createHtml(String base64Image, String imgDesc) throws Exception {
        URL url = new URL("https://api.gptsapi.net/v1/messages");
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestProperty("x-api-key", KEY);
        connection.setRequestProperty("anthropic-version", "2023-06-01");
        connection.setRequestProperty("content-type", "application/json; charset=UTF-8");
        connection.setRequestMethod("POST");
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        connection.setDoOutput(true);

        JSONObject reqBody = new JSONObject();
        reqBody.put("model", "claude-3-7-sonnet-20250219");
        reqBody.put("max_tokens", 12000);
        reqBody.put("stream", true);
        JSONArray jsonArray = new JSONArray();

        JSONObject userRequest = new JSONObject();
        userRequest.put("role", "user");
        JSONArray contents = new JSONArray();

        // 优先添加图片
        JSONObject img = new JSONObject();
        img.put("type", "image");
        JSONObject source = new JSONObject();
        source.put("type", "base64");
        source.put("media_type", "image/jpeg");
        source.put("data", base64Image);
        img.put("source", source);
        contents.add(img);


        // 添加文本描述和要求的提示词及模板
        JSONObject text = new JSONObject();
        text.put("text", "你是一名专家级前端工程师，请使用html+css+vue,  编写一个完整的html页面代码，要求如下 \n" +
                "请参考上传的附件图片，识别其中的位置布局信息，文字信息，颜色信息等\n" +
                imgDesc + "\n" +
                "** 整个页面的高度和宽度不要固定写死，需要自适应高度宽度\n"+
                "** 兼容性要求是至少兼容IE9及以上版本\n" +
                "** 需要有移动端适配\n"
        );
        text.put("type", "text");
        contents.add(text);

        userRequest.put("content", contents);
        jsonArray.add(userRequest);
        reqBody.put("messages", jsonArray);

        String requestBody = reqBody.toJSONString();
//        System.out.println("Request Body: " + requestBody); // 打印请求体以便调试
        try (OutputStream outputStream = connection.getOutputStream()) {
            outputStream.write(requestBody.getBytes("UTF-8"));
            outputStream.flush();
        }

        int responseCode = connection.getResponseCode();
        StringBuilder responseBody = new StringBuilder();
        if (responseCode == 200) {
            System.out.println("Successfully connected to Ali SSE API");
            try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                String inputLine;
                while ((inputLine = in.readLine()) != null) {
                    if (StringUtils.isNotEmpty(inputLine) && inputLine.startsWith("data:")) {
                        // 接收到结果后再分发本次结果
                        String value = inputLine.substring(5);
                        JSONObject jresult = JSONObject.parseObject(value);
                        JSONObject delta = jresult.getJSONObject("delta");
                        if (null != delta && !delta.isEmpty()) {
                            String responseText = delta.getString("text");
                            responseBody.append(responseText);
                        }
                    }
                }
                // 添加结束标记 ，这里有滞后性， 会导致输出顺序错乱
            }
        } else {
            System.out.println("Failed to connect to Ali SSE API, response code: " + responseCode);
        }
        return responseBody.toString();
    }

}
