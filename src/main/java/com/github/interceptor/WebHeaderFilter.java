package com.github.interceptor;

import com.github.util.JsonUtils;
import com.github.util.JsoupUtil;
import com.github.util.SpringContextHolder;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ReadListener;
import javax.servlet.ServletException;
import javax.servlet.ServletInputStream;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @version v1.0
 * @author: leolin
 * @since: 08/12/2020 4:00 PM
 * @description :类描述
 */
@Component
@WebFilter
public class WebHeaderFilter extends OncePerRequestFilter {
    String[] excludes = {"/upload/*","/admin/*", "/assets/*", "/webjson/*","/v2/webjson/*","/v2/add-module-enc","/v2/add-module","/assets-dy/*", "/favicon.ico","/page/(\\d+)/show",
            "/page/(\\w+).html","/page/(\\w+)/(\\w+).html","/web-others/*","/beginner-guide/updateStatus","/sadmin/save-website-plugs"};

    private boolean dealExcludes(HttpServletRequest request) {
        if (excludes.length == 0) {
            return false;
        } else {
            String url = request.getServletPath();
            boolean match = false;
            for (String s : excludes) {
                Matcher m;
                Pattern p = Pattern.compile("^" + s);
                m = p.matcher(url);
                if (m.find()) {
                    match = true;
                    break;
                }
            }
            return match;
        }
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        // SpringBootAdmin 镜像环境不予开放
        if(SpringContextHolder.isMirror() && request.getRequestURI().contains("/actuator")){
            request.getRequestDispatcher("/global/403").forward(request, response);
        }
        // 非镜像环境下。 控制请求的ip。只能是那台服务器才能使用。
        if(! SpringContextHolder.isMirror() && request.getRequestURI().contains("/actuator")){
            request.getRequestDispatcher("/global/403").forward(request, response);
        }

        if (! dealExcludes(request)) {
            ModifyHttpServletRequestWrapper mParametersWrapper = new ModifyHttpServletRequestWrapper(request);
            filterChain.doFilter(mParametersWrapper, response);
        } else {
            filterChain.doFilter(request, response);
        }
    }

    private class ModifyHttpServletRequestWrapper extends HttpServletRequestWrapper {
        ModifyHttpServletRequestWrapper(HttpServletRequest request) {
            super(request);
        }

        @Override
        public String getHeader(String name) {
            String superHeader = super.getHeader(name);
            superHeader = JsoupUtil.clean(superHeader);
            return superHeader;
        }

        @Override
        public Cookie[] getCookies() {
            Cookie[] cookies = super.getCookies();
            if (null == cookies) {
                return cookies;
            }
            Arrays.stream(cookies).forEach(cookie -> {
                cookie.setValue(JsoupUtil.clean(cookie.getValue()));
            });

            return cookies;
        }

        @Override
        public String[] getParameterValues(String parameter) {
            String[] values = super.getParameterValues(parameter);
            if(values == null) {
                return null;
            } else {
                int count = values.length;
                String[] encodedValues = new String[count];

                for(int i = 0; i < count; ++i) {
                    String value = values[i];
                    if (StringUtils.isNotBlank(value)) {
                        if (JsonUtils.isJSON(value)) {
                            value = JsoupUtil.cleanV1WebJsonContent(value);
                        } else {
                            value = JsoupUtil.clean(value);
                        }
                        encodedValues[i] = value;
                    }
                }

                return encodedValues;
            }
        }

        @Override
        public String getParameter(String parameter) {
            String value = super.getParameter(parameter);
            if (StringUtils.isNotBlank(value)) {
                if (JsonUtils.isJSON(value)) {
                    value = JsoupUtil.cleanV1WebJsonContent(value);
                } else {
                    value = JsoupUtil.clean(value);
                }
            }
            return value;
        }


        @Override
        public String getQueryString() {
            String value = super.getQueryString();
            if (StringUtils.isNotBlank(value)) {
                if (JsonUtils.isJSON(value)) {
                    value = JsoupUtil.cleanV1WebJsonContent(value);
                } else {
                    value = JsoupUtil.clean(value);
                }
            }

            return value;
        }

        @Override
        public ServletInputStream getInputStream() throws IOException {
            byte[] bytes = this.inputHandlers(super.getInputStream()).getBytes("UTF-8");
            final ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
            return new ServletInputStream() {
                @Override
                public int read() throws IOException {
                    return bais.read();
                }
                @Override
                public boolean isFinished() {
                    return false;
                }
                @Override
                public boolean isReady() {
                    return false;
                }
                @Override
                public void setReadListener(ReadListener readListener) {
                }
            };
        }

        public String inputHandlers(ServletInputStream servletInputStream) {
            StringBuilder sb = new StringBuilder();
            BufferedReader reader = null;

            String jstr;
            try {
                reader = new BufferedReader(new InputStreamReader(servletInputStream, Charset.forName("UTF-8")));
                jstr = "";

                while((jstr = reader.readLine()) != null) {
                    sb.append(jstr);
                }
            } catch (IOException var17) {
                var17.printStackTrace();
            } finally {
                if(servletInputStream != null) {
                    try {
                        servletInputStream.close();
                    } catch (IOException var16) {
                        var16.printStackTrace();
                    }
                }

                if(reader != null) {
                    try {
                        reader.close();
                    } catch (IOException var15) {
                        var15.printStackTrace();
                    }
                }
            }
            jstr = JsoupUtil.cleanV1WebJsonContent(sb.toString());
            return jstr;
        }
    }

//    public static void main(String[] args) {
//        String[] excludes = {"/upload/*", "/webjson/*" , "/assets/*","/assets-dy/*", "/favicon.ico","/page/(\\w+).html","/page/(\\w+)/(\\w+).html"};
//        String url = "v2/webjson/update";
//        if (excludes.length == 0) {
//
//        } else {
//            boolean match = false;
//            for (String s : excludes) {
//                Matcher m;
//                Pattern p = Pattern.compile("^" + s);
//                m = p.matcher(url);
//                if (m.find()) {
//                    match = true;
//                    break;
//                }
//            }
//            System.out.println(match);
//        }
//
//        String s1 = "11486;alert(1);<script>alert(222)</script>//\\";
//        System.out.println(JsoupUtil.clean(s1));
//    }
}
