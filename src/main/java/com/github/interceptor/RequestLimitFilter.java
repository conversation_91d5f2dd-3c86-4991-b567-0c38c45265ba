package com.github.interceptor;

import com.github.util.ResponseUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * 添加限流设置
 * http://portal.chaoxing.com/_perlimit?status=&uris=/website/,/assets/&per=1
 * http://portal.chaoxing.com/_perlimit?status=&uris=/page/&per=0   -- 禁止uris访问
 * http://portal.chaoxing.com/_perlimit?status=&uris=/page/&per=-1   -- 删除uris访问限制
 */
@Component
@WebFilter
public class RequestLimitFilter implements Filter{

	int timesPerSeconds = 800;

	volatile AtomicInteger threads = new AtomicInteger(0);

	Set<String> readUriSet = new HashSet<>();

	@Override
	public void init(FilterConfig config) throws ServletException {
		try {
			timesPerSeconds = Integer.parseInt(config.getInitParameter("timesPerSeconds"));
		} catch (Exception e2) {
			System.err.println("timesPerSeconds set error, use default "+timesPerSeconds);
		}

		String readUris = config.getInitParameter("readUris");
		String[] readUriArr = readUris==null?new String[0]:readUris.split(",");
		for (String readUri : readUriArr) {
			readUriSet.add(readUri.trim());
		}

		// 限流
		ObjectInputStream ois = null;
		FileInputStream  fis = null;
		try {
			String path = "";
			URL url = RequestLimitFilter.class.getClassLoader().getResource("");
			if(null == url){
				path = System.getProperty("user.dir")+File.separatorChar+"perlimit";
			}else{
				path = url.getPath()+"perlimit";
			}
			File f = new File(path);
			if(!f.exists()) {
				f.createNewFile();
			}
			fis =new FileInputStream(f);
			ois  = new ObjectInputStream(fis);
			uriPers = (Map<String, Integer>)ois.readObject();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		} finally {
			try {
				fis.close();
			} catch (IOException e1) {
			}
			try {
				ois.close();
			} catch (Exception e) {
			}
		}
	}

	static Map<String, Integer> uriPers = new HashMap<>();

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		try {
			HttpServletRequest req = (HttpServletRequest)request;
			HttpServletResponse resp = (HttpServletResponse)response;
			String uri = req.getRequestURI();
			if(uri.contains("monitor")) {
				chain.doFilter(request, response);
				return;
			}
			// 设置限流
			if(uri.startsWith("/_perlimit_acc_p_l")) {
				String status = request.getParameter("status");
				if(StringUtils.isNotBlank(status)) {
					resp.setStatus(HttpServletResponse.SC_OK);
					ResponseUtils.renderHtml(resp, uriPers.toString());
					return ;
				}
				String uris = request.getParameter("uris");
				String[] uriArr = StringUtils.split(uris, ",");
				int per = NumberUtils.toInt(request.getParameter("per"), -1);
				if(per == -1) {
					for (String u : uriArr) {
						uriPers.remove(u);
					}
				}else {
					for (String u : uriArr) {
						uriPers.put(u, per);
					}
				}

				ObjectOutputStream oos = null;
				FileOutputStream fos = null;
				try {
					String path = "";
					URL url = RequestLimitFilter.class.getClassLoader().getResource("");
					if(null == url){
						path = System.getProperty("user.dir")+File.separatorChar+"perlimit";
					}else{
						path = url.getPath()+"perlimit";
					}
					fos = new FileOutputStream(path);
					oos  = new ObjectOutputStream(fos);
					oos.writeObject(uriPers);
				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					try {
						fos.close();
					} catch (IOException e1) {
					}
					try {
						oos.close();
					} catch (IOException e) {
					}
				}

				resp.setStatus(HttpServletResponse.SC_OK);
				resp.getWriter().write("perlimit success");
				return ;
			}

			// 修改限流量
			if(uri.startsWith("/_limit_acc_p_l")) {
				String status = request.getParameter("status");
				if(StringUtils.isNotBlank(status)) {
					resp.setStatus(HttpServletResponse.SC_OK);
					ResponseUtils.renderHtml(resp, "threads="+threads.get());
					return ;
				}
				String ts = request.getParameter("threads");
				if(ts != null) {
					int t = 0;
					try {
						t = Integer.parseInt(ts);
					} catch (Exception e) {
					}
					threads.set(t);
				}

				resp.setStatus(HttpServletResponse.SC_OK);
				resp.getWriter().write("limit success : "+threads.get());
				return ;
			}

			//限流  可以是完整路径，也可以是根据关键字范围限制
			uriPers.keySet().forEach(s->{
				if(uri.contains(s)){
					int per = uriPers.get(s);
					if(per<1) {
						resp.setStatus(HttpServletResponse.SC_OK);
						ResponseUtils.renderHtml(resp, "{\"code\":0,\"errorMsg\":\"\u6B64\u529F\u80FD\u6682\u65F6\u5173\u95ED\uFF0C\u7279\u6B8A\u65F6\u671F\uFF0C\u8BF7\u60A8\u8C05\u89E3\"}");
						return ;
					}
				}
			});

			//超频限流
			try{
				if(threads.incrementAndGet()>timesPerSeconds) {
					resp.setStatus(HttpServletResponse.SC_FOUND);
					ResponseUtils.renderHtml(resp, "{\"code\":0,\"errorMsg\":\"limits"+threads.get()+"\"}");
					return ;
				}

				chain.doFilter(request, response);
			} finally {
				threads.decrementAndGet();
			}

		}finally {

		}
	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	public static Integer getUriPersValue(String uri){
		if(uriPers.keySet().contains(uri)) {
			return uriPers.get(uri);
		}
		return null;
	}

}
