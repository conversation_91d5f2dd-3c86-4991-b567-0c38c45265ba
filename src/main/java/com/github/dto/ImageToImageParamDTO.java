package com.github.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 阿里图生图构造参数
 * <AUTHOR>
 * @version ver 1.0
 * @className ImageToImageParamDTO
 * @description
 * @blame g<PERSON><PERSON><PERSON>
 * @date 2025-03-26 16:09:37
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageToImageParamDTO {
    /** 模型名称。示例值：wanx2.1-imageedit。 **/
    private String model;
    /** 输入的基本信息，如提示词等。 **/
    private Input input;
    private Parameters parameters;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Input{
        /** 提示词，用来描述生成图像中期望包含的元素和视觉特点。支持中英文，长度不超过800个字符，每个汉字/字母占一个字符，超过部分会自动截断 **/
        private String prompt;
        /** 图像编辑功能。枚举见 ImagToImageFunctionEnum **/
        private String function;
        /** 输入图像的URL地址 **/
        private String base_image_url;
        /** 涂抹区域图像地址 仅当function设置为description_edit_with_mask（局部重绘）时生效**/
        private String mask_image_url;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Parameters{
        /** 生成图片的数量。取值范围为1~4张，默认为1 **/
        private Integer n;
        /** 随机数种子，用于控制模型生成内容的随机性。seed参数取值范围是[0, 2147483647]。如果不提供，则算法自动生成一个随机数作为种子。如果您希望生成内容保持相对稳定，请使用相同的seed参数值。 **/
        private Integer seed;
        /** 是否添加水印标识，水印位于图片右下角，文案为“AI生成”。默认false **/
        private Boolean watermark;
        /** 当function设置为super_resolution（图像超分）时才需填写。图像超分的放大倍数。在放大图像的同时增强细节，提升图像分辨率，实现高清处理。取值范围为1~4，默认值为1。
         *  当upscale_factor设置为1时，仅对图像进行高清处理，不进行放大。
         */
        private Integer upscale_factor;
        /** 当function设置为expand（扩图）时才需填写。图像居中，向上按比例扩展图像。默认值为1.0，取值范围[1.0, 2.0]。**/
        private Float top_scale;
        /** 当function设置为expand（扩图）时才需填写。图像居中，向下按比例扩展图像。默认值为1.0，取值范围[1.0, 2.0]。 **/
        private Float bottom_scale;
        /** 当function设置为expand（扩图）时才需填写。图像居中，向左按比例扩展图像。默认值为1.0，取值范围[1.0, 2.0]。**/
        private Float left_scale;
        /** 当function设置为expand（扩图）时才需填写。图像居中，向右按比例扩展图像。默认值为1.0，取值范围[1.0, 2.0]。 **/
        private Float right_scale;

    }
}
