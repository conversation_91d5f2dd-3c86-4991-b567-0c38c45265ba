package com.github.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import java.util.*;

/**
 * 对外提供API统一加密工具
 *
 */
public class ApiEncUtils {

    private static final String KEY = "i[doiw09$@kd[po0";

    /**
     * 根据url参数进行加密
     * 按照参数的字母排序规则，由小到大，进行加密参数封装，
     * 原参数：ab=123&czd=11&af=909&ac=opd&ct=0
     * 拼装结果：ab=123ac=opdaf=909ct=0czd=11（取消&）
     * 然后拿到 拼装字符串 + 当前时间（yyyy-MM-dd）+ Key 然后整个字符串进行md5处理，生成sign
     * 效果：sign = md5(ab=123ac=opdaf=909ct=0czd=11public_key=public_key  + 2020-03-19 +  + secret_key)
     *
     * @param parameterMap
     * @return
     */
    public static String getSign(Map<String, String[]> parameterMap,String secretKey) {
        return DigestUtils.md5Hex(getSignStr(parameterMap,secretKey));
    }

    /**
     * 获取加密前的字符串
     * @param parameterMap
     * @param secretKey
     * @return
     */
    public static String getSignStr(Map<String, String[]> parameterMap,String secretKey){
        // 1、获取url中的参数
        Set<String> keys = parameterMap.keySet();

        TreeMap<String, String> map = new TreeMap<>();
        for (String key : keys) {
            //sign不参与拼接
            if (Objects.equals(key,"sign")) {
                continue;
            }
            // 正常的参数拼接
            map.put(key, parameterMap.get(key)[0]);
        }

        Set<Map.Entry<String, String>> set = map.entrySet();
        Iterator<Map.Entry<String, String>> iterator = set.iterator();
        StringBuilder strBuilder = new StringBuilder();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            strBuilder.append(entry.getKey()).append("=").append(entry.getValue());
        }
        String dateStr = DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd");
        return strBuilder.toString() + dateStr + secretKey;
    }


    /**
     * 检查接口参数
     *
     * @param parameterMap
     * @return
     */
    public static boolean check(Map<String, String[]> parameterMap,String secretKey) {
        if (!parameterMap.containsKey("sign")) {
            return false;
        } else {
            String srcEnc = parameterMap.get("sign")[0];
            String enc = getSign(parameterMap,secretKey);
            if (srcEnc.equals(enc)) {
                return true;
            }
            return false;
        }
    }

    /**
     * 签名测试 构建示例 https://sharewh2.xuexi365.com/share/31d7bbca-7ed3-4526-81ea-a01693af4254?t=3
     * @param args
     */
    public static void main(String[] args) {
        //41d6e39230ac57f6f53dfc7e96a4cdf3
        String url = "http://portal.chaoxing.com/outer/api";

        HashMap<String, String[]> map = new HashMap<>();
        map.put("_d", new String[]{"1698996381527"});
        map.put("publicKey", new String[]{"3bda6ad95f104fc0be7bc560bf99ee73"});
        map.put("uid", new String[]{"48114985"});
        map.put("vc3", new String[]{"L7%2B6HkBdfktkh1yMRUcFNw8iCPLjVJW5tnmgq2ugQRK7xppAdH6fkjuXEIdb8WhxU61GV%2BXmAiKSLKWtMQPaOaaofSJ1lEP%2FY9hRLT%2BsF5hSnoxVBfov03%2B1l73rDljwETRQHvp7l7x3DovVUlP1eRkoFKyGXGlRQVh40jFXtKw%3D02ded77440963bca3e4be9042ce97d95"});
        String target = getSign(map,KEY);
        System.out.println(target);
        System.out.println(target.equals("e489306dffb385539adce0186f428f5b"));

        System.out.println("publicKey: "+UUID.randomUUID().toString().replaceAll("-",""));

        System.out.println(DigestUtils.md5Hex("_d=1698996381527publicKey=3bda6ad95f104fc0be7bc560bf99ee73uid=48114985vc3=L7+6HkBdfktkh1yMRUcFNw8iCPLjVJW5tnmgq2ugQRK7xppAdH6fkjuXEIdb8WhxU61GV+XmAiKSLKWtMQPaOaaofSJ1lEP/Y9hRLT+sF5hSnoxVBfov03+1l73rDljwETRQHvp7l7x3DovVUlP1eRkoFKyGXGlRQVh40jFXtKw=02ded77440963bca3e4be9042ce97d952023-11-031ad054f0af0b99579ede7535e66b91f0"));


        map.put("publicKey", new String[]{"9cde50204c6f11eea50bf3816b77b743"});
        map.put("websiteId", new String[]{"419585"});
        String sign = getSign(map,"8e38c59d4c6f11eea837551e0d0a197b");
        System.out.println(sign);
        String dateStr = DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd");
        String s = DigestUtils.md5Hex("publicKey=9cde50204c6f11eea50bf3816b77b743websiteId=428743" + dateStr + "8e38c59d4c6f11eea837551e0d0a197b");
        System.out.println(s);
        System.out.println(sign.equals(s));

    }

}
