package com.github.util;

import org.apache.commons.lang.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.parser.Parser;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 正则表达式工具
 * <p>
 * <!-- 2013.10.23 10:00, 姚康明, <EMAIL> -->
 */
public class RegexUtil {

    private static Pattern IMG_SOURCE_PATTERN = Pattern.compile("<img.*?\\ssrc=[\"'](.*?)[\"'].*?>", Pattern.CASE_INSENSITIVE);
    private static Pattern A_HREF_PATTERN = Pattern.compile("<a.*?\\shref=[\"']([^\"']*?)(\\.[jpg|jpeg|png|gif|rar|zip|pdf|doc|docx|ppt|pptx|xls|xlsx]+)[\"'].*?>", Pattern.CASE_INSENSITIVE);
    private static Pattern HTML_SPECIAL_CHAR_PATTERN = Pattern.compile("&(.*?);");
    private static Pattern NUM_PATTERN = Pattern.compile("^[-\\+]?[\\d]*$");
    private static Pattern ALL_NUM_PATTERN = Pattern.compile("^[\\d]*$");
    private static Pattern NUM_WORD_PATTERN = Pattern.compile("^[\\w|\\d|\\u4e00-\\u9fa5]*?$");

    /**
     * Pattern的静态化定义
     */
    public static final Pattern NUMBER_PATTERN = Pattern.compile("(\\d+)", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    public static final Pattern APP_ID_PATTERN = Pattern.compile("\"appId\":\"(\\d+)\"", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    public static final Pattern LAYOUT_APP_ID_PATTERN = Pattern.compile("\"layoutModules\":\\[([^\\]]+)\\]", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);


    /**
     * 获得匹配组字符串
     */
    public static String getMatchContent(String regex, CharSequence input) {
        return getMatchContent(regex, 0, input);
    }

    /**
     * 获得匹配组字符串
     */
    public static String getGroup1MatchContent(String regex, CharSequence input) {
        return getMatchContent(regex, 1, input);
    }

    /**
     * 获得匹配组字符串
     */
    public static String getMatchContent(String regex, int group, CharSequence input) {
        String matchContent = null;
        Matcher matcher = matcher(regex, input);
        if (matcher.find() && group <= matcher.groupCount()) {
            matchContent = matcher.group(group);
        } else {
            matchContent = null;
        }
        return matchContent;
    }

    /**
     * 获得第几次匹配到的字符串
     */
    public static String getMatchContentWithTime(String regex, int matchTime, CharSequence input) {
        String matchContent = null;
        Matcher matcher = matcher(regex, input);
        for (int i = 0; i < matchTime; i++) {
            matcher.find();
        }
        matchContent = matcher.group();
        return matchContent;
    }

    /**
     * 域名匹配
     */
    public static String getDomainName(CharSequence input) {
        return getMatchContent("http[s]?://[^/]+/", input);
    }

    /**
     * Ip匹配
     */
    public static String getIp(CharSequence input) {
        return getMatchContent("((25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d)))\\.){3}(25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d)))", input);
    }

    /**
     * 判断是否包含域名
     */
    public static boolean isMatchContainDomainName(CharSequence input) {
        return isMatch("http[s]?://[^/]+/", input);
    }

    /**
     * 启用忽略大小写和DOTALL模式进行匹配
     */
    public static Matcher matcher(String regex, CharSequence input) {
        int flags = Pattern.CASE_INSENSITIVE | Pattern.DOTALL;
        return matcher(regex, input, flags);
    }

    /**
     * 根据Match flags匹配
     */
    public static Matcher matcher(String regex, CharSequence input, int flags) {
        Pattern pattern = Pattern.compile(regex, flags);
        return pattern.matcher(input);
    }

    /**
     * 判断目标字符串是否匹配
     */
    public static boolean isMatch(String regex, CharSequence input) {
        Matcher matcher = matcher(regex, input);
        return matcher.find();
    }

    /**
     * 判断目标字符串是否匹配其中一个正则
     */
    public static boolean isMatch(String[] regexArray, CharSequence input) {
        for (final String regex : regexArray) {
            if (isMatch(regex, input)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否包含汉字
     *
     * @param s
     * @return
     */
    public static boolean checkWord(String s) {
        if (StringUtils.isBlank(s)) {
            return false;
        }
        String regex = "[\\u4e00-\\u9fa5]";
        Matcher matcher = matcher(regex, s);
        return matcher.find();
    }

    /**
     * 清除文本里面的数字和字母
     *
     * @return
     */
    public static String cleanNumAndWord(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        String regex = "[0-9a-zA-Z|/|\\.]";
        String result = matcher(regex, content).replaceAll("");
        return result;
    }

    public static boolean checkNum(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }
        return NUM_PATTERN.matcher(content).matches();
    }

    public static boolean checkAllNum(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }
        return ALL_NUM_PATTERN.matcher(content).matches();
    }

    /**
     * 检查数字和字母
     *
     * @param content
     * @return
     */
    public static boolean checkNumWord(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }
        return NUM_WORD_PATTERN.matcher(content).matches();
    }

    /**
     * 清除文本里面的HTML标签
     * Jsoup.clean(html, Whitelist.none()).replaceAll("[\r|\n|\\s]", "").replaceAll("&nbsp;", "")
     */
    public static String cleanHtml(String html) {
        if (StringUtils.isBlank(html)) {
            return html;
        }
        // 注释首先去掉
        html = matcher("<!--[\\s\\S]+?-->", html).replaceAll("");
        // script的正则表达式
        html = matcher("<script[^>]*?>[\\s\\S]*?<\\/script>", html).replaceAll("");
        // style的正则表达式
        html = matcher("<style[^>]*?>[\\s\\S]*?<\\/style>", html).replaceAll("");
        // HTML标签的正则表达式
        html = matcher("<[^>]+>", html).replaceAll("");
        // 过滤空格, 最后一个是全角空格
        html = matcher("[\r|\n|\\s|　]", html).replaceAll("");
        // 过滤所有的html特殊标签
        html = html.replaceAll("&(.*?);", "");

        return html;
    }

    public static String cleanHtmlTagForContent(String html) {
        if (StringUtils.isBlank(html)) {
            return html;
        }
        // HTML标签的正则表达式
        html = matcher("<[^>]+>", html).replaceAll("");
        return html;
    }


    /**
     * 获取图片标签的src值
     *
     * @param content
     * @return
     */
    public static Set<String> getImageTagSrc(String content) {
        Set<String> imgSrcSet = new HashSet<>();
        if (StringUtils.isNotBlank(content)) {
            Matcher matcher = IMG_SOURCE_PATTERN.matcher(content);
            while (matcher.find()) {
                String src = matcher.group(1);
                imgSrcSet.add(src);
            }
        }
        return imgSrcSet;
    }

    /**
     * 获取A标签为附件的href值
     *
     * @param content
     * @return
     */
    public static Set<String> getATagFileHref(String content) {
        Set<String> aHrefSet = new HashSet<>();
        if (StringUtils.isNotBlank(content)) {
            Matcher matcher = A_HREF_PATTERN.matcher(content);
            while (matcher.find()) {
                String src = matcher.group(1) + matcher.group(2);
                aHrefSet.add(src);
            }
        }
        return aHrefSet;
    }

    public static Set<String> getDownloableFiles(String content) {
        Set<String> imageTagSrc = getImageTagSrc(content);
        Set<String> aTagHref = getATagFileHref(content);
        Set<String> fileUrlSet = new HashSet<>();
        fileUrlSet.addAll(imageTagSrc);
        fileUrlSet.addAll(aTagHref);
        return fileUrlSet;
    }

    public static String cleanPortalDomain(String url) {
        if (StringUtils.isNotBlank(url)) {
            url = url.replaceFirst("^http(s)?:\\/\\/[^\\/]*(portal|mh).chaoxing\\.com", "");
        }
        return url;
    }

    public static boolean isMhDomain(String url) {
        if (StringUtils.isNotBlank(url)) {
            return isMatch("^http(s)?:\\/\\/[^\\/]*(portal|mh).chaoxing\\.com", url) || url.startsWith("/");
        }
        return false;
    }

    /**
     * 获取加密串
     *
     * @param url
     * @return
     */
    public static String getSign(String url) {
        String regex = "&sign=(\\w+)";
        matcher(regex, url);

        String result = getMatchContent(regex, 1, url);
        if (StringUtils.isEmpty(result)) {
            return "";
        } else {
            return result;
        }
    }

    /**
     * 获取CData里的数据
     *
     * @param content
     * @return
     */
    public static String getCData(String content) {
        String regex = "<!\\[CDATA\\[(.*)\\]\\]>";
        String result = getMatchContent(regex, 1, content);
        if (StringUtils.isEmpty(result)) {
            return "";
        } else {
            return result;
        }
    }

    public static Integer getOldEngineTextIdByDetailUrl(String url) {
        Integer id = null;
        if (StringUtils.isBlank(url)) {
            return id;
        }
        String regex = ".*portal.chaoxing.com/engine[2]?/text/(\\d+)/detail.*";
        String result = getMatchContent(regex, 1, url);
        if (StringUtils.isNotEmpty(result)) {
            id = Integer.parseInt(result);
        }
        return id;
    }


    public static Integer getWebsiteId(String query) {
        if (StringUtils.isBlank(query)) {
            return 0;
        }
        String regex = "websiteId=(\\d+)";
        matcher(regex, query);

        String result = getMatchContent(regex, 1, query);
        if (StringUtils.isEmpty(result)) {
            return 0;
        } else {
            return Integer.valueOf(result);
        }
    }


    public static boolean isAccordFile(String path) {

        if (StringUtils.isNotBlank(path) && path.contains("/file/download/")) {
            return true;
        }

//		String regex = ".*([.jpg|.jpeg|.png|.bmp|.gif|.zip|.rar|.mp4|.flv|.avi|.rm|.rmvb|.wmv]$)";
        String regex = ".(jpg|jpeg|png|bmp|gif|zip|rar|mp4|flv|avi|rm|rmvb|wmv|doc|docx|xlsx|7z|epub|txt|log|ppt|xls|pdf|sql|mp3|webm|mpg|wav|wps|mov)$";
        matcher(regex, path);

        String result = getMatchContent(regex, 1, path);
        if (StringUtils.isEmpty(result)) {
            return false;
        } else {
            return true;
        }
    }


    /**
     * pattern匹配出结果
     */
    public static Matcher matcher(Pattern pattern, CharSequence input) {
        return pattern.matcher(input);
    }

    /**
     * 根据url地址获取应用类型
     *
     * @param content
     * @return
     */
    public static Set getWebJsonAppsInteger(String content) {
        Set<Integer> set = new HashSet<>();
        Matcher matcher = RegexUtil.matcher(APP_ID_PATTERN, content);
        while (matcher.find()) {
            String appId = matcher.group(1);
            set.add(Integer.valueOf(appId));
        }
        // 布局中的引用ID
        Matcher matcher2 = RegexUtil.matcher(LAYOUT_APP_ID_PATTERN, content);
        while (matcher2.find()) {
            String layout = matcher2.group(1);
            if (StringUtils.isNotBlank(layout)) {
                String[] ids = layout.split(",");
                for (String s : ids) {
                    s = s.replaceAll("\"", "");
                    if (StringUtils.isNotBlank(s) && !s.equals("null")) {
                        set.add(Integer.valueOf(s));
                    }
                }
            }
        }
        return set;
    }

    /**
     * url参数替换
     *
     * @param url
     * @param name
     * @param value
     * @return
     */
    public static String replaceUrlReg(String url, String name, String value) {
        if (StringUtils.isNotBlank(url) && StringUtils.isNotBlank(value)) {
            url = url.replaceAll("(" + name + "=[^&]*)", name + "=" + value);
        }
        return url;
    }

    /**
     * 替换登录 REFER-URL
     *
     * @param url
     * @param pageId
     * @return
     */
    public static String replace3RdLoginUrlReg(String url, Integer pageId) {
        if (StringUtils.isNotBlank(url) && null != pageId) {
            url = url.replaceAll("(/[\\d]*/mhlogin-refer)", "/" + pageId + "/mhlogin-refer");
        }
        return url;
    }

    public static final Pattern SEARCH_INDEX_STYLE = Pattern.compile("search_index_style=(\\d+)", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

    public static Integer getSearchIndexStyle(String params) {
        Matcher matcher = SEARCH_INDEX_STYLE.matcher(params);
        Integer styleId = 1;
        while (matcher.find()) {
            String style = matcher.group(1);
            styleId = Integer.valueOf(style);
            break;
        }
        return styleId;
    }

    public static final Pattern TYPE_ID_LIST = Pattern.compile("typeIds=(\\d+(\\,\\d+)*)");

    /**
     * 获取typeIdS
     *
     * @param content
     * @return
     */
    public static String getTypeIds(String content) {
        Matcher matcher = TYPE_ID_LIST.matcher(content);
        if (matcher.find()) {
            String result = matcher.group(1);
            return result;
        } else {
            return "";
        }
    }


    /**
     * 根据内容获取图片列表,去重
     *
     * @param content
     * @return
     */
    public static final Pattern CONTENT_IMG_LIST = Pattern.compile("http[-\\d\\w/.:]+(.jpg|.jpeg|.png)");

    public static Set<String> getImgList(String content) {
        Matcher matcher = CONTENT_IMG_LIST.matcher(content);
        Set<String> result = new HashSet<>();
        while (matcher.find()) {
            result.add(matcher.group());
        }
        return result;
    }


    /**
     * 包含函数类方法的时候，直接替换掉括号
     */
    public static final Pattern FUNC_STR = Pattern.compile("confirm|alert|prompt|oncontrolselect|oncopy|oncut|ondataavailable|ondatasetchanged|ondatasetcomplete|ondblclick|ondeactivate|ondrag|ondragend|ondragenter|ondragleave|ondragover|ondragstart|ondrop|onerror=|onerroupdate|onfilterchange|onfinish|onfocus|onfocusin|onfocusout|onhelp|onkeydown|onkeypress|onkeyup|onlayoutcomplete|onload|onlosecapture|onmousedown|onmouseenter|onmouseleave|onmousemove|onmousout|onmouseover|onmouseup|onmousewheel|onmove|onmoveend|onmovestart|onabort|onactivate|onafterprint|onafterupdate|onbefore|onbeforeactivate|onbeforecopy|onbeforecut|onbeforedeactivate|onbeforeeditocus|onbeforepaste|onbeforeprint|onbeforeunload|onbeforeupdate|onblur|onbounce|oncellchange|onchange|onclick|oncontextmenu|onpaste|onpropertychange|onreadystatechange|onreset|onresize|onresizend|onresizestart|onrowenter|onrowexit|onrowsdelete|onrowsinserted|onscroll|onselect|onselectionchange|onselectstart|onstart|onstop|onsubmit|onunload");
    public static final Pattern FUNC_STR2 = Pattern.compile("(window\\.location|\\.location|document\\.cookie|document\\.|window\\.open|window\\.)", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

    public static String rmFunc(String content) {
        Matcher matcher = FUNC_STR.matcher(content);
        if (matcher.find()) {
            content = matcher.replaceAll(" ");
        }
        Matcher matcher2 = FUNC_STR2.matcher(content);
        if (matcher2.find()) {
            content = matcher2.replaceAll(" ");
        }
        return content;
    }


    /**
     * 针对笔记编辑器富文本内容的函数移除,code代码块不处理
     *
     * @param content
     * @return
     */
    public static String rmRichFunc(String content) {
        //替换之前的content文档信息
        Document document1 = Parser.parseBodyFragment(content, "");
        Elements elements1 = document1.getElementsByTag("code");
        Matcher matcher = FUNC_STR.matcher(content);
        if (matcher.find()) {
            content = matcher.replaceAll("");
        }
        Matcher matcher2 = FUNC_STR2.matcher(content);
        if (matcher2.find()) {
            content = matcher2.replaceAll("");
        }
        //替换之后的content文档信息
        Document document2 = Parser.parseBodyFragment(content, "");
        Elements elements2 = document2.getElementsByTag("code");
        int index = 0;
        for (Element element : elements2) {
            //code标签的内容需要还原为替换之前的内容
            element.html(elements1.get(index).html());
            index++;
        }
        content = document2.body().html();
        return content;
    }


    /**
     * 提取HTML标签的属性值
     *
     * @param source  HTML标签内容
     * @param element 标签名称
     * @param attr    标签属性
     * @return
     */
    public static List<String> matchHtmlAttr(String source, String element, String attr) {
        List<String> result = new ArrayList<>();
        String reg = "<" + element + "[^<>]*?\\s" + attr + "=['\"]?(.*?)['\"]?\\s.*?>";
        Pattern pt = Pattern.compile(reg);
        Matcher m = pt.matcher(source);
        while (m.find()) {
            String r = m.group(1);
            result.add(r);
        }
        return result;
    }

    /**
     * 判断是否是链接
     *
     * @param url
     * @return
     */
    public static boolean isLink(String url) {
        if (StringUtils.isNotBlank(url)) {
            return url.indexOf("http://") > 0 || url.indexOf("https://") > 0 || url.indexOf("//") > -1 || url.indexOf("/engine2") > 0;

        }
        return false;
    }

    public static void main(String[] args) {
//		String s = "<p style=\"text-align: center;\"><img src=\"https://p.cldisk.com/star3/origin/b7a9b1ea8411dc05ed8ed92f31aaaad6.png\" width=\"71\" height=\"23\" alt=\"\"> &nbsp;<span style=\"font-size: 28px;\">专题11介绍&nbsp; </span><img src=\"https://p.cldisk.com/star3/origin/c050a49f37c00dee24232a93a9a0195a.png\" width=\"71\" height=\"23\" alt=\"\"><img src=x onerror=alealertrt(00123)><sw src='test'>hahah</sw>";
//		s = rmFunc(s);
//		System.out.println(s);
        String filename = "http://a.com/123.mp4";
        Boolean result = isAccordFile(filename);
        System.out.println(result);

    }
}
