package com.github.util;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 系统常量
 */
public final class Constants {

    public static final String SYSTEM_DOMAIN_MH_TEST = "t0.mh.chaoxing.com";
    public static final String SYSTEM_DOMAIN_MH = "mh.chaoxing.com";
    public static final String SYSTEM_DOMAIN_PORTAL = "portal.chaoxing.com";
    public static final String SYSTEM_DOMAIN_WISWEB = "wisweb.com";
    public static final String SYSTEM_DOMAIN_CHAOXINGMH = "chaoxingmh.com";
    public static final String SYSTEM_DOMAIN_CHAOXINGWEB = "chaoxingweb.com";
    public static final String BRANCH_MIRROR = "mirror"; // 镜像版标识
    public static final String BRANCH_PROD = "prod"; // 正式版标识

    /**
     * 没有权限的提示语
     */
    public static final String NO_PERS = "暂无权限，请联系管理员";

    /**
     * 关闭审核
     */
    public static final Integer STATUS_FALSE = 0;
    /**
     * 普通锁定
     */
    public static final Integer STATUS_TRUE = 1;

    public static final String COOKIE_UID = "UID";

    public static final String COOKIE_WFWFID = "wfwfid";
    public static final String COOKIE_FID = "fid";

    public static final String COOKIE_KEY_SIGN = "~1Ll09l[di%ds]s2F@#B4$M%A";
    public static final String COOKIE_KEY_SIGN2 = "~1LjEgl#B4$M%5K^&4$M%A";
    public static final String COOKIE_KEY_SIGN3 = "~1Ljj_ceYtgBj~8Rh-EM%A";
    public static final String COOKIE_KEY_SIGN4 = "~1Ljd@yIj$mk&6ZXHfrM%A";

    public static final String COOKIE_MHR = "mhr"; //门户数据源角色参数
    public static final String COOKIE_SPACE_R = "spaceRoleId"; //空间角色

    /**
     * 多语言版本cookie标志
     */
    public static final String COOKIE_LANGUAGE = "browserLocale";
    public static final String COOKIE_SUID = "SUID";
    public static final String COOKIE_UUID = "UUID"; //是否在线的唯一标识
    public static final String WORK_ROLE_BENCH_ID = "workRoleBenchId"; //服务工作台参数
    public static final String COOKIE_CURRENT_PAGE_ID = "current_page_id";
    public static final String COOKIE_SPAGE_ID = "spageId";
    public static final String COOKIE_S_SIGN = "s_sign";
    public static final String COOKIE_WEBSITE_ID = "website_id";
    public static final String COOKIE_CURRENT_WEB_FID = "website_fid";
    public static final String COOKIE_CURRENT_WEB_FID_LOGIN = "website_fid_login";

    /**
     * ai检查接口
     */
    public static String AI_CHECK_TEXT_URL = "https://api.gptzero.me/v2/predict/text";
    public static String AI_CHECK_FILE_URL = "https://api.gptzero.me/v2/predict/files";
    public static String AI_CHECK_API_KEY = "32ae0622b6ec4fe4986c95d424f7d119";

    public static Integer MAX_AI_CHECK_TEXT_LENGTH = 600;
    public static Integer MIN_AI_CHECK_TEXT_LENGTH = 400;

    public static final String apiKey = "sk-5a95a3fcf61e4ec49402a03161ec9494";
    public static final Integer EFFECTIVE_TIME = 10 * 60 * 1000;

    /**
     * 阿里 keyId和keySecret
     */
    public static final String ACCESS_KEY_ID = "LTAI5tKGbSefwws7uCVnD7j5";
    public static final String ACCESS_KEY_SECRET = "******************************";

    /**
     * 腾讯ai key
     */
    public static final String TENCENT_API_KEY = "sk-UGjo6tIKxGY9G1NbCSyVoYHVmm5plEHNT11zo7KDf8fowMhd";

    public static AtomicInteger aiCheckCount = new AtomicInteger(0);

    public final static String ERROR_RESPONSE = "你好，这个问题我暂时无法回答，让我们换个话题再聊聊吧。";

    /**
     * 混元大模型，应用接口接入地址
     */
    public static final String HUNYUAN_SSEURL = "https://wss.lke.cloud.tencent.com/v1/qbot/chat/sse"; // SSE 服务器的 URL

    public static String[] GRAYUIDS = new String[]{"22703503", "2774265", "26691608"};

    /**
     * 混元ds接入应用key
     */
    public static final String HUNYUN_R1_ONLINE = "YGdBNEzuNNJLCPPpNYGvfCspBmKcMOFFDsiDAVyplXVqrpGJCJHCbboZLMUfrFErkafNQyNtpinSvnIgSzMwwJGVQTtqRGahpnfzwEbTdtYOBVfCPkLBjhPcqViwhqCf";
    public static final String HUNYUN_R1_OFFLINE = "jkaqeATkCwNCGWRkpQkgDerAXXoTwzwqzFPCItszcfhQwhmTsDkbCpuZkkoEABUdEclfimSnAPJIrcKtsMVJMYbMLYlaKTyKlgNqHwNguRXsJATKkahnyuNSJLzLyXuO";
    public static final String HUNYUN_V3_ONLINE = "JjavbtpCuzSncKUwSeljEWBxYOYVYPWWBcIGqDmfihyNmTXyxsgQhpDeTLOExQzvIQwooSCcslAVRayspxMmPvPTruyGSzhgpMDRBkfIQRTrcnvcnClogjXKqgVsFAWh";
    public static final String HUNYUN_V3_OFFLINE = "dPHTDxSpKgvlTxcLCaPuMkdwXpvERMenWToQbnOcsoNjFDNWbuJzDSjKdfsbipcNpuCTXcFVhsgYknJRqMgZWFASNosqeeiVWsPKXRxyCcOhCvkGuxRdDllMDietJfxG";
    /**
     * 最多建立400个长连接
     */
    public static Integer MAX_REQUEST_LENGTH = 400;

}
