package com.github.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 超盾敏感词检测
 */
@Component
public class SuperShieldCheckUtils {
    private static final Logger logger = LoggerFactory.getLogger(SuperShieldCheckUtils.class);
    /**
     * 产品密钥ID，产品标识
     */
    private final static String SECRET_ID = "Inner_1baab9a835af42988c0c121b7c5601f5";
    /**
     * 产品私有密钥，服务端生成签名信息使用，请严格保管，避免泄露
     */
    private final static String SECRET_KEY = "aa89e721f6da4aa89975dcab32feb374";

    /**
     * 超盾反垃圾云服务文本在线检测接口地址 注：其中async为"async"则代表异步 async为"sync"则代表同步
     */
    private final static String API_TEXT_URL = "https://ai.chaoxing.com/api/v1/text/check/sync";
    @Resource
    private HttpServletRequest request;
    @Resource
    private RestTemplate restTemplate;

    /**
     * 敏感词检测
     *
     * @param title   （可为空）
     * @param content （非空）
     * @return 0:通过，2:不通过，1:嫌疑
     */
    public JSONObject checkText(String title, String content, String keyword) {
        JSONObject result = new JSONObject();
        Set nopassList = new HashSet();
        List<Map<String, Object>> texts = new ArrayList<>();
        if (StringUtils.isNotBlank(title)) {
            Map<String, Object> titleObj = new HashMap<>();
            titleObj.put("name", "标题名称");
            titleObj.put("dataId", UUID.randomUUID().toString());
            titleObj.put("type", 1);
            titleObj.put("data", title);
            texts.add(titleObj);
        }
        if (StringUtils.isNotBlank(keyword)) {
            Map<String, Object> keywordObj = new HashMap<>();
            keywordObj.put("name", "标题名称");
            keywordObj.put("dataId", UUID.randomUUID().toString());
            keywordObj.put("type", 1);
            keywordObj.put("data", keyword);
            texts.add(keywordObj);
        }
        Map<String, Object> contentObj = new HashMap<>();
        contentObj.put("name", "文本名称");
        contentObj.put("dataId", UUID.randomUUID().toString());
        contentObj.put("type", 1);
        contentObj.put("data", content);
        texts.add(contentObj);

        JSONArray jsonArray = getHtml(API_TEXT_URL, texts, 1);

        if (!CollectionUtils.isEmpty(jsonArray)) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject obj = (JSONObject) jsonArray.get(i);
                // 1为不敏感，2为敏感，3为嫌疑   3算不通过
                if (Objects.nonNull(obj)) {
                    if (obj.getInteger("code") > 1 && !CollectionUtils.isEmpty(obj.getJSONArray("hitWords"))) {
                        nopassList.addAll(obj.getJSONArray("hitWords"));
                    }
                }
            }
        }
        result.put("pass", nopassList.isEmpty());
        result.put("nopassList", nopassList);
        return result;
    }

    /**
     * 抽取公共调用方法
     *
     * @param url     请求地址
     * @param objList 对应类型集合 图片或文本
     * @param type    1 文本  2 图片
     * @return
     */
    public JSONArray getHtml(String url, List<Map<String, Object>> objList, Integer type) {
        String html = "";
        JSONArray jsonArray = new JSONArray();
        try {
            CloseableHttpClient client = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            // 用户信息
/*            JSONObject user = new JSONObject();
            user.put("account", uid);
            if(Objects.isNull(wfwfid)){
                wfwfid = CookieUtils.getCookieWfwfid(request);
            }
            user.put("fid", wfwfid);*/

            // 封装json对象
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("nonce", 1);

            jsonObject.put("secretId", SECRET_ID);
            //jsonObject.put("user", user);
            jsonObject.put("timestamp", System.currentTimeMillis());
            if (1 == type) {
                jsonObject.put("contents", objList);
            } else {
                jsonObject.put("images", objList);
            }
            StringEntity stringEntity = new StringEntity(jsonObject.toString(), StandardCharsets.UTF_8);
            stringEntity.setContentEncoding(StandardCharsets.UTF_8.name());
            stringEntity.setContentType("application/json");
            httpPost.setEntity(stringEntity);

            // 签名生成规则 MD5(body + securitykey)
            String str = jsonObject.toJSONString() + SECRET_KEY;
            String rightSignature = DigestUtils.md5Hex((str).getBytes(StandardCharsets.UTF_8));

            // 添加header
            httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
            httpPost.addHeader("CX-Signature", rightSignature);

            HttpHeaders headers = new HttpHeaders();
            headers.add("CX-Signature", rightSignature);
            headers.add("Content-Type", "application/json;charset=utf-8");
            HttpEntity<String> httpEntity = new HttpEntity<>(jsonObject.toJSONString(), headers);

            // restTemplate 执行请求，获取返回值 添加请求头
            String result = restTemplate.postForObject(url,httpEntity, String.class);

//            CloseableHttpResponse response = client.execute(httpPost);
//            HttpEntity entity = response.getEntity();
//            html = EntityUtils.toString(entity, StandardCharsets.UTF_8);
//            EntityUtils.consume(entity);
            JSONObject jsonObject1 = JSON.parseObject(result);
            if (Objects.nonNull(jsonObject1) && jsonObject1.getInteger("code") == 200) {
                jsonArray = jsonObject1.getJSONArray("data");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonArray;
    }


    public static void main(String[] args) {

        JSONObject result = new SuperShieldCheckUtils().checkText("","你大爷的能不能好好说话","");
        System.out.println(result.toJSONString());
    }
}

