package com.github.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 浙江省
 */
public class ZJWfwMap {

    public static Map<String, String> CityMap = new HashMap() {{
        put("浙江图书馆", "2120");
        put("杭州市", "995");
        put("杭州市上城区", "67808");
        put("杭州市滨江区", "19758");
        put("杭州市拱墅区", "28125");
        put("杭州市西湖区", "28126");
        put("杭州市江干区", "28124");
        put("杭州市下城区", "12916");
        put("杭州市余杭区", "8175");
        put("杭州市富阳区", "8875");
        put("杭州市临安区", "8787");
        put("桐庐县", "8786");
        put("建德市", "11361");
        put("淳安县", "11368");
        put("杭州市萧山", "28127");
        put("湖州市", "2576");
        put("德清县", "8522");
        put("安吉县", "8338");
        put("长兴县", "2130");
        put("湖州市南浔区", "8869");
        put("湖州市吴兴区", "136917");
        put("金华市", "2887");
        put("金华市金东区", "21864");
        put("浦江县", "4855");
        put("义乌市", "6244");
        put("东阳市", "11566");
        put("磐安县", "5048");
        put("金华市婺城区", "8629");
        put("永康市", "7759");
        put("武义县", "9022");
        put("兰溪市", "9027");
        put("温州市", "91");
        put("金华少儿", "3434");
        put("鹿城区", "21859");
        put("温州市瓯海区", "9718");
        put("洞头区", "21868");
        put("文成县", "21866");
        put("温州市龙湾区", "9889");
        put("平阳县", "10414");
        put("瑞安市", "10448");
        put("永嘉县", "17678");
        put("乐清市", "18629");
        put("苍南县", "21869");
        put("泰顺县", "21867");
        put("衢州市", "2885");
        put("衢州市柯城区", "28156");
        put("衢州市衢江区", "35165");
        put("江山市", "136918");
        put("开化县", "11447");
        put("常山县", "10134");
        put("龙游县", "9435");
        put("丽水市", "2860");
        put("丽水市莲都区", "17934");
        put("庆元县", "16915");
        put("景宁畲族自治县", "15955");
        put("缙云县", "4442");
        put("遂昌县", "4986");
        put("青田县", "12087");
        put("龙泉市", "10363");
        put("松阳县", "13443");
        put("云和县", "9893");
        put("宁波市", "87");
        put("宁波市北仑区", "9950");
        put("宁波市镇海区", "10229");
        put("宁波市海曙区", "11669");
        put("宁波市鄞州区", "12920");
        put("宁波市江北区", "17441");
        put("宁波市奉化区", "10187");
        put("慈溪市", "9824");
        put("余姚市", "10188");
        put("宁海县", "9951");
        put("象山县", "9769");
        put("台州市", "29168");
        put("玉环县", "2950");
        put("温岭市", "4104");
        put("临海市", "4322");
        put("仙居县", "4792");
        put("台州市路桥区", "8640");
        put("台州市黄岩区", "7819");
        put("台州市椒江区", "12299");
        put("三门县", "9676");
        put("天台县", "8912");
        put("绍兴市", "2886");
        put("绍兴市越城区", "17812");
        put("绍兴市柯桥区", "10466");
        put("绍兴市上虞区", "8481");
        put("诸暨市", "8893");
        put("新昌县", "9046");
        put("嵊州市", "11740");
        put("舟山市", "20465");
        put("舟山市普陀区", "3362");
        put("舟山市定海区", "19287");
        put("岱山县", "6362");
        put("嵊泗县", "4420");
        put("嘉兴市", "2883");
        put("嘉兴市南湖区", "124860");
        put("嘉兴市秀洲区", "21863");
        put("海宁市", "4940");
        put("平湖市", "4939");
        put("桐乡市", "4941");
        put("嘉善县", "4944");
        put("张元济", "4943");
    }};
}
