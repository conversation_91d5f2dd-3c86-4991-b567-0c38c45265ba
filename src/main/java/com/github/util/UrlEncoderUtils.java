package com.github.util;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.BitSet;

public class UrlEncoderUtils {
   
    private static BitSet dontNeedEncoding;
   
    static { 
        dontNeedEncoding = new BitSet(256); 
        int i; 
        for (i = 'a'; i <= 'z'; i++) { 
            dontNeedEncoding.set(i); 
        } 
        for (i = 'A'; i <= 'Z'; i++) { 
            dontNeedEncoding.set(i); 
        } 
        for (i = '0'; i <= '9'; i++) { 
            dontNeedEncoding.set(i); 
        } 
        dontNeedEncoding.set('-');
        dontNeedEncoding.set('_');
        dontNeedEncoding.set('.');
        dontNeedEncoding.set('*');
        dontNeedEncoding.set('+');
    }
   
    /**
     * 判断str是否urlEncoder.encode过<br>
     * 经常遇到这样的情况，拿到一个URL,但是搞不清楚到底要不要encode.<Br>
     * 不做encode吧，担心出错，做encode吧，又怕重复了<Br>
     * 
     * @param str
     * @return
     */ 
    public static boolean hasUrlEncoded(String str) { 
   
        /**
         * 支持JAVA的URLEncoder.encode出来的string做判断。 即: 将' '转成'+' <br>
         * 0-9a-zA-Z保留 <br>
         * '-'，'_'，'.'，'*'保留 <br>
         * 其他字符转成%XX的格式，X是16进制的大写字符，范围是[0-9A-F]
         */ 
        boolean needEncode = false;
        // 包含百分号的才认定为被编码的
        boolean containsPercent = false;
        for (int i = 0; i < str.length(); i++) { 
            char c = str.charAt(i);
            if (c == '%') {
                containsPercent = true;
            }
            if (dontNeedEncoding.get((int) c)) { 
                continue; 
            } 
            if (c == '%' && (i + 2) < str.length()) { 
                // 判断是否符合urlEncode规范 
                char c1 = str.charAt(++i); 
                char c2 = str.charAt(++i); 
                if (isDigit16Char(c1) && isDigit16Char(c2)) { 
                    continue; 
                } 
            } 
            // 其他字符，肯定需要urlEncode 
            needEncode = true; 
            break; 
        } 
   
        return !needEncode && containsPercent;
    }

    public static String getDecodedStr(String str) {
        if (str != null && hasUrlEncoded(str)) {
            try {
                str = URLDecoder.decode(str, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return str;
    }

    /**
     * 判断c是否是16进制的字符
     * 
     * @param c
     * @return
     */ 
    private static boolean isDigit16Char(char c) { 
        return (c >= '0' && c <= '9') || (c >= 'A' && c <= 'F'); 
    }
}