package com.github.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.*;
import org.jsoup.parser.HtmlTreeBuilder;
import org.jsoup.parser.ParseSettings;
import org.jsoup.parser.Parser;
import org.jsoup.safety.Cleaner;
import org.jsoup.safety.Whitelist;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @version v1.0
 * @author: leolin
 * @since: 08/12/2020 3:45 PM
 * @description :类描述
 */
public class JsoupUtil {
    private static final Logger log = LoggerFactory.getLogger(JsoupUtil.class);
    static Whitelist WHITELIST = Whitelist.simpleText().addTags("br");
    /** WHITELIST2与engine2保持一致，请同步修改 **/
    static Whitelist WHITELIST2 = Whitelist.relaxed()
            .addTags("hr").addTags("abbr").addTags("address").addTags("area")
            .addTags("article").addTags("aside").addTags("bdi").addTags("bdo")
            .addTags("big").addTags("canvas").addTags("center").addTags("del")
            .addTags("del").addTags("details").addTags("font").addTags("footer")
            .addTags("header").addTags("ins").addTags("mark").addTags("nav")
            .addTags("s").addTags("section").addTags("small").addTags("sub")
            .addTags("sup").addTags("tt").addTags("strike").addTags("video")
            .addTags("swan-page").addTags("swan-nav").addTags("swan-view")
            .addTags("swan-template").addTags("swan-text").addTags("input")
            .addTags("button")
            //mh-tag-1是webjson里面的script
            .addTags("svg").addTags("style").addTags("mh-tag-1")
            .addAttributes("div", "data-source","contenteditable", "data-noticeid", "module", "data", "type", "statistic", "draggable", "dblclickedit", "site", "object-id", "aspectratio", "data-url", "data-target", "data-parameter", "search-type", "style-type", "search-url", "align-style", "mapzoom", "objectid", "appwidth", "instation-code", "data-shortcut-position")
            .addAttributes("iframe", "advancedpreview", "filetype", "allowpreview", "autoplay", "excel", "againsrc", "editorsrc", "frameborder", "scrolling", "allowfullscreen", "src", "cid", "module", "allowdownload", "download", "preview", "border", "width", "height", "getagain", "previewweb", "data-set-iframe-height-id", "data-iframe-auto-height-current-src")
            .addAttributes("img", "src", "objectid", "mathdata", "allowdownload", "download", "_src", "loadingclass", "data-latex", "fileid", "data-link", "opentype", "role", "aria-label", "tabindex", "data-video-url", "data-islocal-file", "data-has-cover")
            .addAttributes("audio", "controls", "autoplay", "loop", "preload", "src")
            .addAttributes("span", "contenteditable", "data-id", "data-intime", "data-name")
            .addAttributes("a", "module", "target", "filetype", "allowdownload", "download", "preview", "advancedpreview", "allowpreview")
            .addAttributes("code", "lang")
            .addAttributes("area", "shape", "coords", "href")
            .addAttributes("bdi", "dir")
            .addAttributes("bdo", "dir")
            .addAttributes("col", "align", "valign")
            .addAttributes("colgroup", "align", "valign")
            .addAttributes("del", "datetime")
            .addAttributes("details", "open")
            .addAttributes("font", "color", "size", "face")
            .addAttributes("ins", "datetime")
            .addAttributes("li", "serialnum", "level", "search-url", "data-url", "maplng", "maplat", "data-tab-id")
            .addAttributes("ol", "serialnum", "level", "data-origin-start", "data-start")
            .addAttributes("p", "contenteditable")
            .addAttributes("table", "width", "border", "valign")
            .addAttributes("tbody", "align", "valign")
            .addAttributes("td", "align", "valign")
            .addAttributes("th", "align", "valign")
            .addAttributes("tfoot", "align", "valign")
            .addAttributes("thead", "align", "valign")
            .addAttributes("tr", "align", "valign", "rowspan")
            .addAttributes("ul", "serialnum", "level", "data-origin-start", "data-start")
            .addAttributes("video", "autoplay", "controls", "loop", "preload", "src", "height", "width")
            .addAttributes("input", "placeholder", "size-type", "type", "readonly")
            .addAttributes("svg", "xmlns", "version", "xmlns:xlink", "xmlns:svgjs", "width", "height")
            // :all 所有放行标签中的通用属性 "mh-func-1","mh-tag-1" 为自定义前端加密标签或属性
            .addAttributes(":all", "style", "class", "element-id", "name", "id", "alt", "title", "mh-func-1", "color", "background", "top", "center", "url", "background-size", "data-shortcut-position");


    static Document.OutputSettings OUTPUT_SETTINGS = (new Document.OutputSettings()).prettyPrint(false);

    public JsoupUtil() {
    }

    public static String clean(String s) {
        if(StringUtils.isBlank(s)){
            return s;
        }
        String r = Jsoup.clean(s, "http://base.uri", WHITELIST, OUTPUT_SETTINGS);
        r = r.replaceAll("&amp;","&");
        r = r.replaceAll("http://base.uri", "");
        return r;
    }

    /**
     * v1 webjson防xss入口
     * @param s
     * @return
     */
    public static String cleanV1WebJsonContentOld(String s) {
        if(StringUtils.isBlank(s)){
            return s;
        }
        //xss之前先对空格、<、>进行还原
        s = s.replace("[[AA]]"," ").replace("[[BB]]","<").replace("[[CC]]",">");
        s = s.replace("&amp;","&");
        //单引号特殊处理，否则clean之后会变成双引号，导致json结构错误
        // 处理html结构里面写在style里面的单引号问题
        s = s.replaceAll("'", "@mhsigle");
        //阻止clean解析转义
        s = s.replace("&#10;", "@mhquot-#10");
        s = s.replace("&quot;", "@mhquot-#quot");
        // \"替换,分开始和结束
        s = s.replaceAll("=\\\\\"", "=\"@mhquot-start ");
        s = s.replaceAll("\\\\\"", " @mhquot-end\"");

        String r = clean(s, "http://base.uri", WHITELIST2, OUTPUT_SETTINGS);

        r = r.replaceAll("@mhsigle", "'");
        r = r.replaceAll("=\"@mhquot-start ", "=\\\\\"");
        r = r.replaceAll(" @mhquot-end\"", "\\\\\"");
        r = r.replaceAll("@mhquot-#10", "&#10;");
        r = r.replaceAll("@mhquot-#quot", "&quot;");
        r = r.replaceAll("http://base.uri", "");
        //处理，注意这个顺序，必须在替换http://base.uri之后
        r = r.replaceAll("=\"/@mhquot-start ", "=\\\\\"");

        r = r.replace("&amp;","&");
        //webjson反替换，与前端function mhxssEncodeFunc()逻辑一致
        r = r.replace("mh-func-1","onclick").replace("<mh-tag-1>","<script>").replace("</mh-tag-1>","</script>");
        return r;
    }

    /**
     * jsoup clean内容 v1-webjson专用
     * @param bodyHtml
     * @param baseUri
     * @param whitelist
     * @param outputSettings
     * @return
     */
    public static String clean(String bodyHtml, String baseUri, Whitelist whitelist, Document.OutputSettings outputSettings) {
        HtmlTreeBuilder treeBuilder = new HtmlTreeBuilder();
        Parser parser = new Parser(treeBuilder);
        //设置标签和属性保持原状(即大写字母不自动转换为小写)
        parser.settings(ParseSettings.preserveCase);
        Document dirty = Document.createShell(baseUri);
        Element body = dirty.body();
        List<Node> nodeList = parser.parseFragmentInput(bodyHtml, body, baseUri);
        Node[] nodes = nodeList.toArray(new Node[0]);
        for (int i = nodes.length - 1; i > 0; i--) {
            nodes[i].remove();
        }
        for (Node node : nodes) {
            checkNodes(node);
            body.appendChild(node);
        }
        Cleaner cleaner = new Cleaner(whitelist);
        Document clean = cleaner.clean(dirty);
        clean.outputSettings(outputSettings);
        return clean.body().html();
    }

    /**
     * 特殊情况的处理
     * @param node
     */
    private static void checkNodes(Node node) {
        //特殊处理 开始和结束都是 =\" 时，重新拼接 @mhquot-end
        Attributes attributes = node.attributes();
        for (Attribute attribute : attributes) {
            String value = attribute.getValue();
            if (StringUtils.isNotBlank(value) && value.startsWith("@mhquot-start ") && !value.endsWith(" @mhquot-end")) {
                value += " @mhquot-end";
                attribute.setValue(value);
            }
        }
        if (node.childNodeSize() > 0) {
            List<Node> nodes = node.childNodes();
            for (Node child : nodes) {
                checkNodes(child);
            }
        }
    }

    public static String cleanHeaderCookie(String s) {
        if(StringUtils.isBlank(s)){
            return s;
        }
        String r = Jsoup.clean(s, "http://base.uri", WHITELIST, OUTPUT_SETTINGS);
        r = r.replace("alert(","(");
        r = r.replace("prompt(","(");
        r = r.replace("confirm(","(");
        r = r.replace("\\","");
        r = r.replace("confirm(","(");
        r = r.replaceAll("http://base.uri", "");
        return r;
    }


    public static String cleanInvalid(String s) {
        if(StringUtils.isBlank(s)){
            return s;
        }
        String r = Jsoup.clean(s, "http://base.uri", WHITELIST, OUTPUT_SETTINGS);
        r = r.replace("(","");
        r = r.replace(")","");
        r = r.replace(":;","");
        r = r.replace("`","");
        r = r.replace("'","");
        r = r.replace("-","");
        r = r.replaceAll("http://base.uri", "");
        return r;
    }

    public static String cleanJson(String s) {
        if (StringUtils.isEmpty(s)) {
            return s;
        }
        String r = s.trim();
        try {
            if (JsonUtils.isJSON(r)) {
                r = String.valueOf(cleanObject(JSONObject.parseObject(r)));
            } else {
                r = Jsoup.clean(r, "", Whitelist.simpleText().addTags("br"), OUTPUT_SETTINGS);
            }
        }catch (Exception e){
            r = Jsoup.clean(r, "", Whitelist.simpleText().addTags("br"), OUTPUT_SETTINGS);
        }

        r = r.replaceAll("&amp;","&");
        r = r.replaceAll("http://base.uri", "");
        return r;
    }

    public static String jsonStringConvert(String s) {
        log.info("[处理JSON字符串] [将嵌套的双引号转成单引号] [原JSON] :{}", s);
        char[] temp = s.toCharArray();
        int n = temp.length;

        for(int i = 0; i < n; ++i) {
            if(temp[i] == 58 && temp[i + 1] == 34) {
                for(int j = i + 2; j < n; ++j) {
                    if(temp[j] == 34) {
                        if(temp[j + 1] != 44 && temp[j + 1] != 125) {
                            temp[j] = 39;
                        } else if(temp[j + 1] == 44 || temp[j + 1] == 125) {
                            break;
                        }
                    }
                }
            }
        }

        String r = new String(temp);
        return r;
    }

    public static void main(String[] args) throws Exception {

//        cleanTest();
//        cleanFromFileTest();
//
//        String s = "{\"sid\":{\"page\":0,\"app\":[]},\"aid\":[{\"page\":1,\"app\":[{\"x\":9,\"y\":0,\"width\":91,\"height\":30,\"minWidth\":8,\"minHeight\":3,\"appId\":\"4250322\",\"baseModule\":[]},{\"x\":5,\"y\":30,\"width\":50,\"height\":30,\"minWidth\":8,\"minHeight\":3,\"appId\":\"4250299\",\"baseModule\":[]},{\"x\":0,\"y\":60,\"width\":100,\"height\":30,\"minWidth\":8,\"minHeight\":3,\"appId\":\"4340047\",\"baseModule\":[]},{\"x\":0,\"y\":90,\"width\":29,\"height\":30,\"minWidth\":8,\"minHeight\":3,\"appId\":\"4340046\",\"baseModule\":[]},{\"x\":29,\"y\":90,\"width\":50,\"height\":30,\"minWidth\":8,\"minHeight\":3,\"appId\":\"4250233\",\"baseModule\":[]}],\"navId\":\"1\"},{\"page\":2,\"app\":[],\"navId\":\"2\"},{\"page\":3,\"app\":[],\"navId\":\"3\"},{\"page\":4,\"app\":[],\"navId\":\"4\"}],\"setting\":{\"type\":\"nest_normal_lbig\",\"editor\":\"/page/nest_normal_lbig/editor.html\",\"preview\":\"/page/nest_normal_lbig/index.html\",\"hasHeader\":true,\"hasFooter\":true,\"hasBanner\":true,\"showToTop\":false,\"showQuery\":true,\"showMao\":true,\"showBayWindow\":false,\"showGlobalIframe\":false,\"globalIframe\":[{\"minStatuText\":\"点击当前查看更多\",\"src\":\"\",\"isMini\":false,\"showScroll\":false,\"width\":200,\"position\":\"1\",\"height\":200}],\"bg\":{\"multiBgs\":[{\"showStyle\":0,\"bgColor\":\"rgb(255,[[AA]]255,[[AA]]255)\",\"index\":1,\"imgHeight\":600,\"ransparency\":1,\"bgColorHeight\":77},{\"showStyle\":0,\"bgColor\":\"rgb(247,[[AA]]247,[[AA]]247)\",\"index\":2,\"imgHeight\":600,\"ransparency\":1,\"bgColorHeight\":362},{\"showStyle\":0,\"bgColor\":\"rgb(216,[[AA]]205,[[AA]]205)\",\"index\":3,\"imgHeight\":600,\"ransparency\":1,\"bgColorHeight\":435},{\"showStyle\":0,\"bgColor\":\"rgb(115,[[AA]]4,[[AA]]4)\",\"index\":4,\"imgHeight\":600,\"ransparency\":1,\"bgColorHeight\":661}],\"singleBg\":[],\"bgMode\":1},\"themeColor\":\"rgb(192,[[AA]]17,[[AA]]17)\",\"plugs\":[{\"jsComponent\":\"select2\",\"dependentList\":\"\"},{\"jsComponent\":\"mCustomScrollbar\",\"dependentList\":\"\"},{\"jsComponent\":\"xlPaging\",\"dependentList\":\"\"},{\"jsComponent\":\"collect\",\"dependentList\":\"\"},{\"jsComponent\":\"swiper\",\"dependentList\":\"\"},{\"jsComponent\":\"swiper3dflow\",\"dependentList\":[\"swiper\"]},{\"jsComponent\":\"jqthumb\",\"dependentList\":\"\"},{\"jsComponent\":\"swiperProgress\",\"dependentList\":\"\"},{\"jsComponent\":\"mCustomScrollBar\",\"dependentList\":\"\"},{\"jsComponent\":\"jqcloud\",\"dependentList\":\"\"},{\"jsComponent\":\"liMarquee\",\"dependentList\":\"\"},{\"jsComponent\":\"schedule\",\"dependentList\":\"\"},{\"jsComponent\":\"DataNumber\",\"dependentList\":\"\"},{\"jsComponent\":\"tagcloud\",\"dependentList\":\"\"},{\"jsComponent\":\"CountUp\",\"dependentList\":\"\"},{\"jsComponent\":\"svg3dtagcloud\",\"dependentList\":\"\"},{\"jsComponent\":\"almanac\",\"dependentList\":\"\"}],\"globalWindow\":{},\"layoutData\":[],\"moduleSort\":[\"myModules1\",\"myModules3\",\"myModules4\",\"myModules5\"],\"musicStyle\":{\"show\":false,\"type\":1,\"loop\":false,\"online\":false,\"name\":\"\",\"url\":\"\"},\"leftNavSets\":{\"backgroudColor\":\"\",\"backgroudType\":0,\"createTime\":\"2020-03-16[[AA]]11:52:51\",\"createUser\":96612960,\"id\":159676,\"itemList\":[{\"createTime\":\"2020-03-16[[AA]]11:54:39\",\"createUser\":96612960,\"id\":1,\"itemId\":1,\"layout\":0,\"name\":\"学校要闻\",\"navigationId\":159676,\"sequence\":1,\"status\":1,\"updateTime\":\"2020-08-13[[AA]]06:51:13\",\"url\":\"\",\"url2\":\"\"},{\"createTime\":\"2020-08-13[[AA]]06:51:03\",\"createUser\":22531418,\"id\":2,\"itemId\":2,\"layout\":0,\"name\":\"综合新闻\",\"navigationId\":159676,\"sequence\":2,\"status\":1,\"updateTime\":\"2020-08-13[[AA]]06:51:19\",\"url\":\"\",\"url2\":\"\"},{\"createTime\":\"2020-08-13[[AA]]07:04:16\",\"createUser\":22531418,\"id\":3,\"itemId\":3,\"layout\":0,\"name\":\"图片新闻\",\"navigationId\":159676,\"sequence\":3,\"status\":1,\"updateTime\":null,\"url\":\"\",\"url2\":\"\"},{\"createTime\":\"2020-08-13[[AA]]07:14:01\",\"createUser\":22531418,\"id\":4,\"itemId\":4,\"layout\":0,\"name\":\"排行\",\"navigationId\":159676,\"sequence\":4,\"status\":1,\"updateTime\":null,\"url\":\"\",\"url2\":\"\"}],\"logoUrl\":\"\",\"pageId\":830702,\"position\":2,\"showFirst\":0,\"source\":2,\"typeId\":1,\"updateTime\":\"2020-03-16[[AA]]11:52:51\",\"wfwfid\":0},\"leftNavs\":[{\"createTime\":\"2020-03-16[[AA]]11:54:39\",\"createUser\":96612960,\"id\":1,\"itemId\":1,\"layout\":0,\"name\":\"学校要闻\",\"navigationId\":159676,\"sequence\":1,\"status\":1,\"updateTime\":\"2020-08-13[[AA]]06:51:13\",\"url\":\"\",\"url2\":\"\"},{\"createTime\":\"2020-08-13[[AA]]06:51:03\",\"createUser\":22531418,\"id\":2,\"itemId\":2,\"layout\":0,\"name\":\"综合新闻\",\"navigationId\":159676,\"sequence\":2,\"status\":1,\"updateTime\":\"2020-08-13[[AA]]06:51:19\",\"url\":\"\",\"url2\":\"\"},{\"createTime\":\"2020-08-13[[AA]]07:04:16\",\"createUser\":22531418,\"id\":3,\"itemId\":3,\"layout\":0,\"name\":\"图片新闻\",\"navigationId\":159676,\"sequence\":3,\"status\":1,\"updateTime\":null,\"url\":\"\",\"url2\":\"\"},{\"createTime\":\"2020-08-13[[AA]]07:14:01\",\"createUser\":22531418,\"id\":4,\"itemId\":4,\"layout\":0,\"name\":\"排行\",\"navigationId\":159676,\"sequence\":4,\"status\":1,\"updateTime\":null,\"url\":\"\",\"url2\":\"\"}],\"pekingLibraryDiv\":{\"shortcutPosition\":\"1\",\"shortcutDiv\":\"[[BB]]!--固定在左下角的图标（图书馆）--[[CC]]\\r\\n[[BB]]style[[CC]]\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-text-style2[[AA]]{\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-text-style2[[AA]].list-item[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]display:[[AA]]table;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]padding:[[AA]]0[[AA]]12px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]height:[[AA]]36px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]line-height:[[AA]]36px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]font-size:[[AA]]14px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]position:[[AA]]relative;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-webkit-box-sizing:[[AA]]border-box;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-moz-box-sizing:[[AA]]border-box;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]box-sizing:[[AA]]border-box;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]margin-bottom:[[AA]]10px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]width:[[AA]]100%;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-text-style2[[AA]].list-item:last-child[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]margin-bottom:[[AA]]0;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-text-style2[[AA]].list-item:hover[[AA]].big-box[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]display:[[AA]]block;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-text-style2[[AA]].list-item[[AA]]img[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]width:[[AA]]100%;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]height:[[AA]]100%;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-webkit-border-radius:[[AA]]4px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-moz-border-radius:[[AA]]4px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]border-radius:[[AA]]4px;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-text-style2[[AA]].list-item[[AA]].big-box[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]width:[[AA]]100px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]height:[[AA]]100px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-webkit-border-radius:[[AA]]4px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-moz-border-radius:[[AA]]4px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]border-radius:[[AA]]4px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-webkit-box-shadow:[[AA]]0px[[AA]]2px[[AA]]4px[[AA]]0px[[AA]]rgba(0,[[AA]]0,[[AA]]0,[[AA]]0.16);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-moz-box-shadow:[[AA]]0px[[AA]]2px[[AA]]4px[[AA]]0px[[AA]]rgba(0,[[AA]]0,[[AA]]0,[[AA]]0.16);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]box-shadow:[[AA]]0px[[AA]]2px[[AA]]4px[[AA]]0px[[AA]]rgba(0,[[AA]]0,[[AA]]0,[[AA]]0.16);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]background:[[AA]]#ffffff;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]position:[[AA]]absolute;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]top:[[AA]]50%;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-webkit-transform:[[AA]]translateY(-50%);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-moz-transform:[[AA]]translateY(-50%);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-ms-transform:[[AA]]translateY(-50%);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-o-transform:[[AA]]translateY(-50%);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]transform:[[AA]]translateY(-50%);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]left:[[AA]]50px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]z-index:[[AA]]2;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]display:[[AA]]none;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-text-style2.shortcut-position-1[[AA]].list-item,\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-text-style2.shortcut-position-4[[AA]].list-item[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]text-align:[[AA]]left;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-text-style2.shortcut-position-2[[AA]].list-item,\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-text-style2.shortcut-position-3[[AA]].list-item[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]text-align:[[AA]]right;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[BB]]/style[[CC]]\\r\\n[[BB]]!--shortcutPosition[[AA]]后台动态绑定：1:左侧靠边，2:右侧靠边，3:左侧靠内容，4:右侧靠内容--[[CC]]\\r\\n[[BB]]div[[AA]]class=\\\"custom-text-style2[[AA]]shortcut-position-1\\\"[[AA]]data-shortcutPosition=\\\"1\\\"[[AA]]style=\\\"color:[[AA]]#ffffff;\\\"[[CC]]\\r\\n[[AA]][[AA]][[AA]][[AA]][[BB]]div[[AA]]class=\\\"list[[AA]]clear\\\"[[CC]]\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[BB]]a[[AA]]class=\\\"list-item[[AA]]bg-theme\\\"[[AA]]target=\\\"_self\\\"[[AA]]href=\\\"javascript:;\\\"[[AA]]title=\\\"联系电话[[AA]]010-88888888\\\"[[AA]]style=\\\"background:#3D82F2;[[AA]];\\\"[[CC]][[BB]]!--默认主题色,后台可设置--[[CC]]\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[BB]]span[[AA]]class=\\\"txt\\\"[[CC]]读者咨询[[BB]]/span[[CC]]\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[BB]]/a[[CC]]\\r\\n[[AA]][[AA]][[AA]][[AA]][[BB]]/div[[CC]]\\r\\n[[BB]]/div[[CC]]\\r\\n[[BB]]mh-tag-1[[CC]]\\r\\n[[AA]][[AA]][[AA]][[AA]]var[[AA]]customTextTemp2[[AA]]=[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]init:[[AA]]function[[AA]](elem)[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]$(\\\"body\\\").on(\\\"mouseenter\\\",[[AA]]\\\".custom-text-style2[[AA]].list-item\\\",[[AA]]function[[AA]]()[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]var[[AA]]curPos[[AA]]=[[AA]]$(elem).find('.custom-text-style2').attr('data-shortcutPosition');\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]if[[AA]](curPos[[AA]]&&[[AA]](curPos[[AA]]==[[AA]]1[[AA]]||[[AA]]curPos[[AA]]==[[AA]]3))[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]var[[AA]]left[[AA]]=[[AA]]$(this).width()[[AA]]+[[AA]]35;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]$(this).find('.big-box').show().css('left',[[AA]]left);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]}[[AA]]else[[AA]]if[[AA]](curPos[[AA]]&&[[AA]](curPos[[AA]]==[[AA]]2[[AA]]||[[AA]]curPos[[AA]]==[[AA]]4))[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]var[[AA]]right[[AA]]=[[AA]]$(this).width()[[AA]]+[[AA]]35;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]$(this).find('.big-box').show().css({'right':[[AA]]right,[[AA]]'left':[[AA]]'unset'});\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]});\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]$(\\\"body\\\").on(\\\"mouseleave\\\",[[AA]]\\\".custom-text-style2[[AA]].list-item\\\",[[AA]]function[[AA]]()[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]$(this).find('.big-box').hide();\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]});\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]}\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]]function[[AA]]engineInitCallback(elem)[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]customTextTemp2.init(elem);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]return[[AA]]customTextTemp2;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n[[BB]]/mh-tag-1[[CC]]\",\"scrollToTopDiv\":\"[[BB]]!--固定在左下角的图标（图书馆）--[[CC]]\\r\\n[[BB]]style[[CC]]\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-backTop-style1[[AA]].box[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]height:[[AA]]40px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]line-height:[[AA]]40px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]padding:[[AA]]0[[AA]]16px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]position:[[AA]]relative;\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-backTop-style1[[AA]].back-top[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]margin-right:[[AA]]16px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]cursor:[[AA]]pointer;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-backTop-style1[[AA]].back-top:before[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]margin-right:[[AA]]5px;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-backTop-style1[[AA]].box[[AA]]a[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]margin-right:[[AA]]16px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]cursor:[[AA]]pointer;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]position:[[AA]]relative;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-backTop-style1[[AA]].box[[AA]]a:hover[[AA]].hover-box[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]display:[[AA]]block;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-backTop-style1[[AA]].box[[AA]]a:last-child[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]margin-right:[[AA]]0;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-backTop-style1[[AA]].hover-box[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]width:[[AA]]70px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]height:[[AA]]70px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]padding:[[AA]]5px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]background:[[AA]]#ffffff;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-webkit-border-radius:[[AA]]4px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-moz-border-radius:[[AA]]4px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]border-radius:[[AA]]4px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]position:[[AA]]absolute;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]bottom:[[AA]]36px;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]left:[[AA]]50%;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-webkit-transform:[[AA]]translateX(-50%);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-moz-transform:[[AA]]translateX(-50%);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-ms-transform:[[AA]]translateX(-50%);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]-o-transform:[[AA]]translateX(-50%);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]transform:[[AA]]translateX(-50%);\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]display:[[AA]]none;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n[[AA]][[AA]][[AA]][[AA]].custom-backTop-style1[[AA]].hover-box[[AA]]img[[AA]]{\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]width:[[AA]]100%;\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]height:[[AA]]100%;\\r\\n[[AA]][[AA]][[AA]][[AA]]}\\r\\n\\r\\n\\r\\n[[BB]]/style[[CC]]\\r\\n\\r\\n[[BB]]div[[AA]]class=\\\"custom-backTop-style1[[AA]]\\\"[[AA]]style=\\\"color:[[AA]]#ffffff;\\\"[[CC]]\\r\\n[[AA]][[AA]][[AA]][[AA]][[BB]]div[[AA]]class=\\\"box[[AA]]bg-theme\\\"[[CC]]\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[BB]]span[[AA]]class=\\\"icon-up[[AA]]back-top[[AA]]cgMhLan\\\"[[AA]]mh-func-1=\\\"backTop()\\\"[[CC]]回到顶部[[BB]]/span[[CC]]\\r\\n[[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]][[AA]]\\r\\n[[AA]][[AA]][[AA]][[AA]][[BB]]/div[[CC]]\\r\\n[[BB]]/div[[CC]]\\r\\n\\r\\n\\r\\n\"},\"fontStyle\":false,\"autoSave\":true,\"mobileBgColor\":\"rgb(245,[[AA]]245,[[AA]]245)\",\"showAppHeader\":true,\"showAppFooter\":true},\"embedModdle\":{\"headModule\":\"[[BB]]div[[AA]]class=\\\"item-drag-wrap[[AA]]fixed-height[[AA]]language-module-container[[AA]]ui-draggable[[AA]]ui-draggable-handle[[AA]]ui-resizable\\\"[[AA]]style=\\\"position:[[AA]]absolute;top:70.3671875px;left:50%;margin-left:391.625px;\\\"[[CC]][[BB]]div[[AA]]class=\\\"module-set-wrap\\\"[[AA]]style=\\\"inset:[[AA]]-48.1328px[[AA]]0px[[AA]]auto[[AA]]auto;[[AA]]display:[[AA]]none;\\\"[[CC]][[BB]]div[[AA]]class=\\\"little-module-set\\\"[[CC]][[BB]]div[[AA]]class=\\\"set\\\"[[CC]][[BB]]span[[AA]]class=\\\"icon-set[[AA]]icon\\\"[[CC]][[BB]]/span[[CC]][[AA]][[BB]]span[[CC]]设置[[BB]]/span[[CC]][[BB]]/div[[CC]][[BB]]div[[AA]]class=\\\"middle-set\\\"[[CC]][[BB]]span[[AA]]class=\\\"icon[[AA]]zIndex-up[[AA]]icon-arrow-up\\\"[[AA]]data-name=\\\"上一层\\\"[[CC]][[BB]]/span[[CC]][[BB]]span[[AA]]class=\\\"icon[[AA]]zIndex-down[[AA]]icon-arrow-down\\\"[[AA]]data-name=\\\"下一层\\\"[[CC]][[BB]]/span[[CC]][[BB]]/div[[CC]][[BB]]div[[AA]]class=\\\"delete\\\"[[CC]][[BB]]span[[AA]]class=\\\"icon[[AA]]icon-rubbish[[AA]]marginr4\\\"[[CC]][[BB]]/span[[CC]][[BB]]span[[CC]]删除[[BB]]/span[[CC]][[BB]]/div[[CC]][[BB]]/div[[CC]][[BB]]/div[[CC]][[BB]]div[[AA]]class=\\\"language-module[[AA]]item-content\\\"[[CC]][[BB]]a[[CC]][[BB]]span[[AA]]class=\\\"location-btn\\\"[[CC]]简体中文[[BB]]/span[[CC]][[BB]]i[[AA]]class=\\\"icon-down\\\"[[CC]][[BB]]/i[[CC]][[BB]]/a[[CC]][[BB]]/div[[CC]][[BB]]div[[AA]]class=\\\"ui-resizable-handle[[AA]]ui-resizable-n\\\"[[AA]]style=\\\"z-index:[[AA]]90;[[AA]]display:[[AA]]block;\\\"[[CC]][[BB]]/div[[CC]][[BB]]div[[AA]]class=\\\"ui-resizable-handle[[AA]]ui-resizable-e\\\"[[AA]]style=\\\"z-index:[[AA]]90;[[AA]]display:[[AA]]block;\\\"[[CC]][[BB]]/div[[CC]][[BB]]div[[AA]]class=\\\"ui-resizable-handle[[AA]]ui-resizable-s\\\"[[AA]]style=\\\"z-index:[[AA]]90;[[AA]]display:[[AA]]block;\\\"[[CC]][[BB]]/div[[CC]][[BB]]div[[AA]]class=\\\"ui-resizable-handle[[AA]]ui-resizable-w\\\"[[AA]]style=\\\"z-index:[[AA]]90;[[AA]]display:[[AA]]block;\\\"[[CC]][[BB]]/div[[CC]][[BB]]/div[[CC]]\",\"bannModule\":\"\",\"footModule\":\"\"},\"version\":76}";
//        s = cleanV1WebJsonContent(s);
//        System.out.println(s);

        String s = "<div class=\"custom-text-style2 shortcut-position-1\" data-shortcut-position=\"1\" style=\"color: #ffffff;\">\n" +
                "    <div class=\"list clear\">\n" +
                "        <a class=\"list-item bg-theme\" target=\"_self\" href=\"javascript:;\" title=\"联系电话 010-88888888\" style=\"background:#3D82F2; ;\"><!--默认主题色,后台可设置-->\n" +
                "            <span class=\"txt\">读者咨询</span>\n" +
                "            \n" +
                "        </a>\n" +
                "    </div>\n" +
                "</div>";
        Object s2 = cleanObject(s);
        System.out.println(s2.toString());
    }

    /**
     * 过滤测试
     */
    public static void cleanTest() {
        String content = "";
        content = cleanV1WebJsonContent(content);
        System.out.println(content);
    }
    /**
     * 文件读取过长的webjson
     * @throws Exception
     */
    public static void cleanFromFileTest() throws Exception{
        File file = new File("/Users/<USER>/Desktop/webJson11");

        BufferedReader br = new BufferedReader(new FileReader(file));
        String s;
        StringBuilder result = new StringBuilder();
        while((s = br.readLine()) != null) {
            result.append(cleanV1WebJsonContent(s));
        }

        System.out.println(result.toString());
    }

    /**
     * v1 webjson防xss入口
     *
     * @param s
     * @return
     */
    public static String cleanV1WebJsonContent(String s) {
        if (StringUtils.isBlank(s)) {
            return s;
        }
        //xss之前先对空格、<、>进行还原
        s = s.replace("[[AA]]"," ").replace("[[BB]]","<").replace("[[CC]]",">");
        s = s.replaceAll("&amp;", "&");
        String r = s;
        try {
            r = String.valueOf(cleanObject(JSONObject.parseObject(r)));
        } catch (Exception e) {
            r = clean(s, "http://base.uri", WHITELIST2, OUTPUT_SETTINGS);
        }
        r = r.replaceAll("&amp;", "&");
        r = r.replaceAll("http://base.uri", "");
        r = r.replace("mh-func-1","onclick").replace("<mh-tag-1>","<script>").replace("</mh-tag-1>","</script>");

        return r;
    }

    /**
     * 匹配img或者iframe中的src属性
     */
    private static final String SRC_REG = " src\\s*=\\s*['\"]([^'\"]*?)['\"]";
    public static Pattern patternSrc = Pattern.compile(SRC_REG);
    public static Object cleanObject(Object obj) {
        if (obj != null) {
            if (obj instanceof String) {
                String str = String.valueOf(obj);
                if ((str.contains("<") && str.contains(">")) || (str.contains("&lt;") && str.contains("&gt;"))) {
                    str = Jsoup.clean(str, "http://base.uri", WHITELIST2, OUTPUT_SETTINGS);

                    Matcher matcher = patternSrc.matcher(str);
                    StringBuffer modifiedStr = new StringBuffer();
                    while (matcher.find()) {
                        String tag = matcher.group();
                        String srcValue = matcher.group(1);
                        String src = srcValue.replaceAll("http://base.uri", "");
                        if (!src.startsWith("/engine2/") && !src.startsWith("/entry/upload/portal/")
                                && !src.startsWith("/upload/portal/")) {
                            matcher.appendReplacement(modifiedStr, Matcher.quoteReplacement(tag.replace(srcValue, "http://portal.chaoxing.com/" + srcValue)));
                        }
                    }
                    matcher.appendTail(modifiedStr);
                    obj = modifiedStr.toString();
                }
            } else if (obj instanceof JSONArray) {
                JSONArray array = (JSONArray) obj;
                for (Object item : array) {
                    cleanObject(item);
                }
            } else if (obj instanceof JSONObject) {
                JSONObject jsonObj = (JSONObject) obj;
                jsonObj.forEach((k, v) -> {
                    jsonObj.put(k, cleanObject(v));
                });
            }
        }
        return obj;
    }

    static {
        WHITELIST.addAttributes(":all", new String[]{"style"});
        WHITELIST.preserveRelativeLinks(true);
    }
}
