package com.github.util;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;


public class HmacUtils {
    //加密算法
    public static final String HMAC_SHA1 = "HmacSHA1";
    public static final String HMAC_MD5 = "HmacMD5";
    public static final String HMAC_SHA256 = "HmacSHA256";
    public static final String HMAC_SHA512 = "HmacSHA512";


    /**
     * 实现Hmac系列的加密算法HmacSHA1、HmacMD5等
     *
     * @param input 需要加密的输入参数
     * @param key 密钥
     * @param algorithm 选择加密算法
     * @return 加密后的值
     **/
    public static String encrypt(String input, String key, String algorithm) {
        String cipher = "";
        try {
            byte[] data = key.getBytes(StandardCharsets.UTF_8);
            //根据给定的字节数组构造一个密钥，第二个参数指定一个密钥的算法名称，生成HmacSHA1专属密钥
            SecretKey secretKey = new SecretKeySpec(data, algorithm);

            //生成一个指定Mac算法的Mac对象
            Mac mac = Mac.getInstance(algorithm);
            //用给定密钥初始化Mac对象
            mac.init(secretKey);
            byte[] text = input.getBytes(StandardCharsets.UTF_8);
            byte[] encryptByte = mac.doFinal(text);
            cipher = bytesToHexStr(encryptByte);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
        }
        return cipher;
    }

    public static String encrypt2Base64(String input, String key, String algorithm) {
        String cipher = "";
        try {
            byte[] data = key.getBytes(StandardCharsets.UTF_8);
            //根据给定的字节数组构造一个密钥，第二个参数指定一个密钥的算法名称，生成HmacSHA1专属密钥
            SecretKey secretKey = new SecretKeySpec(data, algorithm);

            //生成一个指定Mac算法的Mac对象
            Mac mac = Mac.getInstance(algorithm);
            //用给定密钥初始化Mac对象
            mac.init(secretKey);
            byte[] text = input.getBytes(StandardCharsets.UTF_8);
            byte[] encryptByte = mac.doFinal(text);
            cipher = bytesToBase64(encryptByte);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
        }
        return cipher;
    }


    /**
     * byte数组转16进制字符串
     *
     * @param  bytes byte数组
     * @return hex字符串
     */
    public static String bytesToHexStr(byte[] bytes) {
        StringBuilder hexStr = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(b & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            hexStr.append(hex);
        }
        return hexStr.toString();
    }

    public static String bytesToBase64(byte[] bytes) {
        byte[] encodedBytes = Base64.getEncoder().encode(bytes);

        // 将加密后的字节数组转换为字符串输出
        String encodedString = new String(encodedBytes);
        return encodedString;
    }

    public static void main(String[] args) {
        String valMD5 = HmacUtils.encrypt("abc", "123456", HmacUtils.HMAC_MD5);
        System.out.println(valMD5);

        String valSha1 = HmacUtils.encrypt("abc", "123456", HmacUtils.HMAC_SHA1);
        System.out.println(valSha1);

        String valSha256 = HmacUtils.encrypt("abc", "123456", HmacUtils.HMAC_SHA256);
        System.out.println(valSha256);

        String valSha512 = HmacUtils.encrypt("abc", "123456", HmacUtils.HMAC_SHA512);
        System.out.println(valSha512);

    }
}
