package com.github.util;

import org.apache.commons.lang.StringUtils;
import org.springframework.util.NumberUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class UaUtils {
    public static void main(String[] args) {
        String userAgentOld = "Dalvik/2.1.0 (Linux; U; Android 12; Mi 10 Build/SKQ1.220201.001) com.chaoxing.mobile/ChaoXingStudy_3_4.8.2_android_phone_600_56 (@Azeroth)_8da965c0167a4f92aad6069dd4966e76";
        String userAgentNew = "Dalvik/2.1.0 (Linux; U; Android 11; ABR-AL00 Build/HUAWEIABR-AL00) (schild:d17376ffd117130c1367a0c704e3ba7f) (device:ABR-AL00) Language/zh_CN_#Hans com.chaoxing.mobileinhouse/ChaoXingStudy_3_5.2.3.9_android_phone_891_97 (@Azeroth)_f7a007ab1d9e48b2aca72d8ae08c9388";

        long time = System.currentTimeMillis();
        long useTime = 0;
        Pattern pOld = Pattern.compile(".*ChaoXingStudy_(\\d+)_(\\d+[^_]*)_([^_]*)_([^_]*)_([^ ]*)?( \\([^)]*\\))?.*_(.*[-]?\\w+).*");
        Pattern pNew = Pattern.compile("\\(schild:(\\w+).*ChaoXingStudy_(\\d+)_(\\d+[^_]*)_([^_]*)_([^_]*)_([^ ]*)?( \\([^)]*\\))?.*_(.*[-]?\\w+).*");

        Matcher mOld = pOld.matcher(userAgentOld);

        if (mOld.find()) {
            for (int i = 0; i <= mOld.groupCount(); i++) {
                System.out.println("old_" + i + " " + mOld.group(i));
            }
            useTime += System.currentTimeMillis() - time;
            time = System.currentTimeMillis();
        }
        Matcher mNew = pNew.matcher(userAgentNew);

        if (mNew.find()) {
            for (int i = 0; i <= mNew.groupCount(); i++) {
                System.out.println("new_" + i + " " + mNew.group(i));
            }
            useTime += System.currentTimeMillis() - time;
            time = System.currentTimeMillis();
        }

        System.out.println(getVersion(userAgentNew));
        System.out.println(getVersion(userAgentOld));
    }

    public static Integer getVersion(String userAgent) {
        Pattern pOld = Pattern.compile(".*ChaoXingStudy_(\\d+)_(\\d+[^_]*)_([^_]*)_([^_]*)_([^ ]*)?( \\([^)]*\\))?.*_(.*[-]?\\w+).*");
        Pattern pNew = Pattern.compile("\\(schild:(\\w+).*ChaoXingStudy_(\\d+)_(\\d+[^_]*)_([^_]*)_([^_]*)_([^ ]*)?( \\([^)]*\\))?.*_(.*[-]?\\w+).*");

        Matcher mOld = pOld.matcher(userAgent);
        String value = "";

        if (mOld.find()) {
            for (int i = 0; i <= mOld.groupCount(); i++) {
                if (i >= 5) {
                    value = mOld.group(i);
                    if (StringUtils.isNotBlank(value) && value.indexOf("_") != -1) {
                        value = value.substring(value.indexOf("_") + 1);
                        break;
                    }
                }
            }
        }
        Matcher mNew = pNew.matcher(userAgent);

        if (mNew.find()) {
            for (int i = 0; i <= mNew.groupCount(); i++) {
                if (i >= 5) {
                    value = mNew.group(i);
                    if (StringUtils.isNotBlank(value) && value.indexOf("_") != -1) {
                        value = value.substring(value.indexOf("_") + 1);
                        break;
                    }
                }
            }
        }

        if (StringUtils.isNotEmpty(value)) {
            return NumberUtils.parseNumber(value, Integer.class);
        } else {
            return Constants.STATUS_FALSE;
        }
    }
}
