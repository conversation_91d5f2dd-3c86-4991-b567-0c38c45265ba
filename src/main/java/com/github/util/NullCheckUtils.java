package com.github.util;

import com.github.enums.ErrorCode;
import com.github.util.exception.BusinessException;
import org.apache.commons.lang.StringUtils;

/**
 * @className NullCheckUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/20 10:14
 * @Version 1.0
 **/
public class NullCheckUtils {

    /**
     * 业务层面判空处理。
     * @param args
     * @return
     */
    public static boolean isNull(Object... args){
        boolean result = false;
        for(Object o : args){
            if(null == o){
                result = true;
            }
            if(o instanceof String){
                if(null == o || StringUtils.isBlank(o.toString()) || o.toString().equals("undefined")){
                    result = true;
                }
            }
        }

        if(result){
            throw new BusinessException(ErrorCode.CODE_NULL_POINTER.getCode());
        }

        return result;
    }

}
