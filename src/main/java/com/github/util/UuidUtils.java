package com.github.util;

import com.fasterxml.uuid.Generators;
import org.apache.commons.lang.math.RandomUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 2022-09-01 格式。
 * 7c26f141-29f0-11ed-9d5f-d7935f22f4d2
 * 7c26f142-29f0-11ed-9d5f-31fa4ab8e27f
 * 7c26f143-29f0-11ed-9d5f-3d0354e008aa
 * 7c26f144-29f0-11ed-9d5f-93f99c755164
 *
 * 4393890b-2a61-11ed-a237-4f027f771856
 * 4393890c-2a61-11ed-a237-9125c6fcdd31
 * @className UuidUtils
 * @Description init uuid utils
 * <AUTHOR>
 * @Date 2022/9/1 10:04
 * @Version 1.0
 **/
@Component
public class UuidUtils {
    // 中间空格的部分使用服务器节点来配置实现。
    @Value("${uuid.client:10}")
    private String client;
    public static final int LEN = 36;

    public String init(){
        UUID u = Generators.timeBasedGenerator().generate();

        String uuid = u.toString();// UUID.randomUUID().toString();

        StringBuilder sbuild = new StringBuilder();
        for(int i=0;i<36;i++){
            String t = String.valueOf(uuid.charAt(i));
            if(i == 8 || i == 23){
                t = String.valueOf(RandomUtils.nextInt(9));
            }else if(i == 13){
                t = client.substring(0,1);
            }else if(i == 18){
                t = client.substring(1,2);
            }
            sbuild.append(t);
        }
        return sbuild.toString();
    }

    public static void main(String args[]){
        Long l1 = System.currentTimeMillis();
        for(int i=0;i<10000;i++) {
            String uid = Generators.timeBasedGenerator().generate().toString();
            System.out.println(uid);
        }
        System.out.println(System.currentTimeMillis() - l1);
    }
}
