package com.github.util;

/**
 * <AUTHOR>
 * @version ver 1.0
 * @className PassportConstants
 * @description
 * @blame xhl
 * @date 2019-11-19 14:49:34
 */
public class PassportConstants {

    /**
     *  可用
     */
    public static final Integer CHECK_ENABLE = 1;

    /**
     * 不可用
     */
    public static final Integer CHECK_UNABLE = 0;


    /**
     *  可用
     */
    public static final Integer FY_ENABLE = 0;


    /**
     * 电话号码登录key
     */
    public static final String PHONE_LOGIN_KEY = "zEGoEdPM8G2Vtti8ax";
    /**
     * 用户信息
     */
    public static final String PASSPORT_GET_USER_INFO = "/api/v2/userinfo";
    /**
     * 返回json格式
     */
    public static final String PHONE_LOGIN_JSON_URL = "/api/v2/login6json";

    /**
     * 账号密码登录
     */
    public static final String PHONE_LOGIN_BYPWD_URL = "/api/v2/loginbypwd";

    /**
     * 电话，超星号 登录  checkWeakpwd 检查弱密码为false
     */
    public static final String PHONE_CXH_LOGIN_URL = "/v11/loginregister?logininfo=%s&loginType=1&cfid=%s&checkWeakpwd=false&independentId=%d";

    /**
     * 根据wfwfid查询单位名称
     */
    public static final String ORG_INFO = "/org/getName";
}
