package com.github.util.exception;

import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonJsonView;
import com.github.enums.ErrorCode;
import com.github.util.RestResponse;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.coyote.http11.HeadersTooLargeException;
import org.apache.http.conn.HttpHostConnectException;
import org.apache.tomcat.util.http.fileupload.InvalidFileNameException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.TypeMismatchException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.HttpMediaTypeException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;

/**
 * 处理rest请求（返回json）的异常处理
 *
 * @className: RestHandlerExceptionResolver
 * @description:
 * @author: wwb
 * @date: 2018-12-20 12:47:32
 * @version: ver 1.0
 */
@Order(-1)
@Component
public class RestHandlerExceptionResolver implements HandlerExceptionResolver {

    @Resource
    private FastJsonConfig fastJsonConfig;
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${isgray:false}")
    private boolean isGray;

    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        ModelAndView modelAndView = null;
        // 是Rest请求 并且 接受该异常类型,正式环境，不打印完整信息。
        String errorMessage = "接口请求错误";
        if (isRestRequest(handler)) {
            String errorUrl = request.getRequestURL().toString() + "?" + request.getQueryString();
            if (ex instanceof TypeMismatchException || ex instanceof HttpMediaTypeException || ex instanceof ServletRequestBindingException
                    || ex instanceof HttpRequestMethodNotSupportedException || ex instanceof InvalidFileNameException || ex instanceof IOException || ex instanceof ClientAbortException) {
                logger.error("exurl:{},code:{},exception:{}", errorUrl, errorMessage, ex.getMessage());
            } else if (ex instanceof NoFidException) {
                errorMessage = "当前请求中没有单位ID，请确认是否登录到该单位";
            } else if (ex instanceof BusinessException) {
                errorMessage = ErrorCode.prekey + ex.getMessage();
            } else if (ex instanceof HttpHostConnectException || ex instanceof ResourceAccessException) {
                errorMessage = "资源请求失败";
                logger.error("exurl:{},code:{},exception:{}", errorUrl, errorMessage, ex.getMessage());
            } else if (ex instanceof HeadersTooLargeException) {
                errorMessage = "请求头超过最大限制";
                logger.error("exurl:{},code:{}", errorUrl, errorMessage);
            } else if (ex instanceof MultipartException || ex instanceof InvalidFileNameException) {
                errorMessage = "文件上传异常 MultipartException or InvalidFileNameException";
                logger.error("exurl:{},code:{}", errorUrl, errorMessage);
            } else if (ex instanceof ClientAbortException) {
                errorMessage = "tomcat ClientAbortException: Connection reset by peer";
                logger.error("exurl:{},code:{}", errorUrl, errorMessage);
            } else {
                errorMessage = "mh_p_" + System.nanoTime();
                // 正式环境，其他异常信息需要打印出来具体错误
                logger.error("exurl:{},code:{},exception:{}", errorUrl, errorMessage, ex.getMessage());
            }

            // 直接通过Response将结果写回
            RestResponse restResponse = RestResponse.error(errorMessage).httpStatus(HttpStatus.OK);
            restResponse.setDescription("接口请求错误");

            FastJsonJsonView fastJsonJsonView = new FastJsonJsonView();
            fastJsonJsonView.setJsonpParameterNames(new HashSet<String>() {{
                add("");
            }});
            fastJsonJsonView.setFastJsonConfig(fastJsonConfig);
            fastJsonJsonView.setExtractValueFromSingleKeyModel(true);

            modelAndView = new ModelAndView(fastJsonJsonView);
            modelAndView.addObject(restResponse);
        }
        return modelAndView;
    }

    /**
     * 是不是rest请求
     *
     * @Description: 判断Controller和Method上是否有@ResponseBody注解
     * @author: wwb
     * @Date: 2018-12-20 12:55:35
     * @param: handler
     * @return: boolean
     */
    private boolean isRestRequest(Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            return AnnotationUtils.findAnnotation(handlerMethod.getMethod(), ResponseBody.class) != null ||
                    AnnotationUtils.findAnnotation(handlerMethod.getBeanType(), ResponseBody.class) != null ||
                    AnnotationUtils.findAnnotation(handlerMethod.getBeanType(), RestController.class) != null;
        }
        return false;
    }
}
