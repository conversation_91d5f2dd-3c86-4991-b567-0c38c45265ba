package com.github.util.exception;

import com.alibaba.fastjson.JSONObject;
import com.github.enums.ErrorCode;
import com.github.util.SpringContextHolder;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.tomcat.util.http.fileupload.InvalidFileNameException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.TypeMismatchException;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.HttpMediaTypeException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;

/**页面请求异常处理
 * @className: PageHandlerExceptionResolver
 * @description:
 * @author: wwb
 * @date: 2018-12-20 13:07:03
 * @version: ver 1.0
 */
@Order(-1)
@Component
public class PageHandlerExceptionResolver implements HandlerExceptionResolver {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 判断是否为Page请求，是否接受该异常
        if (isPageRequest(handler)) {
            ModelAndView modelAndView = null;
            String errorUrl = request.getRequestURL().toString() + "?" + request.getQueryString();
            String params = JSONObject.toJSONString(request.getParameterMap());
            if(ex instanceof TypeMismatchException || ex instanceof HttpMediaTypeException || ex instanceof ServletRequestBindingException
                    || ex instanceof HttpRequestMethodNotSupportedException || ex instanceof InvalidFileNameException || ex instanceof ClientAbortException) {

            } else if(ex instanceof NoFidException){
                modelAndView = new ModelAndView("error/nofid", HttpStatus.OK);

            } else if(ex instanceof NullPointerException){
                modelAndView = new ModelAndView("error/500", HttpStatus.OK);
                String msg = ExceptionUtils.getFullStackTrace(ex);
                logger.error("exurl:" + errorUrl + " | " + params + " -- expt:" + msg, ex);
                modelAndView.addObject("key",ErrorCode.CODE_NULL_POINTER.getName());
                modelAndView.addObject("code", ErrorCode.prekey + ErrorCode.CODE_NULL_POINTER.getCode());

            } else if(ex instanceof ParseException || ex instanceof NumberFormatException){
                modelAndView = new ModelAndView("error/50x", HttpStatus.OK);
                logger.error("exurl:" + errorUrl + " | " + params, ex);
                modelAndView.addObject("key",ErrorCode.CODE_CONTENT_TRANS_ERROR.getName());
                modelAndView.addObject("code",ErrorCode.prekey + ErrorCode.CODE_CONTENT_TRANS_ERROR.getCode());
            } else if (ex instanceof MultipartException || ex instanceof InvalidFileNameException) {
                logger.error("MultipartException InvalidFileNameException 文件异常 exurl:{}", errorUrl);
            } else if (ex instanceof ClientAbortException) {
                String errorMessage = "tomcat ClientAbortException: Connection reset by peer";
                logger.error("exurl:{},code:{}", errorUrl, errorMessage);
            } else{
                modelAndView = new ModelAndView("error/50x", HttpStatus.OK);
                String code = ex.getMessage();
                ErrorCode error = ErrorCode.getByCode(code);
                if(error == null){
                    // 异常错误处理
                    logger.error("exurl:" + errorUrl + " | " + params, ex);
                } else {
                    modelAndView.addObject("key",error.getName());
                    modelAndView.addObject("code",ErrorCode.prekey + error.getCode());
                    logger.error("exurl:" + errorUrl + " | " + params + error.getName() + " | " + error.getCode());
                }
            }

            return modelAndView;
        }
        // 其他类型，应用下一个处理器
        return null;
    }
    /**是否是页面请求
     * @Description: Controller 和 Method中未添加@ResponseBody注解
     * @author: wwb
     * @Date: 2018-12-20 13:09:16
     * @param: handler
     * @return: boolean
     */
    private boolean isPageRequest(Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            return AnnotationUtils.findAnnotation(handlerMethod.getMethod(), ResponseBody.class) == null
                    && AnnotationUtils.findAnnotation(handlerMethod.getBeanType(), ResponseBody.class) == null
                    && AnnotationUtils.findAnnotation(handlerMethod.getBeanType(), RestController.class) == null;
        }
        return true;
    }
}
