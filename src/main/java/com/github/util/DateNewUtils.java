package com.github.util;

import org.apache.commons.lang.StringUtils;
import org.springframework.lang.Nullable;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

/**
 * @className DateNewUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/4/7 17:02
 * @Version 1.0
 **/
public class DateNewUtils {

    /**
     * 获取该月的第一天
     * @return
     */
    public static LocalDate getFirstDayOfMonth(){
        LocalDate oneday = LocalDate.now();
        //当月的第1天
        LocalDate firstDay = oneday.with(TemporalAdjusters.firstDayOfMonth());
        return firstDay;
    }

    /**
     * 获取该月的第一天
     * @return
     */
    public static Date getFirstDayOfMonth2(){
        LocalDate oneday = LocalDate.now();
        //当月的第1天
        LocalDate firstDay = oneday.with(TemporalAdjusters.firstDayOfMonth());
        return localDate2Date(firstDay);
    }

    /**
     * LocalDate 转 Date
     * @param localDate
     * @return
     */
    public static Date localDate2Date(LocalDate localDate){
        Instant instant = localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * date 转localDateTime
     * @param date
     * @return
     */
    public static LocalDateTime date2LocalDateTime(Date date){
        LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        return localDateTime;
    }

    /**
     * 格式化LocalDateTime
     * @param ldt
     * @param formatStr 可以为null
     * @return
     */
    public static String format(LocalDateTime ldt,@Nullable String formatStr){
        DateTimeFormatter formatter = DateTimeFormatter.BASIC_ISO_DATE;
        if(StringUtils.isNotBlank(formatStr)) {
            formatter = DateTimeFormatter.ofPattern(formatStr);
        }
        String str = ldt.format(formatter);
        return str;
    }

    /**
     * 日期转字符串
     * @param date
     * @param formatStr
     * @return
     */
    public static String format(Date date,@Nullable String formatStr){
        LocalDateTime ldt = date2LocalDateTime(date);
        String str = format(ldt,formatStr);
        return str;
    }

    /**
     * 字符串，转固定格式的日期
     * @param dateStr
     * @param formatStr
     * @return
     */
    public static LocalDateTime parse(String dateStr,@Nullable String formatStr){
        DateTimeFormatter formatter = DateTimeFormatter.BASIC_ISO_DATE;
        if(StringUtils.isNotBlank(formatStr)) {
            formatter = DateTimeFormatter.ofPattern(formatStr);
        }
        LocalDateTime dateTime = LocalDateTime.parse(dateStr, formatter); //DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime;
    }

    /**
     * 获取 LocalDateTime 秒的时间
     * @param ldt
     * @return
     */
    public static Long getSecond(LocalDateTime ldt){
        Long time = ldt.toEpochSecond(ZoneOffset.of("+8"));
        return time;
    }

    /**
     * 获取 LocalDateTime 毫秒时间
     * @param ldt
     * @return
     */
    public static Long getMillions(LocalDateTime ldt){
        Long time = ldt.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        return time;
    }

//    /**
//     *
//     * @param ldt  LocalDateTime
//     * @param type 操作类型，
//     * @param value
//     * @return
//     */
//    public static LocalDateTime addPlus(LocalDateTime ldt,int type,int value){
//        LocalDateTime time = ldt.plus(value, ChronoUnit.HOURS);
//
//        return time;
//    }


    public static void main(String[] args) {

        // 日期的增减
        LocalDate oneday = LocalDate.now();
        //当月的第1天
        LocalDate firstDay = oneday.with(TemporalAdjusters.firstDayOfMonth());
        System.out.println(firstDay);

        //当月的第1天，另外一种写法
        LocalDate firstDay2 = oneday.withDayOfMonth(1);
        System.out.println(firstDay2);

        //当月的最后1天，不用考虑大月，小月，平年，闰年
        LocalDate lastDay = oneday.with(TemporalAdjusters.lastDayOfMonth());
        System.out.println(lastDay);

        //当前日期＋1天
        LocalDate tomorrow = oneday.plusDays(1);
        System.out.println(tomorrow);

        //判断是否为闰年
        boolean isLeapYear = tomorrow.isLeapYear();
        System.out.println(isLeapYear);

        //
        LocalTime nowTime = LocalTime.now();
        //如果不想显示毫秒
        LocalTime nowTime2 = LocalTime.now().withNano(0); //14:43:14

        //指定时间
        LocalTime time = LocalTime.of(14, 10, 21); //14:10:21
        LocalTime time2 = LocalTime.parse("12:00:01"); // 12:00:01

        System.out.println("time2:"+time2);

        //当前时间增加2小时
        LocalTime nowTimePlus2Hour = nowTime.plusHours(2); //16:47:23.144
        System.out.println("nowTimePlus2Hour:"+nowTimePlus2Hour);
        //或者
        LocalTime nowTimePlus2Hour2 = nowTime.plus(2, ChronoUnit.HOURS);
        System.out.println("nowTimePlus2Hour2:"+nowTimePlus2Hour2);



//        System.out.println("111-long :"+nowTime.getLong(IsoFields.DAY_OF_QUARTER));
//        System.out.println("222-long :"+nowTime.getLong(IsoFields.WEEK_OF_WEEK_BASED_YEAR));

        System.out.println("time zone system out print:");
        // 时区
        ZoneId defaultZone = ZoneId.systemDefault();
        System.out.println(defaultZone); //Asia/Shanghai

        //查看美国纽约当前的时间
        ZoneId america = ZoneId.of("America/New_York");
        LocalDateTime shanghaiTime = LocalDateTime.now();
        LocalDateTime americaDateTime = LocalDateTime.now(america);
        System.out.println(shanghaiTime); //2016-11-06T15:20:27.996
        System.out.println(americaDateTime); //2016-11-06T02:20:27.996 ，可以看到美国与北京时间差了13小时

        //带有时区的时间
        ZonedDateTime americaZoneDateTime = ZonedDateTime.now(america);
        System.out.println(americaZoneDateTime);


        // format
        String specifyDate = "20151011";
        DateTimeFormatter formatter = DateTimeFormatter.BASIC_ISO_DATE;
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd - HH:mm:ss");
        LocalDateTime llt1 = LocalDateTime.now();
        String llt1Str = llt1.format(formatter2);
        System.out.println("日期时间格式化："+llt1Str);

        // 跟date的相互转化
        //Date与Instant的相互转化
        Instant instant  = Instant.now();
        Date date = Date.from(instant);
        Instant instant2 = date.toInstant();
        System.out.println("转化1："+instant2);

        //Date转为LocalDateTime
        Date date2 = new Date();
        LocalDateTime localDateTime2 = LocalDateTime.ofInstant(date2.toInstant(), ZoneId.systemDefault());
        System.out.println("转化2："+localDateTime2);

        //LocalDateTime转Date
        LocalDateTime localDateTime3 = LocalDateTime.now();
        Instant instant3 = localDateTime3.atZone(ZoneId.systemDefault()).toInstant();
        Long l1 = localDateTime3.toEpochSecond(ZoneOffset.of("+8"));
        System.out.println("转化3，输出long s:"+l1);
        Long l2 = localDateTime3.toInstant(ZoneOffset.of("+8")).toEpochMilli();  // 使用时区，默认输出后的时间
        System.out.println("转化3，输出long ms:"+l2);
        System.out.println("转化3，输出long ms - system.syscurren:"+System.currentTimeMillis());  // 默认当前时区的时间。

        Date date3 = Date.from(instant3);
        System.out.println("转化3："+date3);
        //LocalDate转Date
        //因为LocalDate不包含时间，所以转Date时，会默认转为当天的起始时间，00:00:00
        LocalDate localDate4 = LocalDate.now();
        Instant instant4 = localDate4.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
        Date date4 = Date.from(instant4);
        System.out.println("转化4："+date4);

        LocalDateTime ldttt = localDateTime3.withHour(1);
        Long l111 = getMillions(ldttt);
        System.out.println("  ---------- " + l111);
    }
}
