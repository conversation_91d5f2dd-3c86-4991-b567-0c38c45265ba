package com.github.util;

import org.apache.commons.codec.digest.DigestUtils;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

public class SHA256 {
    public static final String salt = "mh_sha256";
    public static String enc(String url) {
        url += "&sign=" + getSign(url);
        return url;
    }
    public static String getSign(String url) {
        // 1、获取url中的参数
        String param = url.substring(url.indexOf("?") + 1);
        String[] pas = param.split("&");
        TreeMap<String, String> map = new TreeMap<>();
        for (String s : pas) {
            // 正常的参数拼接
            String[] p = s.split("=");
            if (p.length == 2) {
                map.put(p[0], p[1]);
            }
        }

        Set<Map.Entry<String, String>> set = map.entrySet();
        Iterator<Map.Entry<String, String>> iterator = set.iterator();

        StringBuilder strBuilder = new StringBuilder();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            strBuilder.append(entry.getKey() + "=" + entry.getValue());
        }
        String sign = sha256(strBuilder.toString());
        return sign;
    }

    public static String sha256(String url) {
        // 将盐和原始字符串拼接
        String saltedString = url + salt;
        // 生成SHA256摘要
        return DigestUtils.sha256Hex(saltedString);
    }
    public static String sha256(String str, String salt) {
        // 将盐和原始字符串拼接
        String saltedString = str + salt;
        // 生成SHA256摘要
        return DigestUtils.sha256Hex(saltedString);
    }
    public static boolean check(String url) {
        return true;
    }
}
