package com.github.util;

import com.alibaba.druid.util.StringUtils;
import com.github.pagehelper.util.StringUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * excel读写工具类
 */
public class ExcelUtil {
    private static Logger logger = LoggerFactory.getLogger(ExcelUtil.class);
    private final static String XLS = "xls";
    private final static String XLSX = "xlsx";

    /**
     * 读入excel文件，解析后返回
     *
     * @param file
     * @throws IOException
     */
    public static List<List<String[]>> readExcel(MultipartFile file) throws IOException {
        //检查文件  
        checkFile(file);
        //获得Workbook工作薄对象  
        Workbook workbook = getWorkBook(file);
        //创建返回对象，把每个sheet中的值作为一个数组，所有行作为一个集合返回
        List<List<String[]>> sheetList = new ArrayList<>();
        if (workbook != null) {
            for (int sheetNum = 0; sheetNum < workbook.getNumberOfSheets(); sheetNum++) {
                List<String[]> list = new ArrayList<>();
                //获得当前sheet工作表  
                Sheet sheet = workbook.getSheetAt(sheetNum);
                if (sheet == null) {
                    sheetList.add(null);
                    continue;
                }
                //获得当前sheet的开始行  
                int firstRowNum = sheet.getFirstRowNum();
                //获得当前sheet的结束行  
                int lastRowNum = sheet.getPhysicalNumberOfRows();
                //循环除了第一行的所有行  
                for (int rowNum = firstRowNum + 1; rowNum <= lastRowNum; rowNum++) {
                    //获得当前行  
                    Row row = sheet.getRow(rowNum);
                    if (row == null) {
                        continue;
                    }
                    //获得当前行的开始列  
                    int firstCellNum = row.getFirstCellNum();
                    //获得当前行的列数  
                    int lastCellNum = row.getLastCellNum();
                    String[] cells = new String[row.getLastCellNum()];
                    //循环当前行  
                    for (int cellNum = firstCellNum; cellNum < lastCellNum; cellNum++) {
                        Cell cell = row.getCell(cellNum);
                        cells[cellNum] = getCellValue(cell);
                    }
                    list.add(cells);
                }
                sheetList.add(list);
            }
            workbook.close();
        }
        return sheetList;
    }



    public static void checkFile(MultipartFile file) throws IOException {
        //判断文件是否存在  
        if (null == file) {
            logger.error("文件不存在！");
            throw new FileNotFoundException("文件不存在！");
        }
        //获得文件名  
        String fileName = file.getOriginalFilename();
        //判断文件是否是excel文件  
        if (!fileName.endsWith(XLS) && !fileName.endsWith(XLSX)) {
            logger.error(fileName + "不是excel文件");
            throw new IOException(fileName + "不是excel文件");
        }
    }

    public static Workbook getWorkBook(MultipartFile file) {
        //获得文件名  
        String fileName = file.getOriginalFilename();
        //创建Workbook工作薄对象，表示整个excel  
        Workbook workbook = null;
        ZipSecureFile.setMinInflateRatio(0.001d);
        try {
            //获取excel文件的io流  
            InputStream is = file.getInputStream();
            //根据文件后缀名不同(xls和xlsx)获得不同的Workbook实现类对象  
            if (fileName.endsWith(XLS)) {
                //2003  
                workbook = new HSSFWorkbook(is);
            } else if (fileName.endsWith(XLSX)) {
                //2007  
                workbook = new XSSFWorkbook(is);
            }
        } catch (IOException e) {
            logger.info(e.getMessage());
        }
        return workbook;
    }

    public static Workbook getWorkBook(InputStream is,String fileName) {
        //获得文件名
        //创建Workbook工作薄对象，表示整个excel
        Workbook workbook = null;
        ZipSecureFile.setMinInflateRatio(0.001d);
        try {
            //获取excel文件的io流
            //根据文件后缀名不同(xls和xlsx)获得不同的Workbook实现类对象
            if (fileName.endsWith(XLS)) {
                //2003
                workbook = new HSSFWorkbook(is);
            } else if (fileName.endsWith(XLSX)) {
                //2007
                workbook = new XSSFWorkbook(is);
            }
        } catch (IOException e) {
            logger.info(e.getMessage());
        }
        return workbook;
    }

    public static String getCellValue(Cell cell) {
        String cellValue = "";
        if (cell == null) {
            return cellValue;
        }
        if (cell.getCellType() != CellType.STRING && HSSFDateUtil.isCellDateFormatted(cell)) {
            SimpleDateFormat sdf = DateUtils.getSimpleDateFormate("yyyy-MM-dd HH:mm:ss");

            Date date = null;
            double numericCellValue = cell.getNumericCellValue();
            if (numericCellValue == 0.0) {
                date = null;
            } else {
                date = HSSFDateUtil.getJavaDate(numericCellValue);
                cellValue = sdf.format(date);
                cell.setCellValue(cellValue);
            }
        }
        //把数字当成String来读，避免出现1读成1.0的情况
        if (cell.getCellType() == CellType.NUMERIC) {
            cell.setCellType(CellType.STRING);
        }
        //判断数据的类型  
        switch (cell.getCellType()) {
            // 数字
            case NUMERIC:
                cellValue = String.valueOf(cell.getNumericCellValue());
                break;
            // 字符串
            case STRING:
                cellValue = String.valueOf(cell.getStringCellValue());
                break;
            // Boolean
            case BOOLEAN:
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            // 公式
            case FORMULA:
                cellValue = String.valueOf(cell.getCellFormula());
                break;
            // 空值
            case BLANK:
                cellValue = "";
                break;
            // 故障
            case ERROR:
                cellValue = "非法字符";
                break;
            default:
                cellValue = "未知类型";
                break;
        }

        return cellValue;
    }

    /**
     * 获取单元格内容，如果内容达到Excel单元格最大值32767，则继续读取下一行本列对应单元格内容，以此类推，直到内容没有填满单元格为止
     * @param cell
     * @param map
     */
    public static void getFullCellValue(Cell cell, Map<String, String> map) {
        String value = getCellValue(cell);
        if (StringUtils.isEmpty(map.get("value"))) {
            map.put("value", value);
        } else {
            map.put("value", map.get("value") + value);
        }
        if (map.get("rowCount") == null) {
            map.put("rowCount", "1");
        }

        if (Objects.isNull(cell)) {
            return;
        }

        Sheet sheet = cell.getSheet();
        int rowNum = cell.getRowIndex();
        int cellNum = cell.getColumnIndex();

        Row nextRow = sheet.getRow(rowNum + 1);

        if (Objects.isNull(nextRow)) {
            return;
        }

        //先判断下一行除了改单元格是否有其他值，有则认为是一条数据
        int lastCellNum = nextRow.getLastCellNum();
        //是否是一行数据，只适用于只有一个单元格超长的情况
        boolean isRowData = false;
        for (int i = 0; i < lastCellNum; i++) {
            if (i!=cellNum || i==0) {
                String cellValue = getCellValue(nextRow.getCell(i));
                if (!StringUtils.isEmpty(cellValue)) {
                    isRowData = true;
                    break;
                }
            }
        }

        // 下一行不是一行数据，追加当前单元格下一行数据，则继续读取下一行本列对应单元格的内容
        if (!isRowData) {
            int rowCount = Integer.parseInt(map.get("rowCount"));
            rowCount++;

            map.put("rowCount", String.valueOf(rowCount));
            Cell nextRowCell = nextRow.getCell(cellNum);
            getFullCellValue(nextRowCell, map);
        }
    }

    public static List<List<String>> readExcelSingle(MultipartFile file) throws IOException {
        //检查文件
        checkFile(file);
        //获得Workbook工作薄对象
        Workbook workbook = getWorkBook(file);
        //创建返回对象，默认一个sheet
        List<List<String>> dataList = new ArrayList<>();
        if (workbook != null) {
            //获得当前sheet工作表
            Sheet sheet = workbook.getSheetAt(0);
            //获得当前sheet的开始行
            int firstRowNum = sheet.getFirstRowNum();
            //获得当前sheet的结束行
            int lastRowNum = sheet.getPhysicalNumberOfRows();

            //第一行是头部,取第一行列数作为总数
            Row headRow = sheet.getRow(0);
            int firstCellNum = 0;
            int lastCellNum = headRow.getLastCellNum();

            //循环除了第一行的所有行
            for (int rowNum = firstRowNum + 1; rowNum <= lastRowNum; rowNum++) {
                //获得当前行
                Row row = sheet.getRow(rowNum);
                if (row == null) {
                    continue;
                }
                //获得当前行的开始列(改成0,防止内容列为空的情况)
                firstCellNum = 0;
                //获得当前行的列数
                int dataCellNum = 0;
                // 最大的单元格占用行数（超过单元格最大值时往下一行本列的单元格继续写数据），考虑到一行多个单元格超过最大值时，取占用行数最多的那个
                int maxCellRowCount = 1;
                List<String> list = new ArrayList<>();
                //循环当前行
                for (int cellNum = firstCellNum; cellNum < lastCellNum; cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    String value = getCellValue(cell);
                    Map<String, String> cellMap = new HashMap<>();
                    cellMap.put("rowCount", String.valueOf(1));

                    getFullCellValue(cell, cellMap);

                    list.add(cellMap.get("value"));
                    maxCellRowCount = Math.max(maxCellRowCount, Integer.parseInt(cellMap.get("rowCount")));
                    if(!StringUtils.isEmpty(value)){
                        dataCellNum++;
                    }
                }
                // 跳过因超过单元格最大值而占用的行
                if (maxCellRowCount > 1) {
                    rowNum = rowNum + (maxCellRowCount - 1);
                }
                if(dataCellNum > 0){
                    dataList.add(list);
                }
            }
        }
        workbook.close();
        return dataList;
    }

    /**
     * 重载
     * @return
     * @throws IOException
     */
    public static List<List<String>> readExcelSingle(InputStream is,String fileName) throws IOException {
        //获得Workbook工作薄对象
        Workbook workbook = getWorkBook(is, fileName);
        //创建返回对象，默认一个sheet
        List<List<String>> dataList = new ArrayList<>();
        if (workbook != null) {
            //获得当前sheet工作表
            Sheet sheet = workbook.getSheetAt(0);
            //获得当前sheet的开始行
            int firstRowNum = sheet.getFirstRowNum();
            //获得当前sheet的结束行
            int lastRowNum = sheet.getPhysicalNumberOfRows();

            //第一行是头部,取第一行列数作为总数
            Row headRow = sheet.getRow(0);
            int firstCellNum = 0;
            int lastCellNum = headRow.getLastCellNum();

            //循环除了第一行的所有行
            for (int rowNum = firstRowNum + 1; rowNum <= lastRowNum; rowNum++) {
                //获得当前行
                Row row = sheet.getRow(rowNum);
                if (row == null) {
                    continue;
                }
                //获得当前行的开始列(改成0,防止内容列为空的情况)
                firstCellNum = 0;
                //获得当前行的列数
                int dataCellNum = 0;
                // 最大的单元格占用行数（超过单元格最大值时往下一行本列的单元格继续写数据），考虑到一行多个单元格超过最大值时，取占用行数最多的那个
                int maxCellRowCount = 1;
                List<String> list = new ArrayList<>();
                //循环当前行
                for (int cellNum = firstCellNum; cellNum < lastCellNum; cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    String value = getCellValue(cell);
                    Map<String, String> cellMap = new HashMap<>();
                    cellMap.put("rowCount", String.valueOf(1));

                    getFullCellValue(cell, cellMap);

                    list.add(cellMap.get("value"));
                    maxCellRowCount = Math.max(maxCellRowCount, Integer.parseInt(cellMap.get("rowCount")));
                    if(!StringUtils.isEmpty(value)){
                        dataCellNum++;
                    }
                }
                // 跳过因超过单元格最大值而占用的行
                if (maxCellRowCount > 1) {
                    rowNum = rowNum + (maxCellRowCount - 1);
                }
                if(dataCellNum > 0){
                    dataList.add(list);
                }
            }
        }
        workbook.close();
        return dataList;
    }

    public static SXSSFWorkbook writeExcel(List<Map<String, String>> headList, List<Map> dataList, String sheetName) throws IOException {

        SXSSFWorkbook workbook = new SXSSFWorkbook(100);
        Sheet sheet = workbook.createSheet(sheetName);

        //title
        Row headRow = sheet.createRow(0);
        for (int i = 0; headList != null && i < headList.size(); i++) {
            Cell headCell = headRow.createCell(i);
            headCell.setCellValue(headList.get(i).get("name"));
        }

        //dataList
        // 当前写的行数
        int currenRowNum = 1;
        for (int i = 0; dataList != null && i < dataList.size(); i++, currenRowNum++) {
            // 本行单元格最多占用的行数，考虑到一行多个单元格超过最大值时，取占用行数最多的那个
            int maxCellRowCount = 1;
            Row row = sheet.createRow(currenRowNum);
            for (int j = 0; j < headList.size(); j++) {
                // 当前单元格占用行数（超过单元格最大值往下一行本列单元格继续写，所以一个单元格内容可能占用多行）
                int currentCellRowCount = 1;
                String key = headList.get(j).get("key");
                String value = MapUtils.getString(dataList.get(i), key);
                if (StringUtil.isEmpty(value) && StringUtils.equals("index", key)) {
                    value = String.valueOf(i);
                }
                if (!StringUtils.isEmpty(value) && value.length() > SpreadsheetVersion.EXCEL2007.getMaxTextLength()) {
                    currentCellRowCount = value.length() % SpreadsheetVersion.EXCEL2007.getMaxTextLength() == 0 ? value.length() / SpreadsheetVersion.EXCEL2007.getMaxTextLength() : (value.length() / SpreadsheetVersion.EXCEL2007.getMaxTextLength() + 1);
                    maxCellRowCount = Math.max(currentCellRowCount, maxCellRowCount);
                }
                for (int k = 0; k < currentCellRowCount; k++) {
                    Row cellRow = sheet.getRow(currenRowNum + k);
                    if (cellRow == null) {
                        try {
                            cellRow = sheet.createRow(currenRowNum + k);
                        }catch (Exception e){ // 兼容已经存在的row情况,跳过该行
                            continue;
                        }
                    }
                    Cell cell = cellRow.getCell(j);
                    if (cell == null) {
                        cell = cellRow.createCell(j);
                    }
                    if(StringUtils.isEmpty(value)){
                        cell.setCellValue("");
                    } else{
                        if (k != currentCellRowCount - 1) {
                            cell.setCellValue(value.substring(k*SpreadsheetVersion.EXCEL2007.getMaxTextLength(), (k+1)*SpreadsheetVersion.EXCEL2007.getMaxTextLength()));
                        } else {
                            cell.setCellValue(value.substring(k*SpreadsheetVersion.EXCEL2007.getMaxTextLength(), value.length()));
                        }
                    }
                }
            }
            if (maxCellRowCount > 1) {
                currenRowNum = currenRowNum + (maxCellRowCount - 1);
            }
        }

        return workbook;
    }

    public static SXSSFWorkbook writeExcel(List<List<String>> dataList, String sheetName) throws IOException {

        SXSSFWorkbook workbook = new SXSSFWorkbook(100);
        Sheet sheet = workbook.createSheet(sheetName);

        for (int i = 0; i < dataList.size(); i++) {
            List<String> excelData = dataList.get(i);
            Row rowData = sheet.createRow(i);
            for (int ci = 0; ci < excelData.size(); ci++) {
                Cell cell0 = rowData.createCell(ci);
                cell0.setCellValue(excelData.get(ci).toString());
            }
        }

        return workbook;
    }

    public static void main(String[] args) throws IOException {
//        String filepath = "你的文件地址" //如 D://image/pic.png
//        File file = new File(filepath);
////上传本地项目的图片
//        FileInputStream fileInputStream = new FileInputStream(file);
//
//        MultipartFile file = new MockMultipartFile(file.getName(), file.getName(), ContentType.IMAGE_JPEG.toString(), fileInputStream);
//
//        List<List<String[]>> dataList = ExcelUtil.readExcel(file);
    }
}