package com.github.util;

import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static org.springframework.http.HttpHeaders.SET_COOKIE;

/**
 * @version v1.0
 * @author: leolin
 * @since: 11/01/2019 12:44 PM
 * @description :类描述 cookie管理工具类
 */
public class CookieUtils {

    private static final String COOKIE_SEPARATOR = ";";
    private static final String COOKIE_KEY_VALUE_SEPARATOR = "=";
    private static final String COOKIE_HTTPONLY = "HttpOnly";
    private static final String COOKIE_PATH = "Path";
    private static final String COOKIE_EXPIRES = "Expires";
    private static final String COOKIE_DOMAIN = "Domain";

    public static final String UNAME = "uname";

    //浙江图书馆UID
    public static final String ZJST_UID= "zjst_uid";

    private static final Pattern COOKIE_DOMAIN_PATTERN = Pattern.compile("^\\.");

    // 设置cookie过期，关闭页面将失效。
    public static final int COOKIE_ALIVE_TIME = 7 * 60 * 60 * 24;
    public static final int COOKIE_ALIVE_TIME_TMP = 30;



    public static final String UID = "UID";
    //默认uid
    public static final Integer DEFAULT_UID = 1;

    public static Map<String,Object> getCookie(HttpServletRequest request){
        request.getCookies();
        return null;
    }

    /**
     * 设置 Cookie
     * @param name 名称
     * @param value 值
     * @param path 路径
     */
    public static void setCookie(HttpServletResponse response, String name, String value, String path) {
        setCookie(response, name, value, path, COOKIE_ALIVE_TIME);
    }

    /**
     * 设置 Cookie
     * @param name 名称
     * @param value 值
     * @param path 路径
     */
    public static void setCookieTmp(HttpServletResponse response, String name, String value, String path) {
        setCookie(response, name, value, path, COOKIE_ALIVE_TIME_TMP);
    }


    /**
     * 设置 Cookie
     * @param name 名称
     * @param value 值
     * @param maxAge 生存时间（单位秒）
     * @param path 路径
     */
    public static void setCookie(HttpServletResponse response, String name, String value, String path, int maxAge) {
        Cookie cookie = new Cookie(name, null);
        cookie.setPath(path);
        cookie.setMaxAge(maxAge);
//        cookie.setHttpOnly(true);
        try {
            cookie.setValue(URLEncoder.encode(value, "utf-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.addCookie(cookie);
    }

    /**
     * 设置cookie到指定的域名下。
     * @param response
     * @param name
     * @param value
     * @param path
     * @param domain
     */
    public static void setCookie(HttpServletResponse response, String name, String value, String path, String domain) {
        Cookie cookie = new Cookie(name, null);
        cookie.setPath(path);
        cookie.setMaxAge(COOKIE_ALIVE_TIME);
        cookie.setDomain(domain);
//        cookie.setHttpOnly(true);
        try {
            cookie.setValue(URLEncoder.encode(value, "utf-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.addCookie(cookie);
    }

    /**
     * 设置cookie到指定的域名下并设置过期时间。
     * @param response
     * @param name
     * @param value
     * @param path
     * @param domain
     */
    public static void setCookie(HttpServletResponse response, String name, String value, String path, String domain,int maxAge) {
        Cookie cookie = new Cookie(name, null);
        cookie.setPath(path);
        cookie.setMaxAge(maxAge);
        cookie.setDomain(domain);
//        cookie.setHttpOnly(true);
        try {
            cookie.setValue(URLEncoder.encode(value, "utf-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.addCookie(cookie);
    }

    /**
     * 移除cookie
     * @param response
     * @param name
     * @param path
     * @param domain
     */
    public static void rmCookie(HttpServletResponse response, String name, String path, String domain) {
        Cookie cookie = new Cookie(name, null);
        cookie.setPath(path);
        cookie.setMaxAge(0);
        cookie.setDomain(domain);
        response.addCookie(cookie);
    }

    /**
     * 移除cookie
     * @param response
     * @param name
     * @param path
     */
    public static void rmCookie(HttpServletResponse response, String name, String path) {
        Cookie cookie = new Cookie(name, null);
        cookie.setPath(path);
        cookie.setMaxAge(0);
        response.addCookie(cookie);
    }

    /**
     * 获得指定Cookie的值
     * @param name 名称
     * @return 值
     */
    public static String getCookie(HttpServletRequest request, String name) {
        String value = getCookie(request, null, name, false);
        return value;
    }

    /**
     * 获得指定Cookie的值，并删除。
     * @param name 名称
     * @return 值
     */
    public static String getCookie(HttpServletRequest request, HttpServletResponse response, String name) {
        return getCookie(request, response, name, true);
    }
    /**
     * 获得指定Cookie的值
     * @param request 请求对象
     * @param response 响应对象
     * @param name 名字
     * @param isRemove 是否移除
     * @return 值
     */
    public static String getCookie(HttpServletRequest request, HttpServletResponse response, String name, boolean isRemove) {
        String value = null;
        Cookie[] cookies = request.getCookies();
        // 获取cookies为null，再从header中获取一次。
        if(null == cookies){
            String cookiesStr = request.getHeader("Cookie");
            if(StringUtils.isNotBlank(cookiesStr)){
                // 解析cookie字符串，获取cookie数组
                String[] cookieArr = cookiesStr.split(";");
                cookies = new Cookie[cookieArr.length];
                int flag = 0;
                for(String cookieStr : cookieArr){
                    String[] cookie = cookieStr.split("=");
                    if(cookie.length == 2){
                        if(StringUtils.isNotBlank(cookie[0].trim()) && StringUtils.isNotBlank(cookie[1].trim())) {
                            String cookieName = cookie[0].trim();
                            // 兼容cookieName 非法字符的情况。 这里加不上cookie就pass
                            try {
                                Cookie ck = new Cookie(cookieName, cookie[1].trim());
                                cookies[flag++] = ck;
                            }catch (Exception e){

                            }
                        }
                    }
                }
            }
        }
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (null != cookie && cookie.getName().equals(name)) {
                    try {
                        value = cookie.getValue();
                        if(StringUtils.isNotBlank(value)){
                            value = URLDecoder.decode(value, "utf-8");
                        }
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }
                    if (isRemove) {
                        cookie.setPath("/");
                        cookie.setMaxAge(0);
                        response.addCookie(cookie);
                    }
                }
            }
        }
        return value;
    }

    /**
     * 南浔微信图书馆，回写cookie
     * @param headers
     * @param response
     * @return
     */
    public static List<Cookie> writeCookie(HttpHeaders headers, HttpServletResponse response) {
        List<String> cookieStrs = headers.get(SET_COOKIE);
        List<Cookie> allCookies = new ArrayList<>();
        if (!CollectionUtils.isEmpty(cookieStrs)) {
            cookieStrs.forEach(cookieStr -> {
                String[] keyValues = cookieStr.split(COOKIE_SEPARATOR);
                Boolean httpOnly = false;
                String path = "/";
                String domain = "";
                List<Cookie> cookies = new ArrayList<>();
                for (String keyValue : keyValues) {
                    if (StringUtils.isEmpty(keyValue)) {
                        continue;
                    }
                    String[] keyAndValue = keyValue.split(COOKIE_KEY_VALUE_SEPARATOR);
                    if (keyAndValue.length > 1) {
                        String key = keyAndValue[0];
                        if (!StringUtils.isEmpty(key)) {
                            key = key.trim();
                        }
                        String value = keyAndValue[1];
                        if (!StringUtils.isEmpty(value)) {
                            value = value.trim();
                        }
                        if (COOKIE_PATH.equalsIgnoreCase(key)) {
                            path = value;
                            continue;
                        }
                        if (COOKIE_DOMAIN.equalsIgnoreCase(key)) {
                            domain = value;
                        }
                        if (COOKIE_EXPIRES.equalsIgnoreCase(key)) {
                            continue;
                        }
                        Cookie cookie = new Cookie(key, value);
                        cookies.add(cookie);
                    } else if (keyAndValue.length == 1 && keyAndValue[0].equalsIgnoreCase(COOKIE_HTTPONLY)) {
                        httpOnly = true;
                    }
                }
                for (Cookie cookie : cookies) {
                    cookie.setHttpOnly(httpOnly);
                    cookie.setPath(path);
                    domain = domain.replaceAll(COOKIE_DOMAIN_PATTERN.pattern(), "");
                    cookie.setDomain(domain);
                    response.addCookie(cookie);
                }
                allCookies.addAll(cookies);
            });
        }
        return allCookies;
    }

    public static String getCookieRole(HttpServletRequest request) {
        String roleId = getCookie(request, Constants.COOKIE_MHR);
        if (StringUtils.isEmpty(roleId)) {
            roleId = getCookie(request, Constants.COOKIE_SPACE_R);
        }
        return roleId;
    }

    /**
     * 获取当前微服务的fid
     */
    public static Integer getCookieUfid(HttpServletRequest request) {
        String value = getCookie(request,  Constants.COOKIE_WFWFID);
        if(StringUtils.isEmpty(value)){
            value = getCookie(request, Constants.COOKIE_FID);
        }
        if(StringUtils.isBlank(value)){
            return 0;
        }
        return Integer.valueOf(value);
    }

}
