package com.github.util;

/**
 * @className RSAUtils
 * @Description 门户登录应用，模板 登录账号密码加密解密工具
 * <AUTHOR>
 * @Date 2022/5/17 18:44
 * @Version 1.0
 **/

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

public class RSAUtils {
    public static Integer ENCRYPT_STR_LEN = 344 ;
    private static Map<Integer, String> keyMap = new HashMap<Integer, String>();
    public static String pubKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvWQlaLkvEritHDVuqodqozBtlgkiCIOdnNYp61X7thI9ST0e0dIAWBBMuiQMJmDR0HzX5FaWOJNwlnWbjbNR36m+DGql++crnhn5lyYm0GkewPnbWLMVxHGdMDikpiGZlSO7HJBWJU3LBqGZbbVQiYhp6iZJx0mG9VlPbeBJ/E111QmSLj2B8Skxu+kTUHRPYqNU4qyff5D15H0vLguxj6zmG5B5gf5xESpAn+1DQ+Y876Ebxg1fVI5gvbZHYxv2RhoupXeKve3Le9vy1MmL7u9/4h3qjH3nAma9DvUiZzCFWt+uuvGsbxoOYZVRfLpKZ997rnoHvvwoFr0QuOXTCwIDAQAB";
    public static String privateKey = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC9ZCVouS8SuK0cNW6qh2qjMG2WCSIIg52c1inrVfu2Ej1JPR7R0gBYEEy6JAwmYNHQfNfkVpY4k3CWdZuNs1Hfqb4MaqX75yueGfmXJibQaR7A+dtYsxXEcZ0wOKSmIZmVI7sckFYlTcsGoZlttVCJiGnqJknHSYb1WU9t4En8TXXVCZIuPYHxKTG76RNQdE9io1TirJ9/kPXkfS8uC7GPrOYbkHmB/nERKkCf7UND5jzvoRvGDV9UjmC9tkdjG/ZGGi6ld4q97ct72/LUyYvu73/iHeqMfecCZr0O9SJnMIVa36668axvGg5hlVF8ukpn33uuege+/CgWvRC45dMLAgMBAAECggEBAILf+ZnsnkLpq3aa1eaxvVcX2CKeHQ6VJQ2EUWN2/4JKCF/cBLgpzmMMoHjhACRlM5hHLUl1LEncWSkgDGZXTHSq79HUwO7aSKHwgTGaA6hxe/cOsi0RVDFz8ZOpEN5cyazuDBcpBt6wa2Qht8zARu9DfS15V/KHNOPG28SqAXZ/Pk4tcLAb/9BWUuLXYj0tEjPfMu6gXIKO+FfzapaqBP3vpwBFPtXeVh1QAiDYaV8+SNh6JT7VZ5C28vS2SRan14Xo4Pq0a035JX6+cDV/w1PHg52q3RqfLrOiMvBlxegS69x2aQdVYHfMvdYk3av0oi/9mym5egZP+Wj3gTJgQwECgYEA94Ks0wYyhNea1nlU/In7no9cOnKiRK60fyOmEQcIpA6Uo800z2VUv5t0Iu5G5oOKfx21XaY8IFMJMjQ8sDr9ad+kQmOOw0kMNtg7m/y9jPVjyi9T9rC+JmoYNvc7nkiVZtUZyLsVeK86Q0OWGbj2lBBN12lghr9d00tu+1U8pLECgYEAw+MkFyBfvAvo30FvYq093F5Tl1ouBFXiPGDQOs+prIyZnEChw1+PzcsOKnEPFU2WcFEoZdiUBmYlm4u6obXt5nMHrb2mimTNZGNjqFs8MpkSYiUHCAqepH8RZGNJPX7VQIhiECiovbEOqA3WaAL4rWhBwyj9oEsJdoYzeyCiUnsCgYEAjltsh5si52ESv5TdmrrKLDKMe1l/lz1ct1uLonOHNsTkHSpj232Otv30WDrwMJkryV0O+aQpJPZdbbC5zyq+omxtD3ovnIahLdJExr0hULe7A4khI9wYIgQXlVWxxqI8u/6Ja/iequEuA44WAQlvWU3fV6xWRmo+zs6uMISf8qECgYBT/JhlvgCt/KE8z651LXgfeuX2N7kRLM2SO6QsAwtpquLVprR0sw1SG+SX99+Sr7B3uaRLfn+A+tzXAyLi4MD+lIVPTcgRAEU77I03Ct8VhqHmKKAKJ5t7RevsEdG+JxpBz/fTm8s1gauaDEzITbXXi6LejT2pM2L8SW/bM9PE7wKBgQCrbYU4DnG0DOAB/VIv4auivAmkRXoGiW3Rwn6iksdVE3Ham7ekH3sEXUzhoKEuyDVFbXPOxTomhJJ89hm9UiZgLbgnc8RZQIKusLGiARdtnydzCrXGHOX06p0j2/8wstw42BQ78Ti65PpHW4HOSC8Eihw68zYQR68Ip8y60e70YQ==";

    public static void main(String[] args) throws Exception {
        //生成公钥和私钥
//        genKeyPair();

        //加密字符串
        String message = "df7238211111110";
        String messageEn = encrypt(message, pubKey);
        System.out.println(message + "\t加密后的字符串为:" + messageEn);

        String messageDe = decrypt(messageEn, privateKey);
        System.out.println("还原后的字符串为:" + messageDe);


        String s1 = "dI1O/I1QmDLHhKb6YsvHPlwLrgBryIv5is400/aTvDdqa2V7sic42H3s5na5UZ2LiEYS6+fgmVTo2Q2zKRic6KfeV+9VyjJqCagExTEFGf+Z1ynV6hiIHPWCfujH423AFYvG6jxIhumej29SjZJCdvcPZnmAij+ay55mg7Oj436XHEMV6Dur3JlTvoHNyZD8UmfvcoGzWLEJdVy7IrMmOHhx/lZZdgwQegQQzi/GKlqcPK8iirmtIak34Uc3kHZe210sivHPw6FPg/xZfif0uvzxlCu/LjeyfOrIKaJ+pQATiO91sKDm7n4F3n29GkMqkCKtBU8AepgncyULozffKA==";

        String messageDe2 = decrypt(s1, privateKey);
        System.out.println("还原后的字符串为:" + messageDe2);
    }

    /**
     * 随机生成密钥对
     *
     * @throws NoSuchAlgorithmException
     */
    public static void genKeyPair() throws NoSuchAlgorithmException {
        // KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        // 初始化密钥对生成器，密钥大小为96-1024位
        keyPairGen.initialize(1024, new SecureRandom());
        // 生成一个密钥对，保存在keyPair中
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();   // 得到私钥
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();  // 得到公钥
        String publicKeyString = new String(Base64.encodeBase64(publicKey.getEncoded()));
        // 得到私钥字符串
        String privateKeyString = new String(Base64.encodeBase64((privateKey.getEncoded())));
        // 将公钥和私钥保存到Map
        keyMap.put(0, publicKeyString);  //0表示公钥
        keyMap.put(1, privateKeyString);  //1表示私钥
    }

    /**
     * RSA公钥加密
     *
     * @param str       加密字符串
     * @param publicKey 公钥
     * @return 密文
     * @throws Exception 加密过程中的异常信息
     */
    public static String encrypt(String str, String publicKey) throws Exception {
        //base64编码的公钥
        byte[] decoded = Base64.decodeBase64(publicKey);
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
        //RSA加密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        String outStr = Base64.encodeBase64String(cipher.doFinal(str.getBytes("UTF-8")));
        return outStr;
    }

    /**
     * RSA私钥解密
     *
     * @param str        加密字符串
     * @param privateKey 私钥
     * @return 铭文
     * @throws Exception 解密过程中的异常信息
     */
    public static String decrypt(String str, String privateKey) throws Exception {
        //64位解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
        //base64编码的私钥
        byte[] decoded = Base64.decodeBase64(privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;
    }

}

