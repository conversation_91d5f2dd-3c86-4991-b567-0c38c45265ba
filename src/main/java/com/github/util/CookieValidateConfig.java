package com.github.util;

import org.apache.commons.lang.StringUtils;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Writer;
import java.util.HashSet;
import java.util.Set;


@Configuration
public class CookieValidateConfig {

    @Bean
    public FilterRegistrationBean authFilterRegistrationBean() {
        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        registrationBean.setName("cookieValidateFilter");
        registrationBean.setFilter(new CookieValidateFilter());
        registrationBean.addUrlPatterns("/t/*");
        return registrationBean;
    }
}


class CookieValidateFilter extends OncePerRequestFilter {

    public static final String PUBLIC_KEY_STRING = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCAZ2WX9e0wrotq7ynUPhl6ISkaY0xNTS1UxvsBhYgTTqp9tm/UJJfUaghqH2LtaJnpun1DYUt2xTfJSktdxEJO5326bKWZVbuBJhSthjrRlWngGlmFGbNaEx4l8ASfMzlZjs/G5Jwh8AvF+ZVLo/YfpS7n9xKbvtwGQBQ6yuNK0QIDAQAB";
    private static final Set<String> EXCLUDE_PATHS = new HashSet<>();

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        String path = request.getRequestURI().substring(request.getContextPath().length()).replaceAll("[/]+$", "");
        if (!EXCLUDE_PATHS.contains(path)) {
            if (StringUtils.isBlank(CookieValidateUtils.valid(request))) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                Writer writer = response.getWriter();
                writer.write("{\"code\":0,\"description\":\"\",\"message\":\"未授权访问\",\"requestId\":\"\",\"status\":200}");
                writer.flush();
                return;
            }
        }

        filterChain.doFilter(request, response);
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        return super.shouldNotFilter(request);
    }

}
