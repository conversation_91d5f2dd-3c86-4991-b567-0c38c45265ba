package com.github.util;

import org.apache.commons.lang.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.TemporalField;
import java.time.temporal.WeekFields;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description：数据源包装工具类
 * @date 2021-11-11 23:49
 * @modified By：
 */
public final class MhApiUtil {

    /**
     * 获取preParams中的参数
     * @param preParams
     * @param param
     * @return
     */
    public static String getValueOfPreparams(String preParams, String param) {
        if (StringUtils.isNotBlank(preParams)) {
            String[] split = preParams.split("&");
            for (String params : split) {
                if (params.split("=")[0].equals(param)) {
                    return params.split("=")[1];
                }
            }
        }
        return "";
    }

    /**
     * 获取preParams中的参数
     * @param preParams
     * @param param
     * @param defaultValue
     * @return
     */
    public static String getValueOfPreparams(String preParams, String param,String defaultValue) {
        if (StringUtils.isNotBlank(preParams)) {
            String[] split = preParams.split("&");
            for (String params : split) {
                if (params.split("=")[0].equals(param)) {
                    return params.split("=")[1];
                }
            }
        }
        return StringUtils.isEmpty(defaultValue)? "" : defaultValue;
    }


    /**
     * url拼接参数
     * @param builder
     * @param key
     * @param value
     */
    public static void appendUrlParam(StringBuilder builder,String key,String value) {
        if (!StringUtils.isBlank(value)) {
            builder.append("&").append(key).append("=").append(value);
        }
    }

    /**
     * 添加field
     *
     * @param key
     * @param value
     * @param orsUrl
     * @param flag
     * @param type
     * @return
     */
    public static Map<String, Object> addFieldMap(String key, Object value, String orsUrl, int flag, int type) {
        Map<String, Object> map = new HashMap<>();
        map.put("key", key);
        map.put("name", key);
        map.put("value", value);
        map.put("orsUrl", orsUrl);
        map.put("flag", flag);
        map.put("type", type);
        return map;
    }


    /**
     * 根据日期类型获取起始时间
     *
     * @param timeType
     */
    public static Map<String, String> getTimeParam(String timeType) {
        Map<String, String> result = new HashMap<>(2);
        LocalDate today = LocalDate.now();
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startTime = "";
        if (Objects.equals("1",timeType)) {
            //1-今日
            startTime = today.format(dateFormat);
        } else if (Objects.equals("2",timeType)) {
            TemporalField fieldISO = WeekFields.of(Locale.FRANCE).dayOfWeek();
            startTime = today.with(fieldISO, 1).toString();
        } else if (Objects.equals("3",timeType)) {
            //3-近1周 （7天）
            startTime = today.minusDays(7).toString();
        } else if (Objects.equals("4",timeType)) {
            //4-本月 01开始
            startTime = today.with(TemporalAdjusters.firstDayOfMonth()).toString();
        } else if (Objects.equals("5",timeType)) {
            //5-近30天
            startTime = today.minusDays(30).toString();
        } else if (Objects.equals("6",timeType)) {
            //6-近3个月 3个月
            startTime = today.minusMonths(3).format(dateFormat);
        } else if (Objects.equals("7",timeType)) {
            //7-近半年 6个月
            startTime = today.minusMonths(6).format(dateFormat);
        } else if (Objects.equals("8",timeType)) {
            //8-本年 01-01 开始
            startTime = today.with(TemporalAdjusters.firstDayOfYear()).toString();
        } else if (Objects.equals("9",timeType)) {
            //9-近1年 （365天）
            startTime = today.minusDays(365).toString();
        }else if (Objects.equals("10",timeType)) {
            //10-昨日
            startTime = today.minusDays(1).toString();
        }else if(Objects.equals("-1",timeType)){
            //全部
            startTime = null;
        }
        String endTime = today.format(dateFormat);
        result.put("startDate", startTime);
        result.put("endDate", endTime);
        return result;
    }

}
