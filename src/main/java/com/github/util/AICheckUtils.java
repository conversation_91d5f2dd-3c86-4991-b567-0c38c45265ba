package com.github.util;

import com.alibaba.fastjson.JSONObject;
import com.github.conf.NoCookieRestTemplateCustomizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;

/**
 * @className AICheckUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/3 11:52
 * @Version 1.0
 **/
public class AICheckUtils {
    static Logger logger = LoggerFactory.getLogger(AICheckUtils.class);
    /**
     * 检查文本内容
     * @param content
     * @return
     */
    public static String checkText(String content){
        String url = Constants.AI_CHECK_TEXT_URL;

        JSONObject param = new JSONObject();
        param.put("document",content);
        param.put("multilingual",true);
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-api-key",Constants.AI_CHECK_API_KEY);
        headers.set("Content-Type", "application/json");

        HttpEntity<String> httpEntity = new HttpEntity<>(param.toJSONString(), headers);
        logger.info("开始文本接口请求");
        Long t1 = System.currentTimeMillis();
        RestTemplate restTemplateNoCookie = new RestTemplateBuilder().additionalCustomizers(new NoCookieRestTemplateCustomizer()).build();
        String responseContent = restTemplateNoCookie.postForObject(url, httpEntity, String.class);
        logger.info("完成执行文本接口请求，耗时："+(System.currentTimeMillis() - t1));
        return responseContent;
    }

    /**
     * 文本检查，内部接口
     * @param content
     * @return
     */
    public static String checkTextInner(String content){
        String url = "https://test-aigcdetect.chaoxing.com/api/nlp/gc/v1/txt";

        JSONObject param = new JSONObject();
        param.put("text",content);
        param.put("threshold",0.5);
        param.put("min_words",Constants.MIN_AI_CHECK_TEXT_LENGTH);
        param.put("max_words",Constants.MAX_AI_CHECK_TEXT_LENGTH);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(param.toJSONString(), headers);
        logger.info("开始文本接口请求");
        Long t1 = System.currentTimeMillis();
        RestTemplate restTemplateNoCookie = new RestTemplateBuilder().additionalCustomizers(new NoCookieRestTemplateCustomizer()).build();
        JSONObject responseContent = restTemplateNoCookie.postForObject(url, httpEntity, JSONObject.class);
        logger.info("完成执行文本接口请求，耗时："+(System.currentTimeMillis() - t1));
        String requestId = responseContent.getString("request_id");
        return requestId;
    }

    /**
     * 校验文件
     * @param filePath
     * @return
     */
    public static String checkFile(String filePath){
        File file = new File(filePath);
        if(!file.exists() || !file.isFile()) {
            throw new RuntimeException("文件不存在或不是一个文件！");
        }
        // 调用aicheck 接口进行文件解析
        String url = Constants.AI_CHECK_FILE_URL;
        MultipartBodyBuilder builder = new MultipartBodyBuilder();

        builder.part("files", new FileSystemResource(file));
        MultiValueMap<String, HttpEntity<?>> multipartBody = builder.build();

        HttpHeaders headers = new HttpHeaders();
        headers.set("x-api-key",Constants.AI_CHECK_API_KEY);
        headers.set("Accept", "application/json");
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        HttpEntity<MultiValueMap<String,HttpEntity<?>>> httpEntity = new HttpEntity<>(multipartBody, headers);
        logger.info("开始执行文件接口请求：");
        Long t1 = System.currentTimeMillis();
        RestTemplate restTemplateNoCookie = new RestTemplateBuilder().additionalCustomizers(new NoCookieRestTemplateCustomizer()).build();
        String responseContent = restTemplateNoCookie.postForObject(url, httpEntity, String.class);
        logger.info("开始执行文件接口请求，耗时："+(System.currentTimeMillis() - t1));
        // 解析成功后删除源文件
        file.delete();
        return responseContent;
    }

    /**
     * 校验文件 公司内部接口
     * @param filePath
     * @return
     */
    public static String checkFileInner(String filePath){
        File file = new File(filePath);
        if(!file.exists() || !file.isFile()) {
            throw new RuntimeException("文件不存在或不是一个文件！");
        }
        // 调用aicheck 接口进行文件解析
        String url = "https://test-aigcdetect.chaoxing.com/api/nlp/gc/v1/file";
        MultipartBodyBuilder builder = new MultipartBodyBuilder();

        builder.part("file", new FileSystemResource(file));
        builder.part("threshold",0.5);
        builder.part("min_words",Constants.MIN_AI_CHECK_TEXT_LENGTH);
        builder.part("max_words",Constants.MAX_AI_CHECK_TEXT_LENGTH);
        MultiValueMap<String, HttpEntity<?>> multipartBody = builder.build();

        HttpHeaders headers = new HttpHeaders();
//        headers.set("x-api-key",Constants.AI_CHECK_API_KEY);
        headers.set("Accept", "application/json");
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        HttpEntity<MultiValueMap<String,HttpEntity<?>>> httpEntity = new HttpEntity<>(multipartBody, headers);
        logger.info("开始执行文件接口请求：");
        Long t1 = System.currentTimeMillis();
        RestTemplate restTemplateNoCookie = new RestTemplateBuilder().additionalCustomizers(new NoCookieRestTemplateCustomizer()).build();
        RestTemplate restTemplate = new RestTemplate();
        JSONObject resultJson = restTemplate.postForObject(url, httpEntity, JSONObject.class);
        logger.info("开始执行文件接口请求，耗时："+(System.currentTimeMillis() - t1));
        // 解析成功后删除源文件
        file.delete();
        String requestId = resultJson.getString("request_id");
        return requestId;
    }

    public static void main(String[] args) {

        String text = "我爱你111";
        System.out.println(checkTextInner(text));

        String filePath = "/Users/<USER>/java/wkspace/ai-content-check/2.txt";
        checkFileInner(filePath);
    }

}
