package com.github.util;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class DateUtils {
    //文件日志
    private final static Log log = LogFactory.getLog(DateUtils.class);
    public static final String FORMATSTR = "yyyy-MM-dd";

    public static final String FORMATSTRYMD = "yyyyMMdd";

    public static final String FORMAT_STRYMD = "yyyy年MM月dd日";

    public static final String FORMATSTRYM = "yyyy-MM";

    public static final String FORMATTZ = "yyyy-MM-dd'T'hh:mm:ss'Z'";
    public static final String FORMATT = "yyyy-MM-dd'T'hh:mm:ss";

    public static final String FORMATHH = "yyyy-MM-dd HH:mm:ss";

    public static final String FORMATHM = "yyyy-MM-dd HH:mm";
    public static final String FORMATHM2 = "yyyy-MM-ddHH:mm";

    public static final String FORMSTAMP = "yyyyMMddHHmmss";

    private static ThreadLocal<Map<String, SimpleDateFormat>> threadLocal = new ThreadLocal<Map<String, SimpleDateFormat>>() {
        @Override
        protected Map<String, SimpleDateFormat> initialValue() {
            return new HashMap<String, SimpleDateFormat>();
        }
    };

    public static SimpleDateFormat getSimpleDateFormate(String str) {
        SimpleDateFormat sdf = threadLocal.get().get(str);
        if (null != sdf) {
            return sdf;
        } else {
            sdf = new SimpleDateFormat(str);
            threadLocal.get().put(str, sdf);
        }
        return sdf;
    }

    public static SimpleDateFormat getSimpleDateFormate(String str, Locale local) {
        String strFlag = str + "_local";
        SimpleDateFormat sdf = threadLocal.get().get(strFlag);
        if (null != sdf) {
            return sdf;
        } else {
            sdf = new SimpleDateFormat(str, local);
            threadLocal.get().put(strFlag, sdf);
        }
        return sdf;
    }

    public static String date2String(Date date, String formatStr) {
        return getSimpleDateFormate(formatStr).format(date);
    }

    public static Date string2Date(String dateStr, String formatStr) throws ParseException {
        if (formatStr == null) {
            return getSimpleDateFormate(FORMATSTR).parse(dateStr);
        }
        return getSimpleDateFormate(formatStr).parse(dateStr);
    }

    public static Date string2DateTZ(String dateStr) throws ParseException {
//		dateStr = dateStr.replace("T", " ");
//		dateStr = dateStr.replace("Z", "");

        Date d = null;
        try {
            d = getSimpleDateFormate(FORMATTZ).parse(dateStr);
        } catch (Exception e) {
            d = getSimpleDateFormate(FORMATT).parse(dateStr);
        }
        return d;
    }

    /**
     * 得到年月的 时间格式
     *
     * @param dateStr
     * @return
     * @throws ParseException
     */
    public static Date string2DateYM(String dateStr) throws ParseException {
        return getSimpleDateFormate(FORMATSTRYM).parse(dateStr);
    }

    public static Date string2DateYMD(String dateStr) throws ParseException {
        return getSimpleDateFormate(FORMATSTRYM).parse(dateStr);
    }

    public static Date getNowDate() {
        Date date = null;
        SimpleDateFormat sdf = getSimpleDateFormate(FORMATHH);
        try {
            date = string2Date(sdf.format(Calendar.getInstance().getTime()), FORMATHH);
        } catch (ParseException e) {
            log.error("系统时间转换失败!");
        }
        return date;

    }

    public static Long getTimeDifference(Date date1, Date date2) {
        long l1 = Long.valueOf(date2String(date1, FORMSTAMP));
        long l2 = Long.valueOf(date2String(date2, FORMSTAMP));
        return Math.abs(l1 - l2);
    }

    /**
     * 获取当前时间和1970-01-01之间的总分钟数
     *
     * @return
     */
    public static long getTimeDiff() {
        String start = "1970-01-01";
        SimpleDateFormat sdf = getSimpleDateFormate(FORMATSTR);
        try {
            Date d = sdf.parse(start);
            Date dt = new Date();
            long diff = dt.getTime() - d.getTime();
            return diff / (1000 * 60);
        } catch (ParseException e) {
        }
        return 0;
    }

    /**
     * 计算月数
     *
     * @param date1 <String>
     * @param date2 <String>
     * @return int
     * @throws ParseException
     */
    public static int getMonthsByStr(String date1, String date2)
            throws ParseException {
        SimpleDateFormat sdf = getSimpleDateFormate("yyyy-MM");
        return getMonths(sdf.parse(date1), sdf.parse(date2));
    }

    /**
     * 计算月数
     *
     * @return int
     * @throws ParseException
     */
    public static int getMonths(Date beginDate, Date endDate) {

        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(beginDate);

        c2.setTime(endDate);
        int elapsed = 0;
        if (c1.after(c2)) {
            Calendar c3;
            c3 = c2;
            c2 = c1;
            c1 = c3;
        }
        c1.clear(Calendar.MILLISECOND);
        c1.clear(Calendar.SECOND);
        c1.clear(Calendar.MINUTE);
        c1.clear(Calendar.HOUR_OF_DAY);
        c1.clear(Calendar.DATE);
        c2.clear(Calendar.MILLISECOND);
        c2.clear(Calendar.SECOND);
        c2.clear(Calendar.MINUTE);
        c2.clear(Calendar.HOUR_OF_DAY);
        c2.clear(Calendar.DATE);
        while (c1.before(c2) || c1.equals(c2)) {
            c1.add(Calendar.MONTH, 1);
            elapsed++;
        }
        return elapsed;
    }

    /**
     * 得到当月的第一天 0时0分0秒
     *
     * @return
     */
    public static Date getFirstDayOfMonth() {
        Calendar cal_1 = Calendar.getInstance();//获取当前日期
//        cal_1.add(Calendar.MONTH, -1);
        cal_1.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天 0点0分第一秒开始的。
        cal_1.set(Calendar.HOUR_OF_DAY, 0);
        cal_1.set(Calendar.MINUTE, 0);
        cal_1.set(Calendar.SECOND, 1);
        Date date = cal_1.getTime();
        return date;
    }

    /**
     * 标准化格式化日期时间
     *
     * @param date
     * @return
     */
    public static String formatDate(Date date) {
        SimpleDateFormat format = getSimpleDateFormate(FORMATSTR);
        return format.format(date);
    }

    public static String formatDate(Date date, String formatStr) {
        SimpleDateFormat format = getSimpleDateFormate(formatStr);
        return format.format(date);
    }


    /**
     * 将yyyy-MM-dd格式的字符串转换成时间
     *
     * @param dateStr
     * @return
     */
    public static Date strToDate2(String dateStr) {
        SimpleDateFormat format = getSimpleDateFormate(FORMATSTR);
        Date date = null;
        try {
            date = format.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 得到当前时间的年月日部分
     *
     * @return
     * @throws ParseException
     */
    public static Date getDate() throws ParseException {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat format = getSimpleDateFormate(DateUtils.FORMATSTR);
        return format.parse(calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH) + 1) + "-" + calendar.get(Calendar.DAY_OF_MONTH));
    }

    /**
     * 时间格式化（秒转换成00:00:00）
     *
     * @param ms
     * @return
     */
    public static String formatTime(long ms) {
        int ss = 1000;
        int mi = ss * 60;
        int hh = mi * 60;
        int dd = hh * 24;

        long day = ms / dd;
        long hour = (ms - day * dd) / hh;
        long minute = (ms - day * dd - hour * hh) / mi;
        long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        String strHour = hour < 10 ? "0" + hour : "" + hour;//小时
        String strMinute = minute < 10 ? "0" + minute : "" + minute;//分钟
        String strSecond = second < 10 ? "0" + second : "" + second;//秒
        if ("00".equals(strHour)) {
            return strMinute + ":" + strSecond;
        }
        return strHour + ":" + strMinute + ":" + strSecond;
    }

    /**
     * 时间格式化（秒转换成00:00:00）
     * 传入字符串。取整
     *
     * @param sString
     * @return
     */
    public static String ss2mi(String sString) {
        String[] ss = sString.split("\\.");
        sString = ss[0];
        Long ms = Long.valueOf(sString);
        int mi = 60;
        int hh = mi * 60;
        int dd = hh * 24;

        long day = ms / dd;
        long hour = (ms - day * dd) / hh;
        long minute = (ms - day * dd - hour * hh) / mi;
        long second = ms - day * dd - hour * hh - minute * mi;
        String strHour = hour < 10 ? "0" + hour : "" + hour;//小时
        String strMinute = minute < 10 ? "0" + minute : "" + minute;//分钟
        String strSecond = second < 10 ? "0" + second : "" + second;//秒
        if ("00".equals(strHour)) {
            return strMinute + ":" + strSecond;
        }
        return strHour + ":" + strMinute + ":" + strSecond;
    }

    public static String getNowDateString() {
        String date = null;
        SimpleDateFormat sdf = getSimpleDateFormate(FORMATHH);
        date = sdf.format(Calendar.getInstance().getTime());
        return date;

    }

    /**
     * 获取传入时间到现在的天数  传入时间小于当前天
     *
     * @param d2
     * @return
     */
    public static Integer subTimeByNow(Date d2) {
        if (d2 == null)
            return 1;
        Date d1 = new Date();
        Long leftTime = (d1.getTime() - d2.getTime()) % (1000 * 60 * 60 * 24l);
        Long t = (d1.getTime() - d2.getTime()) / (1000 * 60 * 60 * 24l);
        if (leftTime > 0) {
            t += 1;
        }
        return t.intValue();
    }

    /**
     * 时间转化为LONG
     *
     * @param dateStr
     * @return
     */
    public static Long getDatestamp(String dateStr) {
        Long timestamp = null;
        try {
            if (StringUtils.isNotBlank(dateStr)) {
                timestamp = getSimpleDateFormate("yyyy-MM-dd HH:mm:ss").parse(dateStr).getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timestamp;
    }

    public static Long getDateStampByFormatter(String dateStr, String formatter) {
        Long timestamp = null;
        try {
            if (StringUtils.isNotBlank(dateStr)) {
                timestamp = org.apache.commons.lang.time.DateUtils.parseDate(dateStr, new String[]{formatter}).getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timestamp;
    }


    /**
     * 当前时间转为为String
     *
     * @return
     */
    public static String getCurrDateStr() {
        return date2String(new Date(), FORMATHM);
    }

    /**
     * 当前时间转为为String
     *
     * @return
     */
    public static String getCurrDateStr2() {
        return date2String(new Date(), FORMATHM2);
    }


    /**
     * 获取某个时间的前1分钟时间
     *
     * @return
     */
    public static String getAfterTimeDateStr() throws Exception {
        SimpleDateFormat sdf = getSimpleDateFormate(FORMATHM);
        Calendar beforeTime = Calendar.getInstance();
        beforeTime.setTime(string2Date(getCurrDateStr(), FORMATHM));
        beforeTime.add(Calendar.MINUTE, -1);// 1分钟之前的时间
        Date beforeD = beforeTime.getTime();
        return sdf.format(beforeD);
    }

    public static String getWebVersion() {
        Date date = new Date();
        String result = formatDate(date, "yyyyMMdd");
//        result = ymd + date.getHours()/2;
        return result;
    }

    public static Date parseDate(String str, String[] parsePatterns) throws ParseException {
        return org.apache.commons.lang.time.DateUtils.parseDate(str, parsePatterns);
    }

    public static Date addDays(Date date, int amount) {
        return org.apache.commons.lang.time.DateUtils.add(date, Calendar.DAY_OF_MONTH, amount);
    }

}
