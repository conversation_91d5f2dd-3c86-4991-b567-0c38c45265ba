package com.github.util;

import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpHeaders;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.StringTokenizer;

/**http响应工具类
 * @className: HttpServletResponseUtils
 * @description:
 * @author: xhl
 * @date: 2019-04-08 16:59:51
 * @version: ver 1.0
 */
public class HttpServletResponseUtils {
    
    /**回写cookie
     * @Description: 
     * @author: xhl
     * @Date: 2019-04-08 17:10:32
     * @param: response
     * @param: httpServletResponse
     * @return: void
     */
    public static void writeBackCookie(HttpHeaders headers, HttpServletResponse httpServletResponse, String domain) {
        List<String> cookieList = headers.get("Set-Cookie");
        if (cookieList != null && !cookieList.isEmpty()) {
            cookieList.forEach(header -> writeBackCookie(header, httpServletResponse, domain));
        }
    }

    /**回写cookie
     * @Description: 
     * @author: xhl
     * @Date: 2019-04-08 17:01:01
     * @param: cookieHeader
     * @param: response
     * @return: void
     */
    public static void writeBackCookie(String cookieHeader, HttpServletResponse response, String domain) {
        Cookie cookie;
        StringTokenizer tokenizer = new StringTokenizer(cookieHeader, ";");
        String nameValuePair = tokenizer.nextToken();
        int index = nameValuePair.indexOf('=');
        if (index != -1) {
            String name = nameValuePair.substring(0, index).trim();
            String value = nameValuePair.substring(index + 1).trim();

            // passport的cookie不要
            if (StringUtils.equalsIgnoreCase(name, "route")) {
                return;
            }
            cookie = new Cookie(name, value);
        } else {
            throw new IllegalArgumentException("Invalid cookie name-value pair");
        }

        boolean isChaoxingDomain = false;
        if (StringUtils.contains(domain, "chaoxing.com")) {
            isChaoxingDomain = true;
        }

        while (tokenizer.hasMoreTokens()) {
            nameValuePair = tokenizer.nextToken();
            index = nameValuePair.indexOf('=');
            String name, value;
            if (index != -1) {
                name = nameValuePair.substring(0, index).trim();
                value = nameValuePair.substring(index + 1).trim();
            } else {
                name = nameValuePair.trim();
                value = null;
            }
            switch (name) {
                case "Domain":
                    if (value.startsWith(".")) {
                        value = value.substring(1, value.length());
                    }
                    if (isChaoxingDomain) {
                        cookie.setDomain(value);
                    } else {
                        if (StringUtils.isNotBlank(domain)) {  //domain 非空情况下才设置值。
                            cookie.setDomain(domain);
                        }
                    }
                    break;
                case "Path":
                    cookie.setPath(value);
                    break;
                case "HttpOnly":
                    cookie.setHttpOnly(false);
                    break;
                case "Expires":

                    SimpleDateFormat dateFormat = DateUtils.getSimpleDateFormate("EEE, dd/MMM/yyyy HH:mm:ss 'GMT'", Locale.ENGLISH);
                    try {
                        if (StringUtils.contains(value, "-")) {
                            dateFormat = DateUtils.getSimpleDateFormate("EEE, dd-MMM-yyyy HH:mm:ss 'GMT'", Locale.ENGLISH);
                        }
                        Date d1 = dateFormat.parse(value);
                        long times = (d1.getTime() - System.currentTimeMillis()) / 1000L;
                        cookie.setMaxAge((int) times);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    break;
                default:
                    // ignore...
            }
        }
        response.addCookie(cookie);
    }

    /**
     * 获取cookie的FID值
     * @param cookieHeader
     */
    public static Integer getCookieFid(String cookieHeader) {
        StringTokenizer tokenizer = new StringTokenizer(cookieHeader, ";");
        String nameValuePair = tokenizer.nextToken();
        int index = nameValuePair.indexOf('=');
        if (index != -1) {
            String name = nameValuePair.substring(0, index).trim();
            String value = nameValuePair.substring(index + 1).trim();
            if(name.equals(Constants.COOKIE_FID)){
                return Integer.valueOf(value);
            }
        } else {
            throw new IllegalArgumentException("Invalid cookie name-value pair");
        }
        return 0;
    }

    /**
     * 获取cookie的FID值
     * @param cookieHeader
     */
    public static Integer getCookieUid(String cookieHeader) {
        StringTokenizer tokenizer = new StringTokenizer(cookieHeader, ";");
        String nameValuePair = tokenizer.nextToken();
        int index = nameValuePair.indexOf('=');
        if (index != -1) {
            String name = nameValuePair.substring(0, index).trim();
            String value = nameValuePair.substring(index + 1).trim();
            if(name.equals(Constants.COOKIE_UID)){
                return Integer.valueOf(value);
            }
        } else {
            throw new IllegalArgumentException("Invalid cookie name-value pair");
        }
        return 0;
    }

    public static Integer getCookieFid(HttpHeaders headers) {
        List<String> cookieList = headers.get("Set-Cookie");
        Integer cookieFid = 0;
        if (cookieList != null && !cookieList.isEmpty()) {
            for(String header : cookieList){
                Integer thisFid = getCookieFid(header);
                if( thisFid > 0){
                    cookieFid = thisFid;
                }
            }
        }
        return cookieFid;
    }

    public static Integer getCookieUid(HttpHeaders headers) {
        List<String> cookieList = headers.get("Set-Cookie");
        Integer cookieFid = 0;
        if (cookieList != null && !cookieList.isEmpty()) {
            for(String header : cookieList){
                Integer thisFid = getCookieUid(header);
                if( thisFid > 0){
                    cookieFid = thisFid;
                }
            }
        }
        return cookieFid;
    }

    public static void main(String[] args) throws ParseException {
        String dateStr = "Thu, 29/Apr/2021 11:26:32 GMT";
        String dateStr3 = "Thu, 29-Apr-2021 12:15:06 GMT";
//        String dateStr3 = "Thu, 29-Apr-2021 12:12:24 GMT";
        String dateStr2 = dateStr.substring(dateStr.indexOf(",")+2);
        dateStr2 = dateStr2.replace(" GMT","");

        SimpleDateFormat dateFormat = DateUtils.getSimpleDateFormate("EEE, dd/MMM/yyyy HH:mm:ss 'GMT'");
        SimpleDateFormat dateFormat2 = DateUtils.getSimpleDateFormate("EEE, dd-MMM-yyyy HH:mm:ss 'GMT'");
        try {
            Date d1 = dateFormat.parse(dateStr);
            System.out.println(d1);
            System.out.println(d1.getTime());
            System.out.println(new Date(d1.getTime()));
            System.out.println(System.currentTimeMillis()+"---");
            long times = (d1.getTime()-System.currentTimeMillis())/1000L;
            System.out.println((int)times);

            System.out.println(dateFormat2.parse(dateStr3).getTime()+"---------");
        } catch (ParseException e) {
            e.printStackTrace();
        }
//        Date d1 = DateUtils.string2Date(dateStr2,"dd/mmm/yyyy HH:MM:SS");
//        Date d1 = dateFormat.parse(dateStr);
//        System.out.println(d1.getTime());
    }
}
