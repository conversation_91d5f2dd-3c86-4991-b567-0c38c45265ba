package com.github.util;

/**
 * @className TemplateConstants
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/24 16:08
 * @Version 1.0
 **/
public class TemplateConstants {

    // html代码基础模板
    public static String content = "<style>\n" +
            "    /*图文 样式30 */\n" +
            "    .eng-graphic-style30 {\n" +
            "        padding-top: 16px;\n" +
            "    }\n" +
            "    .eng-graphic-style30 .eng-tabs {\n" +
            "        margin: 0 16px 15px;\n" +
            "        width: auto;\n" +
            "    }\n" +
            "    .eng-graphic-style30 .eng-tabs-info {\n" +
            "        overflow: hidden;\n" +
            "        text-align: left;\n" +
            "    }\n" +
            "    .eng-graphic-style30 .eng-tabs-info .full-banner {\n" +
            "        height: 515px;\n" +
            "    }\n" +
            "    .eng-graphic-style30 .eng-tabs-info .swiper-slide {\n" +
            "        -webkit-border-radius: 4px;\n" +
            "        -moz-border-radius: 4px;\n" +
            "        border-radius: 4px;\n" +
            "        cursor: pointer;\n" +
            "    }\n" +
            "    .eng-graphic-style30 .eng-tabs-info .swiper-slide .img-box {\n" +
            "        width: 100%;\n" +
            "        height: 198px;\n" +
            "        overflow: hidden;\n" +
            "    }\n" +
            "    .eng-graphic-style30 .eng-tabs-info .swiper-slide .img-box img {\n" +
            "        width: 100%;\n" +
            "        height: 100%;\n" +
            "    }\n" +
            "    .eng-graphic-style30 .eng-tabs-info .swiper-slide .btm-info {\n" +
            "        padding: 16px;\n" +
            "        color: inherit;\n" +
            "        position: relative;\n" +
            "    }\n" +
            "    .eng-graphic-style30 .eng-tabs-info .swiper-slide .name {\n" +
            "        font-size: 16px;\n" +
            "    }\n" +
            "    .eng-graphic-style30 .eng-tabs-info .swiper-slide .hint {\n" +
            "        font-size: 14px;\n" +
            "        margin-top: 16px;\n" +
            "        line-height: 20px;\n" +
            "        overflow: hidden;\n" +
            "        text-overflow: ellipsis;\n" +
            "        display: -webkit-box;\n" +
            "        -webkit-line-clamp: 4;\n" +
            "        -webkit-box-orient: vertical;\n" +
            "    }\n" +
            "    .eng-graphic-style30 .eng-tabs-info .swiper-slide .time {\n" +
            "        font-size: 14px;\n" +
            "        margin-top: 8px;\n" +
            "        text-align: right;\n" +
            "        position: absolute;\n" +
            "        top: 152px;\n" +
            "        right: 20px;\n" +
            "    }\n" +
            "    /*布局适配*/\n" +
            "    .layout-content2_1_2 .eng-graphic-style30 .eng-tabs-info .swiper-slide {\n" +
            "        width: 257px;\n" +
            "    }\n" +
            "    .layout-content3_1_3 .eng-graphic-style30 .eng-tabs-info .swiper-slide,\n" +
            "    .layout-content2_1_3 .eng-graphic-style30 .eng-tabs-info .swiper-slide {\n" +
            "        width: calc(100% - 32px);\n" +
            "    }\n" +
            "    .layout-content2_2_3 .eng-graphic-style30 .eng-tabs-info .swiper-slide {\n" +
            "        width: 353px;\n" +
            "    }\n" +
            "    @media only screen and (min-width: 1px) and (max-width: 926px) {\n" +
            "        .eng-graphic-style30 .eng-tabs-info .full-banner {\n" +
            "            height: auto !important;\n" +
            "        }\n" +
            "        .eng-graphic-style30 .eng-tabs-info {\n" +
            "            padding: 0 5%;\n" +
            "        }\n" +
            "        .eng-graphic-style30 .eng-tabs-info .swiper-slide {\n" +
            "            margin: 0 auto;\n" +
            "        }\n" +
            "        .eng-graphic-style30 .eng-tabs-info .swiper-slide .btm-info {\n" +
            "            padding-top: 8px;\n" +
            "        }\n" +
            "        .eng-graphic-style30 .eng-tabs-info .swiper-slide .hint {\n" +
            "            margin-top: 8px;\n" +
            "        }\n" +
            "        .eng-graphic-style30 .eng-tabs-info .swiper-slide .time{\n" +
            "            top: 138px;\n" +
            "        }\n" +
            "    }\n" +
            "</style>\n" +
            "<th:block th:if=\"${isConfigType && !isMobile}\">\n" +
            "    <style>\n" +
            "        [(|${typeStyle}|)]\n" +
            "    </style>\n" +
            "</th:block>\n" +
            "<th:block th:if=\"${isConfigType && isMobile}\">\n" +
            "    <style>\n" +
            "        @media only screen and (min-width: 1px) and (max-width: 926px) {\n" +
            "            [(|${typeStyle}|)]\n" +
            "        }\n" +
            "    </style>\n" +
            "</th:block>\n" +
            "<div class=\"eng-graphic-style30\" th:classappend=\"|style-${milliSecond}|\" th:id=\"|graphic-template-30-${milliSecond}|\">\n" +
            "    <div class=\"item-engine-title clear txt-l\" th:if=\"${engineInstance.header || engineInstance.more}\" >\n" +
            "        <span class=\"title-t\" th:utext=\"${engineInstance.name}\" th:if=\"${engineInstance.header}\">图文列表</span>\n" +
            "        <a th:data-href=\"${engineInstance.moreUrl}\" th:id=\"|more-${milliSecond}|\" th:classappend=\"${moreStyleType=='1'?'more-type-text':(moreStyleType=='2'?'more-type-image':'')}\"\n" +
            "               th:styleappend=\"${style1}\" th:if=\"${engineInstance.more}\"\n" +
            "           class=\"more fr\" target=\"_blank\">\n" +
            "            <span th:styleappend=\"${style2}\" class=\"mcgMhLan\"  th:if=\"${moreStyleType=='1' || moreStyleType=='0'}\">\n" +
            "                    [[${#strings.isEmpty(engineInstance.moreTitle)} ? '更多 ' : ${engineInstance.moreTitle}]]\n" +
            "                </span>\n" +
            "                <img th:styleappend=\"${style3}\" th:src=\"${moreImgUrl}\" alt=\"\" th:if=\"${moreStyleType=='2'}\">\n" +
            " <i th:styleappend=\"${style3}\" th:classappend=\"${class}\" th:if=\"${(moreStyleType=='1' && moreShowIcon) || moreStyleType=='0'}\"  class=\"icon-up\"></i>\n" +
            "        </a>\n" +
            "    </div>\n" +
            "    <div class=\"muchimgs-wrap\">\n" +
            "        <!--tab 切换-->\n" +
            "        <div th:classappend=\"${isConfigType?'mh-eng-tabs-set mh-eng-tabs-style'+styleIndex:''}\" class=\"eng-tabs show\" th:if=\"${engineInstance.showType && types != null && #lists.size(types) > 1}\">\n" +
            "            <span th:each=\"type, iterStat : ${types}\"\n" +
            "                         th:classappend=\"'type'\" th:class=\"${iterStat.index eq 0 ? 'current txt-theme' : 'tab'}\"\n" +
            "                  th:text=\"${type.name}\">活动</span>\n" +
            "        </div>\n" +
            "        <div class=\"eng-null-data\" th:data-id=\"|no-data-${milliSecond}|\"\n" +
            "             th:hidden=\"${datas != null && #lists.size(datas) > 0}\">\n" +
            "            <img th:src=\"@{/assets/images/style/engine/img_null_data.png}\">\n" +
            "            <p class=\"tip\" th:text=\"${emptyStr}\">暂无数据</p>\n" +
            "        </div>\n" +
            "        <div class=\"eng-tabs-info clear\"\n" +
            "             th:style=\"|color: ${(engineInstance.fontColor == null || #strings.isEmpty(engineInstance.fontColor)) ? '#333333' : engineInstance.fontColor}|\">\n" +
            "            <div class=\"swiper-container full-banner\"\n" +
            "                 th:classappend=\"|style-${milliSecond}|\" th:id=\"|graphic-swiper-30-${milliSecond}|\"\n" +
            "                 th:style=\"${engineInstance.backgroudType==0}?(${engineInstance.backgroudColor!=null and !#strings.isEmpty(engineInstance.backgroudColor)}?'background: '+${engineInstance.backgroudColor}+'':'background: #3D82F2'):'background: url('+${engineInstance.backgroudImgUrl}+') no-repeat;background-size: 100% 100%;'\">\n" +
            "                <div class=\"swiper-wrapper\">\n" +
            "                    <div class=\"swiper-slide\" th:each=\"data, iterStat : ${datas}\">\n" +
            "                        <div class=\"img-box js-crop-img\" th:if=\"${data['0'] != null}\">\n" +
            "                            <img th:src=\"${data['0'].value}\"\n" +
            "                                 onerror=\"this.onerror=null;this.src='/engine2/assets/images/on_error.png'\">\n" +
            "                        </div>\n" +
            "                        <div class=\"btm-info\">\n" +
            "                            <h3 class=\"name overHidden2\" th:if=\"${data['1'] != null}\" th:text=\"${data['1'].value}\" th:classappend=\"${#strings.isEmpty(engineInstance.dataTitleFontStyle)}?'':('titlefs'+${engineInstance.dataTitleFontStyle})\">如何用 5</h3>\n" +
            "                            <p class=\"hint\" th:if=\"${data['4'] != null}\" th:utext=\"${data['4'].value}\">\n" +
            "                                深色主题是应用界面设计的最新趋势</p>\n" +
            "                            <p class=\"time\" th:if=\"${data['6'] != null && data['6'] != ''}\"\n" +
            "                               th:text=\"${#strings.length(data['6'].value) >= 10 ? #strings.substring(data['6'].value, 0, 10) : data['6'].value}\">\n" +
            "                                2020-04-04</p>\n" +
            "                        </div>\n" +
            "                    </div>\n" +
            "                </div>\n" +
            "            </div>\n" +
            "        </div>\n" +
            "    </div>\n" +
            "</div>\n" +
            "<script th:inline=\"javascript\">\n" +
            "    var graphicTemplate30Callback = {\n" +
            "        data: {\n" +
            "            openTarget : [[${openTarget}]],\n" +
            "            ctx: [[${ctx}]],\n" +
            "            appId: [[${appId}]],\n" +
            "            engineInstance: [[${engineInstance}]],\n" +
            "            engineExternalId: [[${engineExternalId}]],\n" +
            "            wfwfid: [[${wfwfid}]],\n" +
            "            milliSecond: [[${milliSecond}]],\n" +
            "            types: [[${types}]],\n" +
            "            datas: [],\n" +
            "            selectedTypeIndex: 0,\n" +
            "            swiperObj: null,\n" +
            "            ele: \"\"\n" +
            "        },\n" +
            "        init: function (ele) {\n" +
            "            var $this = this;\n" +
            "            $('body').on('click', '#more-' + $this.data.milliSecond, function () {\n" +
            "                var url = $(\"#more-\" + $this.data.milliSecond).attr(\"data-href\");\n" +
            "                if (!$this.isEmpty(url)) {\n" +
            "                    mhTUrlOrMore(url, $this.data.openTarget);\n" +
            "                }\n" +
            "            });\n" +
            "            if ($this.isEmpty($this.data.types)) {\n" +
            "                $this.data.types = [];\n" +
            "                $this.data.types.push({});\n" +
            "            }\n" +
            "            $($this.data.types).each(function (i) {\n" +
            "                if (i == 0) {\n" +
            "                    this.dataLoaded = true;\n" +
            "                    this.datas = [[${datas}]];\n" +
            "                    $this.data.datas = this.datas;\n" +
            "                } else {\n" +
            "                    this.dataLoaded = false;\n" +
            "                    this.datas = [];\n" +
            "                }\n" +
            "            });\n" +
            "            $this.data.ele = ele;\n" +
            "            $this.initTab(ele);\n" +
            "            $this.initSwiper(ele);\n" +
            "            $this.initGridEvents();\n" +
            "        },\n" +
            "        getData: function (index, dom) {\n" +
            "            var $this = this;\n" +
            "            var type = $this.data.types[index];\n" +
            "            if ($this.isEmpty(type)) {\n" +
            "                return [];\n" +
            "            }\n" +
            "            if (type.dataLoaded) {\n" +
            "                return type.datas;\n" +
            "            }\n" +
            "            $this.loadData(index, dom);\n" +
            "            return null;\n" +
            "        },\n" +
            "        loadData: function (index, dom) {\n" +
            "            var $this = this;\n" +
            "            var type = $this.data.types[index];\n" +
            "            if ($this.isEmpty(type.engineInstanceId)) {\n" +
            "                type.engineInstanceId = $this.data.engineInstance.id;\n" +
            "            }\n" +
            "            var url = \"/engine2/general/\" + $this.data.appId + \"/type/datas?engineInstanceId=\" + $this.data.engineInstance.id + \"&typeId=\" + type.id;\n" +
            "            $.get(url, function (data) {\n" +
            "                if (data.code == 1) {\n" +
            "                    data = data.data;\n" +
            "                    var typeId = data.typeId;\n" +
            "                    var datas = data.datas.datas;\n" +
            "                    var typeIndex = null;\n" +
            "                    $($this.data.types).each(function (i) {\n" +
            "                        if (this.id == typeId) {\n" +
            "                            typeIndex = i;\n" +
            "                            return false;\n" +
            "                        }\n" +
            "                    });\n" +
            "                    if (typeIndex != null) {\n" +
            "                        $this.data.types[typeIndex].dataLoaded = true;\n" +
            "                        $this.data.types[typeIndex].datas = datas;\n" +
            "                        if ($this.data.selectedTypeIndex == typeIndex) {\n" +
            "                            $this.data.datas = datas;\n" +
            "                            // 渲染\n" +
            "                            $this.replaceSlides($this.generateSlides(datas), dom);\n" +
            "                        }\n" +
            "                    }\n" +
            "                } else {\n" +
            "                    var errorMessage = data.message;\n" +
            "                    if ($this.isEmpty(errorMessage)) {\n" +
            "                        errorMessage = \"加载分类:\" + type.id + \"下数据失败\";\n" +
            "                    }\n" +
            "                    console.log(errorMessage);\n" +
            "                }\n" +
            "            }).fail(function () {\n" +
            "                console.log(\"加载分类:\" + type.id + \"下数据失败\");\n" +
            "            });\n" +
            "        },\n" +
            "        initTab: function (ele) {\n" +
            "            var $this = this;\n" +
            "            $('body').on('click', '#graphic-template-30-' + $this.data.milliSecond + ' .eng-tabs span', function () {\n" +
            "                $(this).siblings().removeClass('current txt-theme');\n" +
            "                $(this).addClass('current txt-theme');\n" +
            "                setThemeColor('text', $(this));\n" +
            "                var index = $(this).index();\n" +
            "                var generalDataType = $this.data.types[index];\n" +
            "                if (2 == generalDataType.openType) {\n" +
            "                    var url = generalDataType.url;\n" +
            "                    if (!$this.isEmpty(url)) {\n" +
            "                        window.open(url);\n" +
            "                    }\n" +
            "                    return;\n" +
            "                }\n" +
            "                $this.data.selectedTypeIndex = index;\n" +
            "                var generalDataTypeId = generalDataType.id;\n" +
            "                $this.data.openTarget = generalDataType.openTarget;\n" +
            "                if (2 == generalDataType.openType) {\n" +
            "                    var url = generalDataType.url;\n" +
            "                    if (!$this.isEmpty(url)) {\n" +
            "                        mhTUrlOrMore(url, $this.data.openTarget);\n" +
            "                    }\n" +
            "                    return;\n" +
            "                }\n" +
            "                if ($this.isEmpty(generalDataTypeId)) {\n" +
            "                    var moreUrl = $this.data.engineInstance.moreUrl;\n" +
            "                    $(\"#more-\" + $this.data.milliSecond).attr(\"data-href\", moreUrl);\n" +
            "                } else {\n" +
            "                    $(\"#more-\" + $this.data.milliSecond).attr(\"data-href\", $this.replaceUrlParam($(\"#more-\" + $this.data.milliSecond).attr(\"data-href\"), \"typeId\", generalDataTypeId));\n" +
            "                }\n" +
            "                // 下标\n" +
            "                var datas = $this.getData(index, ele);\n" +
            "                if (datas != null) {\n" +
            "                    $this.data.datas = datas;\n" +
            "                    $this.replaceSlides($this.generateSlides(datas), ele);\n" +
            "                }\n" +
            "            });\n" +
            "            // 详情点击\n" +
            "            $('body').on('click', '#graphic-template-30-' + $this.data.milliSecond + ' .text-tab-list li', function () {\n" +
            "                var index = $(this).index();\n" +
            "                $($this.data.datas).each(function (i) {\n" +
            "                    if (i == index) {\n" +
            "                        $this.detail(this);\n" +
            "                    }\n" +
            "                });\n" +
            "            });\n" +
            "        },\n" +
            "        initSwiper: function () {\n" +
            "            var $this = this;\n" +
            "            var swiperContainerId = \"#graphic-swiper-30-\" + $this.data.milliSecond;\n" +
            "            $this.data.swiperObj = new Swiper(swiperContainerId, {\n" +
            "                slidesPerView: 1,\n" +
            "                loop: true,\n" +
            "                autoplay: $this.data.engineInstance.switchSpeed == null ? 3000 : $this.data.engineInstance.switchSpeed * 1000,\n" +
            "                watchActiveIndex: true,\n" +
            "                autoplayDisableOnInteraction: false,\n" +
            "                calculateHeight: isMobileDevice() ? true : false,\n" +
            "                observer: true,\n" +
            "                observeParents: true,\n" +
            "                onSlideClick: function (swiper) {\n" +
            "                    // 详情\n" +
            "                    var index = Math.abs(swiper.activeLoopIndex);\n" +
            "                    $($this.data.datas).each(function (i) {\n" +
            "                        if (i == index) {\n" +
            "                            $this.detail(this);\n" +
            "                        }\n" +
            "                    });\n" +
            "                }\n" +
            "            });\n" +
            "            cropImgFun($this.data.ele);\n" +
            "        },\n" +
            "        destroySwiper: function () {\n" +
            "            var curSlide = 0;\n" +
            "            if (this.data.swiperObj) {\n" +
            "                this.data.swiperObj.destroy(true);\n" +
            "            }\n" +
            "            this.initSwiper();\n" +
            "            if (this.data.swiperObj) {\n" +
            "                this.data.swiperObj.swipeTo(curSlide, 100, false);\n" +
            "            }\n" +
            "        },\n" +
            "        initGridEvents: function () {\n" +
            "            var $this = this;\n" +
            "            //拖拽后\n" +
            "            $('.grid-stack').off('gsresizestop').on('gsresizestop', function (event, elem) {\n" +
            "                if (!$this.isEmpty($this.data.swiperObj)) {\n" +
            "                    var slide = $this.data.swiperObj.activeLoopIndex;\n" +
            "                    $this.data.swiperObj.reInit();\n" +
            "                    $this.data.swiperObj.swipeTo(slide, 100, false);\n" +
            "                }\n" +
            "            });\n" +
            "        },\n" +
            "        generateSlides: function (datas) {\n" +
            "            var $this = this;\n" +
            "            var html = \"\";\n" +
            "            if (datas == null || datas.length < 1) {\n" +
            "                // 显示无数据\n" +
            "                $(\"div[data-id='no-data-\" + $this.data.milliSecond + \"']\").hide();\n" +
            "                $(\"div[data-id='no-data-\" + $this.data.milliSecond + \"']\").show();\n" +
            "            } else {\n" +
            "                $(\"div[data-id='no-data-\" + $this.data.milliSecond + \"']\").hide();\n" +
            "            }\n" +
            "            $(datas).each(function () {\n" +
            "                html += '<div class=\"swiper-slide\">';\n" +
            "                if (!$this.isEmpty(this['0']) && !$this.isEmpty(this['0'].value)) {\n" +
            "                    html += '<div class=\"img-box js-crop-img\">' +\n" +
            "                        '<img src=\"' + this['0'].value + '\" onerror=\"this.onerror=null;this.src=\\'/engine2/assets/images/on_error.png\\'\">' +\n" +
            "                        '</div>';\n" +
            "                }\n" +
            "                html += '<div class=\"btm-info\">';\n" +
            "                if (!$this.isEmpty(this['1'])) {\n" +
            "                    html += '<h3 class=\"name overHidden2 '+($this.data.engineInstance.dataTitleFontStyle==null?'':('titlefs'+$this.data.engineInstance.dataTitleFontStyle))+'\">' + this['1'].value + '</h3>';\n" +
            "                }\n" +
            "                if (!$this.isEmpty(this['4'])) {\n" +
            "                    html += '<p class=\"hint\">' + this['4'].value + '</p>';\n" +
            "                }\n" +
            "                if (!$this.isEmpty(this['6'])) {\n" +
            "                    var time = this['6'].value;\n" +
            "                    if (!$this.isEmpty(time)) {\n" +
            "                        time = time.substr(0, 10);\n" +
            "                    }\n" +
            "                    html += '<p class=\"time\">' + time + '</p>';\n" +
            "                }\n" +
            "                html += '</div></div>';\n" +
            "            });\n" +
            "            return html;\n" +
            "        },\n" +
            "        replaceSlides: function (html, ele) {\n" +
            "            $(ele).find('.swiper-wrapper').html(html);\n" +
            "            this.destroySwiper();\n" +
            "            cropImgFun(ele);\n" +
            "        },\n" +
            "        detail: function (data) {\n" +
            "            var $this = this;\n" +
            "            if (!$this.isEmpty(data)) {\n" +
            "                if (typeof (dataPopupWindowPreHandle) == 'undefined' || !dataPopupWindowPreHandle($this.data.engineInstance, data)) {\n" +
            "                    var url = data.url;\n" +
            "                    if (!$this.isEmpty(url)) {\n" +
            "                        mhDetail(url, [[${engineInstance.urlType}]])\n" +
            "                    }\n" +
            "                }\n" +
            "            }\n" +
            "        },\n" +
            "        isEmpty: function (str) {\n" +
            "            return typeof(str) == 'undefined' || str == null || \"\" === $.trim(str);\n" +
            "        },\n" +
            "        replaceUrlParam: function (url, param, value) {\n" +
            "            var $this = this;\n" +
            "            if (!$this.isEmpty(url)) {\n" +
            "                var re = new RegExp(\"[?&](\" + param + \"=[^&]*)\", \"gi\");\n" +
            "                var replaceStr = param + \"=\" + value;\n" +
            "                return re.test(url) ? url.replace(RegExp.$1, replaceStr) : url.indexOf('?') > -1 ? url + \"&\" + replaceStr : url + \"?\" + replaceStr;\n" +
            "            } else {\n" +
            "                return \"\";\n" +
            "            }\n" +
            "        }\n" +
            "    };\n" +
            "    function engineInitCallback(ele) {\n" +
            "        graphicTemplate30Callback.init(ele);\n" +
            "        return graphicTemplate30Callback;\n" +
            "    }\n" +
            "</script>\n";
}
