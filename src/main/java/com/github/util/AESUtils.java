package com.github.util;

import com.alibaba.fastjson.JSONObject;
import com.github.util.exception.BusinessException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;

/**
 * @version v1.0
 * @author: leolin
 * @since: 17/12/2020 4:32 PM
 * @description :类描述
 */
public class AESUtils {

    final static String KEY="1qaz3wsx%TGB&UJM";
    final static String IV="7ujm6y%T";
    //final static String iv="7ujm6yhn$RFV9edc";


    public static String encode(String content){
        String date = DateUtils.formatDate(new Date(),"yyyyMMdd");
        return aesEncrypt(content,KEY.getBytes(),(IV+date).getBytes());
    }

    public static String decode(String content){
        String date = DateUtils.formatDate(new Date(),"yyyyMMdd");
        return aesDecrypt(content,KEY.getBytes(),(IV+date).getBytes());
    }



    /**
     * AES加密
     * @param content  明文
     * @param keyBytes 秘钥
     * @param iv      偏移量
     * @return
     */
    public static String aesEncrypt(String content, byte[] keyBytes, byte[] iv){

        try{
            SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");
            Cipher cipher=Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(iv));
            byte[] result=cipher.doFinal(content.getBytes());
            return new String(Base64.encodeBase64(result),"UTF-8");
        }catch (Exception e) {
            System.out.println("exception:"+e.toString());
        }
        return null;
    }

    /**
     * AES解密
     * @param content   密文
     * @param keyBytes  秘钥
     * @param iv        偏移量
     * @return
     */
    public static String aesDecrypt(String content, byte[] keyBytes, byte[] iv){

        try{
            byte[] decryptBaseData=Base64.decodeBase64(content.getBytes("utf-8"));
            SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");
            Cipher cipher=Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, key, new IvParameterSpec(iv));
            byte[] result=cipher.doFinal(decryptBaseData);
            return new String(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 字符串转为 byte[]
     * @param hexString
     * @return
     */
    public static byte[] hexStringToBytes(String hexString) {
        if (hexString == null || hexString.equals("")) {
            return null;
        }
        hexString = hexString.toUpperCase();
        int length = hexString.length() / 2;
        char[] hexChars = hexString.toCharArray();
        byte[] d = new byte[length];
        for (int i = 0; i < length; i++) {
            int pos = i * 2;
            d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
        }
        return d;
    }


    /**
     * Convert char to byte
     *
     * @param c char
     * @return byte
     */
    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }


    /** productType 专用 **/
    final static String PRODUCT_TYPE_KEY = "4q#z3wsx%THB&UJM";
    final static String PRODUCT_TYPE_IV = "8uG*6y%T6^@32Ku0";
    /**
     * productType 校验
     * @param productType
     * @return
     */
    public static boolean checkProductType(String productType, Integer websiteProductType) {
        if (StringUtils.isBlank(productType)) {
            throw new BusinessException("productType请求参数缺失");
        }
        return Objects.equals(decodeProductTypeStr(productType), websiteProductType);
    }
    /**
     * 解密
     * @param productType
     */
    public static Integer decodeProductTypeStr(String productType) {

        byte[] decode = java.util.Base64.getDecoder().decode(productType);

        String result = AESUtils.aesDecrypt(new String(decode, StandardCharsets.UTF_8), PRODUCT_TYPE_KEY.getBytes(StandardCharsets.UTF_8), PRODUCT_TYPE_IV.getBytes(StandardCharsets.UTF_8));
        if (StringUtils.isBlank(result)) {
            return -1;
        }
        try {
            return Integer.valueOf(result);
        } catch (Exception e){
            e.printStackTrace();
        }
        return -1;
    }

    /**
     * 生成加密串
     * @param productType  网站表里的字段
     */
    private static String generateEncodeStr(String productType) {
        String result = AESUtils.aesEncrypt(productType, PRODUCT_TYPE_KEY.getBytes(StandardCharsets.UTF_8), PRODUCT_TYPE_IV.getBytes(StandardCharsets.UTF_8));
        if (StringUtils.isNotBlank(result)) {
            return java.util.Base64.getEncoder().encodeToString(result.getBytes(StandardCharsets.UTF_8));
        } else {
            return "";
        }
    }

    public static void main(String[] args) {
        JSONObject jsonObject =  new JSONObject();
        JSONObject user =  new JSONObject();
        user.put("name","login123");
        user.put("uid","1234");
        user.put("fid","23112");
        user.put("displayName","张三丰");
        user.put("sex","1111");
        user.put("tel","15612345678");
        jsonObject.put("user",user);

        String jcontent = jsonObject.toJSONString();

        String enc2 = encode(jcontent);
        System.out.println(enc2);
        System.out.println(decode(enc2));


        //超管后台，网站管理访问地址生成示例
        String url = "http://portal.chaoxing.com/sadmin/website-list-normal?p=";
        String productType = "1012";
        String p = generateEncodeStr(productType);
        System.out.println(url+p);
    }

}
