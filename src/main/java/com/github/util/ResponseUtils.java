package com.github.util;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * ResponseUtils<br/>
 * response常用操作工具类<br/>
 * 比如：直接输出html操作
 * <AUTHOR>
 * @version 1.0
 */
public class ResponseUtils {
    // -- header 常量定义 --//
    private static final String HEADER_ENCODING = "encoding";
    private static final String HEADER_NOCACHE = "no-cache";
    private static final String DEFAULT_ENCODING = "UTF-8";
    private static final boolean DEFAULT_NOCACHE = true;
    /**
     * 直接输出html
     * @param response
     * @param html
     * @param headers
     */
    public static void renderHtml(HttpServletResponse response ,final String html, final String... headers) {
        if(response==null){
            return ;
        }
        render(response ,MediaType.TEXT_HTML_VALUE, html, headers);
    }
    /**
     * 直接输出文本.
     * @see #render(String, String, String...)
     */
    public static void renderText(HttpServletResponse response ,final String text, final String... headers) {
        render(response ,MediaType.TEXT_HTML_VALUE, text, headers);
    }
    
    /**
     * 直接输出XML.
     * @see #render(String, String, String...)
     */
    public static void renderXml(HttpServletResponse response ,final String xml, final String... headers) {
        render(response ,MediaType.TEXT_HTML_VALUE, xml, headers);
    }
    
    /**
     * 直接输出JSON.
     * @param jsonString json字符串.
     * @see #render(String, String, String...)
     */
    public static void renderJson(HttpServletResponse response ,final String jsonString, final String... headers) {
        render(response ,MediaType.APPLICATION_JSON_VALUE, jsonString, headers);
    }
    
    /**
     * 直接输出JSON,使用Jackson转换Java对象.
     * @param data 可以是List<POJO>, POJO[], POJO, 也可以Map名值对.
     * @see #render(String, String, String...)
     */
    public static void renderJson(HttpServletResponse response ,final Object data, final String... headers) {
        render(response ,MediaType.APPLICATION_JSON.getType(), JSON.toJSONString(data), headers);
    }
    
    /**
     * 直接输出支持跨域Mashup的JSONP.
     * @param callbackName callback函数名.
     * @param object Java对象,可以是List<POJO>, POJO[], POJO ,也可以Map名值对, 将被转化为json字符串.
     */
    public static void renderJsonp(HttpServletResponse response ,final String callbackName, final Object object, final String... headers) {
        String jsonString = JSON.toJSONString(object);
        
        String result = new StringBuilder().append(callbackName).append("(").append(jsonString).append(");").toString();
        
        // 渲染Content-Type为javascript的返回内容,输出结果为javascript语句, 如callback197("{html:'Hello World!!!'}");
        render(response , MediaType.APPLICATION_JSON_VALUE, result, headers);
    }
    /**
     * @param response
     * @param contentType
     * @param content
     * @param headers
     */
    public static void render(HttpServletResponse response ,final String contentType, final String content, final String... headers) {
        initResponseHeader(response ,contentType, headers);
        try {
            response.getWriter().write(content);
            response.getWriter().flush();
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }
    /**
     * 对传入进来的response进行加工
     * @param response
     * @param contentType
     * @param headers
     * @return
     */
    private static void initResponseHeader(HttpServletResponse response ,final String contentType, final String... headers) {
        // 分析headers参数
        String encoding = DEFAULT_ENCODING;
        boolean noCache = DEFAULT_NOCACHE;
        for (String header : headers) {
            String headerName = StringUtils.substringBefore(header, ":");
            String headerValue = StringUtils.substringAfter(header, ":");
            
            if (StringUtils.equalsIgnoreCase(headerName, HEADER_ENCODING)) {
                encoding = headerValue;
            } else if (StringUtils.equalsIgnoreCase(headerName, HEADER_NOCACHE)) {
                noCache = Boolean.parseBoolean(headerValue);
            } else {
                throw new IllegalArgumentException(headerName + "不是一个合法的header类型");
            }
        }
        // 设置headers参数
        String fullContentType = contentType + ";charset=" + encoding;
        response.setContentType(fullContentType);
//        if (noCache) {
//            ServletUtils.setNoCacheHeader(response);
//        }
    }
}
