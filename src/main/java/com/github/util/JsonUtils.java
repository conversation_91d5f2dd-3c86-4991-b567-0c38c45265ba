package com.github.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @version ver 1.0
 * @className JsonUtils
 * @description
 * @blame wwb
 * @date 2020-03-17 19:51:02
 */
public class JsonUtils {

    private JsonUtils() {

    }

    /**
     * 排序jsonArr的int的字段
     *
     * @param jsonArray
     * @param key
     * @return
     */
    public static JSONArray ascJson(JSONArray jsonArray, String key) {
        // 排序字段
        List<JSONObject> borrowList = JSONArray.parseArray(jsonArray.toJSONString(), JSONObject.class);
        borrowList.sort((obj1, obj2) -> {
            int val1 = obj1.getIntValue(key);
            int val2 = obj2.getIntValue(key);
            // 是降序
            return val1 - val2;
        });
        return JSONArray.parseArray(borrowList.toString());
    }

    public static boolean isJSON(String jsonString) {
        return (jsonString.startsWith("{") && jsonString.endsWith("}")) || (jsonString.startsWith("[") && jsonString.endsWith("]"));
    }

}
