package com.github.util;

import com.github.conf.PassportConfig;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.Base64Utils;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;

/**
 * @className CookieValidateUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/6 12:35
 * @Version 1.0
 **/
public class CookieValidateUtils {

    public static final String PUBLIC_KEY_STRING = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCAZ2WX9e0wrotq7ynUPhl6ISkaY0xNTS1UxvsBhYgTTqp9tm/UJJfUaghqH2LtaJnpun1DYUt2xTfJSktdxEJO5326bKWZVbuBJhSthjrRlWngGlmFGbNaEx4l8ASfMzlZjs/G5Jwh8AvF+ZVLo/YfpS7n9xKbvtwGQBQ6yuNK0QIDAQAB";
    public static final String PUBLIC_KEY_MIRROR_STRING = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCbTi4wEEReljcQIrEfLsUDVIO9SYg9HntD2tSxvhXLeRVXx5I8lZmbPx/94W7OpSjUWTwQ15ta7iUgoiL5CZ86UqkC3TxqwQLWVvLLC2YEv8tO6G1YJxKRs7P3vSoT26GpRDjlcx/CW0j4jvdM7bn24oXOI5Wai/K0OrZ1ZN/uQwIDAQAB";
    // 模拟登录KEY
    public static final String IMITATE_KEY_STRING = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwDE0cC5PgzRXydMSIkjUzj2yMMJ3ApqqIFS+28aeiQCwKIKht7oKeGNP9Dt0T8yjl0x32qFNrRuzpiOsBT0PBmrh44XpM8DKl6tFmtPXJA2wkJTcR1PLwfsdwe304UPAwMJ0fyhNd05J7YNc6JBJt/GDbcrdz2ZdJTKw0Mth45wIDAQAB";

    /**
     * 验证cookie，成功返回uid，失败为null
     *
     * @param request
     * @return
     * @throws Exception
     */
    public static String valid(HttpServletRequest request) {
        String uidCookie = CookieUtils.getCookie(request, "UID");

        try {
            return checkCookie(request, uidCookie, "[%s][%s]", false);
        } catch (Exception e) {
        }
        return null;
    }


    public static String checkCookie(HttpServletRequest request, String uidCookie, String format, Boolean isMirror) throws UnsupportedEncodingException {
        String timestampCookie = CookieUtils.getCookie(request, "_d");
        String signCookie = CookieUtils.getCookie(request, "vc3");
        if (StringUtils.isNotBlank(uidCookie) && StringUtils.isNotBlank(timestampCookie) && StringUtils.isNotBlank(signCookie)) {
            String uid = uidCookie;
            synchronized (uid.intern()) {
                String timestamp = timestampCookie;
                String sign = signCookie; //URLDecoder.decode(signCookie, "UTF-8");
                sign = sign.substring(0, sign.length() - 32);
                String content = String.format(format, uid, timestamp); // UID _d  vc3

                if (verify(content, sign)) {
                    return uid;
                }
                if(PassportConfig.PASSPORT_PUBLIC_KEYS.size() > 0){
                    for (int i = 0; i < PassportConfig.PASSPORT_PUBLIC_KEYS.size(); i++) {
                        String pk = PassportConfig.PASSPORT_PUBLIC_KEYS.get(i);
                        // 验证签名，有一个验证成功就返回true
                        if(verify(uid, sign, pk)){
                            return uid;
                        }
                    }
                }
            }
        }
        return null;
    }

    public static boolean verify(String content, String sign) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] encodedKey = Base64Utils.decodeFromString(CookieValidateUtils.PUBLIC_KEY_STRING);

            PublicKey publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));

            Signature signature = Signature.getInstance("SHA256WithRSA");
            signature.initVerify(publicKey);
            signature.update(content.getBytes(StandardCharsets.UTF_8));

            return signature.verify(Base64Utils.decodeFromString(sign));
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean verify(String content, String sign, String pk) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] encodedKey = Base64Utils.decodeFromString(pk);

            PublicKey publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));

            Signature signature = Signature.getInstance("SHA256WithRSA");
            signature.initVerify(publicKey);
            signature.update(content.getBytes(StandardCharsets.UTF_8));

            return signature.verify(Base64Utils.decodeFromString(sign));
        } catch (Exception e) {
            return false;
        }
    }

    public static void main(String[] args) {
        String signCookie = "NdVFYhXxfzjf58nhQesKe6mY25DwYlRxVg1cTmZzOoo%2BI5JqN7VwthybwMWhZ0s1g91QMQndB5XD7yuaOujYmVwqKhzxW%2BMD57ognY0M2M%2BqzEr9rirZb2qzkDCG7Fd67l0ThZBXJrCSg6hqBOIlzueZRnHMT%2FMcjJX3aYoKqrE%3D19a97077d1dd16817e172af838c60b3f";
        String uid = "2";
        synchronized (uid.intern()) {
            String timestamp = "1669621449293";
            String sign = null;
            try {
                sign = URLDecoder.decode(signCookie, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            sign = sign.substring(0, sign.length() - 32);
            String content = String.format("[%s][^cxmirror^][%s]", uid, timestamp); // UID _d  vc3

        }
    }
}
