package com.github.util;

import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 获取浏览器名称和请求源的系统名称
 * <AUTHOR>
 */
public class UserAgentUtils {

    public static final List<String> OSS = Arrays.asList("Windows", "Linux", "Mac", "Android", "HarmonyOS", "FreeBSD", "Debian", "iOS", "BlackBerry", "MeeGo", "Symbian", "Chrome OS", "WebOS");

    /**
     * 部分浏览器ua信息有重复，不直接返回
     * @param ua
     * @return
     */
    public static String getBrowserName(String ua) {
        if(StringUtils.isBlank(ua)){
            return "Unknown";
        }

        String browser = "";
        if (ua.indexOf("Safari") > -1) {
            browser = "Safari";
        }
        if (ua.indexOf("Chrome") > -1 || ua.indexOf("CriOS") > -1) {
            browser = "Chrome";
        }
        if (ua.indexOf("MSIE") > -1 || ua.indexOf("Trident") > -1) {
            browser = "IE";
        }
        if (ua.indexOf("Edge") > -1 || ua.indexOf("Edg") > -1 || ua.indexOf("EdgA") > -1 || ua.indexOf("EdgiOS") > -1) {
            browser = "Edge";
        }
        if (ua.indexOf("Firefox") > -1 || ua.indexOf("FxiOS") > -1 || ua.indexOf("Focus") > -1) {
            browser = "Firefox";
        }
        if (ua.indexOf("Chromium") > -1) {
            browser = "Chromium";
        }
        if (ua.indexOf("Opera") > -1) {
            browser = "Opera";
        }
        if (ua.indexOf("Vivaldi") > -1) {
            browser = "Vivaldi";
        }
        if (ua.indexOf("YaBrowser") > -1) {
            browser = "Yandex";
        }
        if (ua.indexOf("Arora") > -1) {
            browser = "Arora";
        }
        if (ua.indexOf("Lunascape") > -1) {
            browser = "Lunascape";
        }
        if (ua.indexOf("QupZilla") > -1) {
            browser = "QupZilla";
        }
        if (ua.indexOf("coc_coc_browser") > -1) {
            browser = "Coc Coc";
        }
        if (ua.indexOf("Kindle") > -1 || ua.indexOf("Silk") > -1) {
            browser = "Kindle";
        }
        if (ua.indexOf("Iceweasel") > -1) {
            browser = "Iceweasel";
        }
        if (ua.indexOf("Konqueror") > -1) {
            browser = "Konqueror";
        }
        if (ua.indexOf("Iceape") > -1) {
            browser = "Iceape";
        }
        if (ua.indexOf("SeaMonkey") > -1) {
            browser = "SeaMonkey";
        }
        if (ua.indexOf("Epiphany") > -1) {
            browser = "Epiphany";
        }
        if (ua.indexOf("QihooBrowser") > -1 || ua.indexOf("QHBrowser") > -1 || ua.indexOf("360EE") > -1 || ua.indexOf("360SE") > -1) {
            browser = "360";
        }
        if (ua.indexOf("QQBrowser") > -1 || ua.indexOf("QQ") > -1) {
            browser = "QQBrowser";
        }
        if (ua.indexOf("Baidu") > -1 || ua.indexOf("BIDUBrowser") > -1 || ua.indexOf("baidubrowser") > -1 || ua.indexOf("baiduboxapp") > -1 || ua.indexOf("baiduboxapp") > -1) {
            browser = "Baidu";
        }
        if (ua.indexOf("Maxthon") > -1) {
            browser = "Maxthon";
        }
        if (ua.indexOf("MetaSr") > -1 || ua.indexOf("Sogou") > -1) {
            browser = "Sogou";
        }
        if (ua.indexOf("LBBROWSER") > -1 || ua.indexOf("LieBaoFast") > -1) {
            browser = "Liebao";
        }
        if (ua.indexOf("2345Explorer") > -1 || ua.indexOf("Mb2345Browser") > -1 || ua.indexOf("2345chrome") > -1) {
            browser = "2345Explorer";
        }
        if (ua.indexOf("115Browser") > -1) {
            browser = "115Browser";
        }
        if (ua.indexOf("TheWorld") > -1) {
            browser = "TheWorld";
        }
        if (ua.indexOf("MiuiBrowser") > -1) {
            browser = "XiaoMi";
        }
        if (ua.indexOf("Quark") > -1) {
            browser = "Quark";
        }
        if (ua.indexOf("Qiyu") > -1) {
            browser = "Qiyu";
        }
        if (ua.indexOf("MicroMessenger") > -1 || ua.indexOf("wxwork") > -1) {
            browser = "Wechat";
        }
        if (ua.indexOf("AliApp(TB") > -1) {
            browser = "Taobao";
        }
        if (ua.indexOf("AliApp(AP") > -1) {
            browser = "Alipay";
        }
        if (ua.indexOf("Weibo") > -1) {
            browser = "Weibo";
        }
        if (ua.indexOf("com.douban.frodo") > -1) {
            browser = "Douban";
        }
        if (ua.indexOf("SNEBUY-APP") > -1) {
            browser = "Suning";
        }
        if (ua.indexOf("IqiyiApp") > -1) {
            browser = "iQiYi";
        }
        if (ua.indexOf("DingTalk") > -1) {
            browser = "DingTalk";
        }
        if (ua.indexOf("HuaweiBrowser-APP") > -1 || ua.indexOf("HUAWEI/") > -1 || ua.indexOf("HONOR") > -1) {
            browser = "Huawei";
        }
        if (ua.indexOf("VivoBrowser") > -1) {
            browser = "Vivo";
        }
        return browser;
    }

    public static String getOsName(String ua) {
        if(StringUtils.isBlank(ua)){
            return "Unknown";
        }
        AtomicReference<String> os = new AtomicReference<>("");
        OSS.forEach(bb -> {
            if (ua.lastIndexOf(bb) > 0) {
                os.set(bb);
            }
        });
        if (StringUtils.isEmpty(os.get())) {
            os.set("Unknown");
        }
        return os.get();
    }

    public static Integer isMobile(HttpServletRequest request, Integer isMobile) {
        String userAgent = request.getHeader("USER-AGENT");
        if (userAgent.indexOf("Android") != -1 || userAgent.indexOf("iPhone") != -1 || "1".equals(request.getParameter("isMobile"))) {
            return Constants.STATUS_TRUE;
        }
        return isMobile;
    }
}
