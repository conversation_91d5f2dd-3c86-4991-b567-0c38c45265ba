package com.github.util;

import java.util.regex.Pattern;

/**
 * @className IPAddressValidator
 * @Description 校验ip合法性方法。
 * <AUTHOR>
 * @Date 2024/5/9 15:59
 * @Version 1.0
 **/
public class IPAddressValidator {
    private static final String IPV4_PATTERN =
            "^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[1-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[1-9]|[0])){3}$";


    private static final String IPV6_STD_PATTERN =
            "^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$";

    private static final String IPV6_HEX_COMPRESSED_PATTERN =
            "^(([0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4})*)?)::((([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4})?)$";

    private static final String IPV6_COMPRESSED_CIDR_PATTERN =
            "^(?:[0-9a-fA-F]{1,4}(?::[0-9a-fA-F]{1,4})*)?::\\/(\\d{1,3})$";

    public static boolean isValidIPV4(String ip) {
        return Pattern.compile(IPV4_PATTERN).matcher(ip).matches();
    }

    public static boolean isValidIPV6Std(String ip) {
        return Pattern.compile(IPV6_STD_PATTERN).matcher(ip).matches();
    }

    public static boolean isValidIPV6HexCompressed(String ip) {
        return Pattern.compile(IPV6_HEX_COMPRESSED_PATTERN).matcher(ip).matches();
    }


    public static boolean isValidIPV6CompressedCIDR(String ip) {
        return Pattern.compile(IPV6_COMPRESSED_CIDR_PATTERN).matcher(ip).matches();
    }

    public static boolean isValidIPAddress(String ip) {
        if(ip.contains("-")){
            String ip1 = ip.split("-")[0];
            String ip2 = ip.split("-")[1];
            return isValidIPV4(ip1) || isValidIPV6Std(ip1) || isValidIPV6HexCompressed(ip1) || isValidIPV6CompressedCIDR(ip1) ||
                    isValidIPV4(ip2) || isValidIPV6Std(ip2) || isValidIPV6HexCompressed(ip2) || isValidIPV6CompressedCIDR(ip2);
        } else {
            return isValidIPV4(ip) || isValidIPV6Std(ip) || isValidIPV6HexCompressed(ip) || isValidIPV6CompressedCIDR(ip);
        }
    }

    public static void main(String[] args) {
        String ip1 = "2001:250:3413::/48";
        String ip2 = "2001:250:3413::48";
        String ip3 = "2001:0db8:85a3:0000:0000:8a2e:0370:7334";
        String ip4 = "********";
        String ip5 = "***************";
        String ip6 = "*********";
        String ip7 = "*********-***************";
        String ip8 = "2001:0db8:85a3:0000:0000:8a2e:0370:7334-2001:0db8:85a3:0000:0000:8a2e:0370:7934";
//        String ipv4Address = "***********";
//        System.out.println("Is " + ipv4Address + " a valid IPv4 address? " + validator.isValidIPV4(ipv4Address));
//
//        String ipv6StdAddress = "2001:0db8:85a3:0000:0000:8a2e:0370:7334";
//        System.out.println("Is " + ipv6StdAddress + " a valid standard IPv6 address? " + validator.isValidIPV6Std(ipv6StdAddress));
//
//        String ipv6CompressedAddress = "2001:db8::1";
//        System.out.println("Is " + ipv6CompressedAddress + " a valid compressed IPv6 address? " + validator.isValidIPV6HexCompressed(ipv6CompressedAddress));
//
//        String ipv6CompressedAddress2 = "2001:db8::64";
//        System.out.println("Is " + ipv6CompressedAddress + " a valid compressed IPv6 address? " + validator.isValidIPV6CompressedCIDR(ipv6CompressedAddress2));
        System.out.println(isValidIPAddress(ip1));
        System.out.println(isValidIPAddress(ip2));
        System.out.println(isValidIPAddress(ip3));
        System.out.println(isValidIPAddress(ip4));
        System.out.println(isValidIPAddress(ip5));
        System.out.println(isValidIPAddress(ip6));
        System.out.println(isValidIPAddress(ip7));
        System.out.println(isValidIPAddress(ip8));
    }
}
