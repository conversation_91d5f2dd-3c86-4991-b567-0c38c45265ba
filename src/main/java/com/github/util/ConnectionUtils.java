package com.github.util;

import javax.net.ssl.HttpsURLConnection;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.time.*;
import java.util.Date;

/**
 * @className ConnectionUtils
 * @Description TODO  校验url的ssl证书是否在有效期内，临期15天内算非法
 * <AUTHOR>
 * @Date 2024/1/25 17:21
 * @Version 1.0
 **/
public class ConnectionUtils {

    public static void main(String[] args) throws IOException {
        String urlStr = "https://mh.chaoxing.com/t/arok";
        Integer status = checkStatus(urlStr);
        System.out.println(status);
    }

    private static final Integer STATUS_NORMAL = 1;
    private static final Integer STATUS_ERROR = 2;
    private static final Integer STATUS_NO_SSL = 3;

    /**
     * 获取证书状态，提前15天告警。 15天之内的都算不正常状态
     * @param urlStr
     * @return 1 正常 2 异常 3 没有证书
     */
    public static Integer checkStatus(String urlStr) {

        try {
            URL url = new URL(urlStr);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.connect();
            Certificate[] cs = connection.getServerCertificates();
            if(cs.length > 0){
                Certificate certificate = cs[0];
                X509Certificate x509Certificate = (X509Certificate) certificate;
                Date d1 = x509Certificate.getNotAfter();
                LocalDate today = LocalDate.now();
//                Long d3 = LocalDateTime.of(today, LocalTime.now()).plusDays(15).toInstant(ZoneOffset.of("+8")).toEpochMilli();
                Long d3 = LocalDateTime.now().plusDays(15).toInstant(ZoneOffset.of("+8")).toEpochMilli();
//                System.out.println(today);
//                System.out.println(d1.getTime());
//                System.out.println(d3);
//                System.out.println(System.currentTimeMillis());
                if(d1.getTime() < d3){
                    return STATUS_ERROR;
                }else {
                    return STATUS_NORMAL;
                }
            }

        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return STATUS_NO_SSL;  // 没有证书
    }
}
