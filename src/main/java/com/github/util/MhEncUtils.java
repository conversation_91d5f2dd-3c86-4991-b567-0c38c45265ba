package com.github.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * 门户参数加密工具
 */
public class MhEncUtils {
    /**
     * 出口接口处理
     *
     * @param map
     * @param key
     * @return
     */
    public static String generateEnc(TreeMap<String, String> map, String key) {
        StringBuilder source = new StringBuilder();
        if (!CollectionUtils.isEmpty(map)) {
            Set<Map.Entry<String, String>> entries = map.entrySet();
            for (Map.Entry<String, String> entry : entries) {
                String value = entry.getValue();
                if(StringUtils.isBlank(value)){
                    value = "";
                }
                source.append("[").append(entry.getKey()).append("=").append(value).append("]");
            }
        }
        String enc = DigestUtils.md5Hex(String.format("%s[%s]", source, key));
        return enc;
    }
}
