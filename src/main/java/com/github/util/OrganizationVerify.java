package com.github.util;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by ZhouXinyu on 2019/11/27 18:07.
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OrganizationVerify {

    /**
     * 区分校验类型 "website"：网站，"page"：页面，"webjson"：页面json
     */
    String type() default "";

}