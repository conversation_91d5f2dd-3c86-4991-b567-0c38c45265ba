package com.github.util;

import io.netty.util.NetUtil;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.*;
import java.util.Enumeration;

public class IPUtils {

    /**
     * 请求通过反向代理之后，可能包含请求客户端真实IP的HTTP HEADER
     * 如果后续扩展，有其他可能包含IP的HTTP HEADER，加到这里即可
     */
    private final static String[] POSSIBLE_HEADERS = new String[] {
            "X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP",
            "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"
    };

    private final static String[] INNER_HEADERS = new String[] {
            "X-Real-IP"
    };

    /**
     * 获取浏览器所在用户端的ip地址
     * @param request
     * @return
     */
    public static String getClientIpAddr(HttpServletRequest request) {
        String ipAddress = request.getHeader("x-forwarded-for");
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
 
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
            if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
                //根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    e.printStackTrace();
                }
                ipAddress = inet.getHostAddress();
            }
        }
        //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ipAddress != null && ipAddress.length() > 15) { //"***.***.***.***".length() = 15
            if (ipAddress.indexOf(",") > 0) {
                ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
            }
        }
 
        return ipAddress;
    }

    /**
     * 获取请求客户端的真实IP地址
     * @param request javax.servlet.http.HttpServletRequest
     * @return 客户端端真实IP地址
     */
    public static String getRequestClientRealIP(HttpServletRequest request) {
        String ip = null;
        // 先检查代理：逐个HTTP HEADER检查过去，看看是否存在客户端真实IP
        for (String header : POSSIBLE_HEADERS) {
            ip = request.getHeader(header);
            if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                // 请求经过多次反向代理后可能会有多个IP值（以英文逗号分隔），第一个IP才是客户端真实IP

                ip = ip.contains(",") ? ip.split(",")[0] : ip;
                break;
            }
        }
        ip = ip == null ? request.getRemoteAddr() : ip;
        if ("0:0:0:0:0:0:0:1".equals(ip) || "127.0.0.1".equals(ip)) {
            // 说明是从本机发出的请求，直接获取并返回本机IP地址
            return getLocalRealIP();
        }
        // 从所有可能的HTTP HEADER中都没有找到客户端真实IP，采用request.getRemoteAddr()来兜底
        if(StringUtils.isNotBlank(ip) && (NetUtil.isValidIpV6Address(ip) || NetUtil.isValidIpV4Address(ip))){
            return ip;
        }else {
            return "0.0.0.0";
        }
    }

    public static String getInnerIp(HttpServletRequest request) {
        String ip = null;
        ip = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            // 请求经过多次反向代理后可能会有多个IP值（以英文逗号分隔），第一个IP才是客户端真实IP
            ip = ip.contains(",") ? ip.split(",")[0] : ip;
        }

        String ip2 = request.getHeader("X-Real-IP");
        if (StringUtils.isBlank(ip)) {
            ip = ip2;
        } else if (!ip.equals(ip2)) { // 校验内网ip的时候，发现该请求被代理过，则返回一个固定外网ip。
            ip = "***************";
        }

        if (StringUtils.isBlank(ip)) {
            ip = request.getRemoteAddr();
        }

        if ("0:0:0:0:0:0:0:1".equals(ip) || "127.0.0.1".equals(ip)) {
            // 说明是从本机发出的请求，直接获取并返回本机IP地址
            return getLocalRealIP();
        }
        // 从所有可能的HTTP HEADER中都没有找到客户端真实IP，采用request.getRemoteAddr()来兜底

        if (org.apache.commons.lang.StringUtils.isNotBlank(ip) && (NetUtil.isValidIpV6Address(ip) || NetUtil.isValidIpV4Address(ip))) {
            return ip;
        } else {
            return "0.0.0.0";
        }
    }

    /**
     * 获取本机IP地址
     * @return 若配置了外网IP则优先返回外网IP；否则返回本地IP地址。如果本机没有被分配局域网IP地址（例如本机没有连接任何网络），则默认返回127.0.0.1
     */
    public static String getLocalRealIP() {
        String localIP = "127.0.0.1"; // 本地IP
        String netIP = null; // 外网IP

        Enumeration<NetworkInterface> netInterfaces;
        try {
            netInterfaces = NetworkInterface.getNetworkInterfaces();
        } catch (SocketException e) {
            e.printStackTrace();
            // 发生异常则返回null
            return null;
        }
        InetAddress ip;
        boolean netIPFound = false; // 是否找到外网IP
        while (netInterfaces.hasMoreElements() && !netIPFound) {
            Enumeration<InetAddress> address = netInterfaces.nextElement().getInetAddresses();
            while (address.hasMoreElements()) {
                ip = address.nextElement();
                if (!ip.isSiteLocalAddress() && !ip.isLoopbackAddress() && !ip.getHostAddress().contains(":")) {
                    // 外网IP
                    netIP = ip.getHostAddress();
                    netIPFound = true;
                    break;
                } else if (ip.isSiteLocalAddress() && !ip.isLoopbackAddress() && !ip.getHostAddress().contains(":")) {
                    // 内网IP
                    localIP = ip.getHostAddress();
                }
            }
        }

        if (StringUtils.isNotBlank(netIP)) {
            // 如果配置了外网IP则优先返回外网IP地址
            return netIP;
        }
        return localIP;
    }

    public static boolean checkIpValid(String ip){
        return NetUtil.isValidIpV6Address(ip) || NetUtil.isValidIpV4Address(ip);
    }

    /**
     *
     * @param ipv6
     * @return
     */
    public static String ipv6Complete(String ipv6) {
        try {
            // 输入部分 IPv6 地址

            // 补全 IPv6 地址
            InetAddress[] addresses = Inet6Address.getAllByName(ipv6);

            // 打印补全后的 IPv6 地址
            if(addresses.length> 0){
                return addresses[0].getHostAddress();
            }
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }

        return ipv6;
    }
}
