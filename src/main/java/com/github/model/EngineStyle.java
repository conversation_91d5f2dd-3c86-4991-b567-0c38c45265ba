package com.github.model;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version ver 1.0
 * @className EngineStyle
 * @description
 * @blame xhl
 * @date 2020-05-18 19:12:57
 */
public class EngineStyle {

    public static final Integer STYLE_TYPE = 0;
    public static final Integer MORE_TYPE = 1;

    //头部的type值
    public static final Integer HEADER_TYPE = 1;

    //dataType
    public static final Integer DATA_DEFAULT = 0;
    public static final Integer DATA_ALL = 1;
    public static final Integer DATA_TYPE = 2;
    public static final Integer DATA_NULL = 3;

    /** id   db_column: id */
    private Integer id;
    /** 样式名称   db_column: name */
    private String name;
    /** 图片地址   db_column: img_url */
    private String imgUrl;
    /** 样式code   db_column: code */
    private Integer code;
    /** 引擎类型   db_column: engine_type */
    private String engineType;
    /** 样式类型   db_column: type */
    private Integer type;
    /** 0:系统默认 1：定制单位id  db_column: wfwfid */
    private Integer wfwfid;
    /** 顺序  db_column: order */
    private Integer order;
    /** 描述  db_column: des */
    private String des;
    /** 0:不可以 1:可以  db_column: status */
    private Integer status;

    private Integer isSys;

    private String createUserId;

    private String tplPath;
    /**  样式源ID db_column: origin_id */
    private String originId;
    /**  样式版本号 按' 1.0','2.0'方式明名 db_column: version */
    private String version;
    /**  是否直接查询所有数据，0否，1 查全部数据，2 仅查分类, 3 不查询数据 */
    private Integer dataType;


    private String enc;

    public Integer getDataType() {
        return Objects.isNull(dataType)?0:dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getEngineType() {
        return engineType;
    }

    public void setEngineType(String engineType) {
        this.engineType = engineType;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getWfwfid() {
        return wfwfid;
    }

    public void setWfwfid(Integer wfwfid) {
        this.wfwfid = wfwfid;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public Integer getIsSys() {
        return isSys;
    }

    public void setIsSys(Integer isSys) {
        this.isSys = isSys;
    }

    public String getTplPath() {
        return tplPath;
    }

    public void setTplPath(String tplPath) {
        this.tplPath = tplPath;
    }

    public String getEnc() {
        return enc;
    }

    public void setEnc(String enc) {
        this.enc = enc;
    }

    public String getOriginId() {
        return originId;
    }

    public void setOriginId(String originId) {
        this.originId = originId;
    }

    public String getVersion() {
        return Objects.isNull(version) ? "1" : version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
