/*
 */
package com.github.model;

import org.apache.commons.lang.builder.*;

public class UserContent {
	
	//alias
	public static final String TABLE_ALIAS = "UserContent";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_FILE_ID = "fileId";
	public static final String ALIAS_CONTENT = "content";
	
	
	//columns START
	/** id   db_column: id */ 	
	private Integer id;
	/** fileId   db_column: file_id */ 	
	private Integer fileId;
	/** content   db_column: content */ 	
	private String content;
	//columns END

	public UserContent(){
	}

	public UserContent(
		Integer id
	){
		this.id = id;
	}

	public void setId(Integer value) {
		this.id = value;
	}
	public Integer getId() {
		return this.id;
	}
	public void setFileId(Integer value) {
		this.fileId = value;
	}
	public Integer getFileId() {
		return this.fileId;
	}
	public void setContent(String value) {
		this.content = value;
	}
	public String getContent() {
		return this.content;
	}

	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof UserContent == false) return false;
		if(this == obj) return true;
		UserContent other = (UserContent)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}
}

