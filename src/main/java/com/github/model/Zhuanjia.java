/*
 */
package com.github.model;

import org.apache.commons.lang.builder.*;

public class <PERSON><PERSON><PERSON><PERSON> {
	
	//alias
	public static final String TABLE_ALIAS = "Zhuanjia";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_XINGM = "xingm";
	public static final String ALIAS_PHONE = "phone";
	public static final String ALIAS_GZDW = "gzdw";
	public static final String ALIAS_DEPT = "dept";
	public static final String ALIAS_DYTJ = "dytj";
	public static final String ALIAS_DETJ = "detj";
	public static final String ALIAS_SAIQU = "saiqu";
	public static final String ALIAS_SFZH = "sfzh";
	public static final String ALIAS_ZYFX = "zyfx";
	public static final String ALIAS_ZJLY = "zjly";
	
	
	//columns START
	/** id   db_column: id */ 	
	private Integer id;
	/** xingm   db_column: xingm */ 	
	private String xingm;
	/** phone   db_column: phone */ 	
	private String phone;
	/** gzdw   db_column: gzdw */ 	
	private String gzdw;
	/** dept   db_column: dept */ 	
	private String dept;
	/** dytj   db_column: dytj */ 	
	private String dytj;
	/** detj   db_column: detj */ 	
	private String detj;
	/** saiqu   db_column: saiqu */ 	
	private String saiqu;
	/** sfzh   db_column: sfzh */ 	
	private String sfzh;
	/** zyfx   db_column: zyfx */ 	
	private String zyfx;
	/** zjly   db_column: zjly */ 	
	private String zjly;
	//columns END

	public Zhuanjia(){
	}

	public Zhuanjia(
		Integer id
	){
		this.id = id;
	}

	public void setId(Integer value) {
		this.id = value;
	}
	public Integer getId() {
		return this.id;
	}
	public void setXingm(String value) {
		this.xingm = value;
	}
	public String getXingm() {
		return this.xingm;
	}
	public void setPhone(String value) {
		this.phone = value;
	}
	public String getPhone() {
		return this.phone;
	}
	public void setGzdw(String value) {
		this.gzdw = value;
	}
	public String getGzdw() {
		return this.gzdw;
	}
	public void setDept(String value) {
		this.dept = value;
	}
	public String getDept() {
		return this.dept;
	}
	public void setDytj(String value) {
		this.dytj = value;
	}
	public String getDytj() {
		return this.dytj;
	}
	public void setDetj(String value) {
		this.detj = value;
	}
	public String getDetj() {
		return this.detj;
	}
	public void setSaiqu(String value) {
		this.saiqu = value;
	}
	public String getSaiqu() {
		return this.saiqu;
	}
	public void setSfzh(String value) {
		this.sfzh = value;
	}
	public String getSfzh() {
		return this.sfzh;
	}
	public void setZyfx(String value) {
		this.zyfx = value;
	}
	public String getZyfx() {
		return this.zyfx;
	}
	public void setZjly(String value) {
		this.zjly = value;
	}
	public String getZjly() {
		return this.zjly;
	}

	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof Zhuanjia == false) return false;
		if(this == obj) return true;
		Zhuanjia other = (Zhuanjia)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}
}

