/*
 */
package com.github.model;

import org.apache.commons.lang.builder.*;

public class FidFreeCount {
	
	//alias
	public static final String TABLE_ALIAS = "FidFreeCount";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_FID = "文件ID";
	public static final String ALIAS_FREE_COUNT = "已经使用了的免费额度";

	public static final Integer STATUS_TRY = 1; // 试用
	public static final Integer STATUS_NORAML = 2; // 正式

	//columns START
	/** id   db_column: id */ 	
	private Integer id;
	/** 文件ID   db_column: fid */ 	
	private Integer fid;
	/** 已经使用了的免费额度   db_column: free_count */ 	
	private Integer freeCount;
	/** 状态，1试用，2正式 */
	private Integer status;
	//columns END

	public FidFreeCount(){
	}

	public FidFreeCount(
		Integer id
	){
		this.id = id;
	}

	public void setId(Integer value) {
		this.id = value;
	}
	public Integer getId() {
		return this.id;
	}
	public void setFid(Integer value) {
		this.fid = value;
	}
	public Integer getFid() {
		return this.fid;
	}
	public void setFreeCount(Integer value) {
		this.freeCount = value;
	}
	public Integer getFreeCount() {
		return this.freeCount == null ? 0 : this.freeCount;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof FidFreeCount == false) return false;
		if(this == obj) return true;
		FidFreeCount other = (FidFreeCount)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}
}

