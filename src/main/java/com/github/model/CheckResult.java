/*
 */
package com.github.model;

import org.apache.commons.lang.builder.*;

public class CheckResult {
	
	//alias
	public static final String TABLE_ALIAS = "CheckResult";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_CONTENT = "解析完之后的富文本内容";
	
	
	//columns START
	/** id   db_column: id */ 	
	private Integer id;
	/** 解析完之后的富文本内容   db_column: content */ 	
	private String content;
	//columns END

	public CheckResult(){
	}

	public CheckResult(
		Integer id
	){
		this.id = id;
	}

	public void setId(Integer value) {
		this.id = value;
	}
	public Integer getId() {
		return this.id;
	}
	public void setContent(String value) {
		this.content = value;
	}
	public String getContent() {
		return this.content;
	}

	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof CheckResult == false) return false;
		if(this == obj) return true;
		CheckResult other = (CheckResult)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}
}

