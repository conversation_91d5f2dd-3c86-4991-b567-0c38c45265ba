package com.github.model;

import com.alibaba.fastjson.JSONArray;

/**
 * @className TChatCompletion
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/10 11:52
 * @Version 1.0
 **/
public class TChatCompletion {
    private String role;

    private String content;

    private String reasoningContent;

    private String chatId;

    private String object;

    private JSONArray references;

    public TChatCompletion(String role, String content, String chatId, String object, String reasoningContent,JSONArray references) {
        this.role = role;
        this.content = content;
        this.chatId = chatId;
        this.object = object;
        this.reasoningContent = reasoningContent;
        this.references = references;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public String getReasoningContent() {
        return reasoningContent;
    }

    public void setReasoningContent(String reasoningContent) {
        this.reasoningContent = reasoningContent;
    }

    public JSONArray getReferences() {
        return references;
    }

    public void setReferences(JSONArray references) {
        this.references = references;
    }
}
