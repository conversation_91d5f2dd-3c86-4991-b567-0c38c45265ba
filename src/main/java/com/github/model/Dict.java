/*
 */
package com.github.model;

import org.apache.commons.lang.builder.*;

public class Dict {
	
	//alias
	public static final String TABLE_ALIAS = "Dict";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_CODE = "code";
	public static final String ALIAS_NAME = "name";
	public static final String ALIAS_VALUE = "value";
	public static final String ALIAS_CREATE_TIME = "createTime";
	
	
	//columns START
	/** id   db_column: id */ 	
	private Integer id;
	/** code   db_column: code */ 	
	private String code;
	/** name   db_column: name */ 	
	private String name;
	/** value   db_column: value */ 	
	private String value;
	/** createTime   db_column: create_time */ 	
	private java.util.Date createTime;
	//columns END

	public Dict(){
	}

	public Dict(
		Integer id
	){
		this.id = id;
	}

	public void setId(Integer value) {
		this.id = value;
	}
	public Integer getId() {
		return this.id;
	}
	public void setCode(String value) {
		this.code = value;
	}
	public String getCode() {
		return this.code;
	}
	public void setName(String value) {
		this.name = value;
	}
	public String getName() {
		return this.name;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getValue() {
		return this.value;
	}
	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	public java.util.Date getCreateTime() {
		return this.createTime;
	}

	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof Dict == false) return false;
		if(this == obj) return true;
		Dict other = (Dict)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}
}

