/*
 */
package com.github.model;

import org.apache.commons.lang.builder.*;

import java.math.BigDecimal;
import java.util.Date;

public class UserFileOrder {
	
	//alias
	public static final String TABLE_ALIAS = "UserFileOrder";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_UID = "用户uid";
	public static final String ALIAS_FID = "单位ID";
	public static final String ALIAS_FILE_NAME = "上传的文件名";
	public static final String ALIAS_STATUS = "status";
	public static final String ALIAS_ORDER_NO = "订单编号";
	public static final String ALIAS_PRICE = "价格";
	public static final String ALIAS_PAY_STATUS = "支付状态：0未支付，1支付成功，-1支付失败";
	public static final String ALIAS_CHECK_STATUS = "0未检测，1检测中，2检测失败，3检测成功";
	public static final String ALIAS_OBJECT_ID = "生成的云盘objectId";
	public static final String ALIAS_FILE_ORIGIN_NAME = "文件原名称";

	/**
	 * ai check 状态
	 */
//	public static final Integer  CHECK_STATUS_INIT = 0;
	public static final Integer  CHECK_STATUS_ING = 2;
	public static final Integer  CHECK_STATUS_API = 3;  // api接口调用成功
	public static final Integer  CHECK_STATUS_SUCCESS = 4;

	public static final Integer  CHECK_STATUS_OUT_TIME = 5;
	// 文件不存在状态
	public static final Integer  CHECK_STATUS_NO_FILE = -2;
	public static final Integer  CHECK_STATUS_RETRY_MAX = -3;

	/**
	 * 支付状态
	 */
	public static final Integer  PAY_STATUS_UNPAY = 0;
	public static final Integer  PAY_STATUS_SUCCESS = 1;
	public static final Integer  PAY_STATUS_FAIL = -1;

	
	//columns START
	/** id   db_column: id */ 	
	private Integer id;
	/** 用户uid   db_column: uid */ 	
	private Integer uid;
	/** 用户uid   db_column: uname */
	private String uname;
	/** 单位ID   db_column: fid */
	private Integer fid;
	/** 单位ID   db_column: dept */
	private String dept;
	/** 上传的文件的篇名   db_column: file_name */
	private String fileName;
	/** status   db_column: status */ 	
	private Integer status;
	/** 订单编号   db_column: order_no */ 	
	private String orderNo;
	/** 价格   db_column: price */ 	
	private BigDecimal price;
	/** 支付状态：0未支付，1支付成功，-1支付失败   db_column: pay_status */ 	
	private Integer payStatus;
	/** 0未检测，1检测中，2检测失败，3检测成功   db_column: check_status */ 	
	private Integer checkStatus;
	/** 生成的云盘objectId   db_column: object_id */ 	
	private String objectId;
	/** 文件原名称   db_column: file_origin_name */ 	
	private String fileOriginName;
	/** 文件uuid名称   db_column: uuid_name */
	private String uuidName;

	private String requestId;

	private Date createTime;
	private Integer words;

	private String persent;

	private Integer isOut;
	//columns END

	public UserFileOrder(){
	}

	public UserFileOrder(
		Integer id
	){
		this.id = id;
	}

	public void setId(Integer value) {
		this.id = value;
	}
	public Integer getId() {
		return this.id;
	}
	public void setUid(Integer value) {
		this.uid = value;
	}
	public Integer getUid() {
		return this.uid;
	}
	public void setFid(Integer value) {
		this.fid = value;
	}
	public Integer getFid() {
		return this.fid;
	}
	public void setFileName(String value) {
		this.fileName = value;
	}
	public String getFileName() {
		return this.fileName;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}
	public Integer getStatus() {
		return this.status;
	}
	public void setOrderNo(String value) {
		this.orderNo = value;
	}
	public String getOrderNo() {
		return this.orderNo;
	}
	public void setPrice(BigDecimal value) {
		this.price = value;
	}
	public BigDecimal getPrice() {
		return this.price;
	}
	public void setPayStatus(Integer value) {
		this.payStatus = value;
	}
	public Integer getPayStatus() {
		return this.payStatus;
	}
	public void setCheckStatus(Integer value) {
		this.checkStatus = value;
	}
	public Integer getCheckStatus() {
		return this.checkStatus == null ? 0 : this.checkStatus;
	}
	public void setObjectId(String value) {
		this.objectId = value;
	}
	public String getObjectId() {
		return this.objectId;
	}
	public void setFileOriginName(String value) {
		this.fileOriginName = value;
	}
	public String getFileOriginName() {
		return this.fileOriginName;
	}

	public String getUuidName() {
		return uuidName;
	}

	public void setUuidName(String uuidName) {
		this.uuidName = uuidName;
	}

	public String getDept() {
		return dept;
	}

	public void setDept(String dept) {
		this.dept = dept;
	}

	public String getUname() {
		return uname;
	}

	public void setUname(String uname) {
		this.uname = uname;
	}

	public String getPersent() {
		return persent;
	}

	public void setPersent(String persent) {
		this.persent = persent;
	}

	public Integer getIsOut() {
		return isOut;
	}

	public void setIsOut(Integer isOut) {
		this.isOut = isOut;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getWords() {
		return words;
	}

	public void setWords(Integer words) {
		this.words = words;
	}

	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof UserFileOrder == false) return false;
		if(this == obj) return true;
		UserFileOrder other = (UserFileOrder)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}
}

