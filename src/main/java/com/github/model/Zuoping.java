/*
 */
package com.github.model;

import org.apache.commons.lang.builder.*;

public class Zuoping {
	
	//alias
	public static final String TABLE_ALIAS = "Zuoping";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_FENZU = "fenzu";
	public static final String ALIAS_XUHAO = "xuhao";
	public static final String ALIAS_SAIQU = "saiqu";
	public static final String ALIAS_XINGM = "xingm";
	public static final String ALIAS_GZDW = "gzdw";
	
	
	//columns START
	/** id   db_column: id */ 	
	private Integer id;
	/** fenzu   db_column: fenzu */ 	
	private String fenzu;
	/** xuhao   db_column: xuhao */ 	
	private String xuhao;
	/** saiqu   db_column: saiqu */ 	
	private String saiqu;
	/** xingm   db_column: xingm */ 	
	private String xingm;
	/** gzdw   db_column: gzdw */ 	
	private String gzdw;
	//columns END

	public Zuoping(){
	}

	public Zuoping(
		Integer id
	){
		this.id = id;
	}

	public void setId(Integer value) {
		this.id = value;
	}
	public Integer getId() {
		return this.id;
	}
	public void setFenzu(String value) {
		this.fenzu = value;
	}
	public String getFenzu() {
		return this.fenzu;
	}
	public void setXuhao(String value) {
		this.xuhao = value;
	}
	public String getXuhao() {
		return this.xuhao;
	}
	public void setSaiqu(String value) {
		this.saiqu = value;
	}
	public String getSaiqu() {
		return this.saiqu;
	}
	public void setXingm(String value) {
		this.xingm = value;
	}
	public String getXingm() {
		return this.xingm;
	}
	public void setGzdw(String value) {
		this.gzdw = value;
	}
	public String getGzdw() {
		return this.gzdw;
	}

	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof Zuoping == false) return false;
		if(this == obj) return true;
		Zuoping other = (Zuoping)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}
}

