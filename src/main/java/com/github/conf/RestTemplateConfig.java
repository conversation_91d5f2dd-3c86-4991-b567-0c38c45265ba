package com.github.conf;

import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.LaxRedirectStrategy;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;


@Configuration
public class RestTemplateConfig {

	@Resource private HttpClient httpClient;
	@Resource
	private FastJsonConfig fastJsonConfig;
	@Resource
	private RestTemplateBuilder restTemplateBuilder;

	@Primary
	@Bean
	public RestTemplate restTemplate() {
		RestTemplate restTemplate = restTemplateBuilder
				.build();
		restTemplate.setRequestFactory(clientHttpRequestFactory());
		List<HttpMessageConverter<?>> converters = restTemplate.getMessageConverters();

		FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
		fastJsonHttpMessageConverter.setFastJsonConfig(fastJsonConfig);
		fastJsonHttpMessageConverter.setSupportedMediaTypes(new ArrayList<MediaType>() {
			private static final long serialVersionUID = 6688659690562975474L;

			{
				this.add(MediaType.APPLICATION_JSON);
				this.add(MediaType.APPLICATION_JSON_UTF8);
			}
		});


		// 找到httpMessageConverter替换成UTF8类型的
		for (int i = 0; i < converters.size(); i++) {
			HttpMessageConverter<?> httpMessageConverter = converters.get(i);
			if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
				converters.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
			} else if(httpMessageConverter.getClass().equals(MappingJackson2HttpMessageConverter.class)){
				converters.set(i, fastJsonHttpMessageConverter);
				break;
			}
		}
		return restTemplate;

	}

	@Bean("restTemplateAutoRedirect")
	public RestTemplate restTemplateAutoRedirect(){
		//使用apacheClient，实现服务器内302重定向
		ClientHttpRequestFactory httpRequestFactory = clientHttpRequestFactoryBsApp();
		RestTemplate restTemplate = new RestTemplate(httpRequestFactory);
		ResponseErrorHandler responseErrorHandler = new ResponseErrorHandler() {
			@Override
			public boolean hasError(ClientHttpResponse clientHttpResponse) throws IOException {
				return false;
			}
			@Override
			public void handleError(ClientHttpResponse clientHttpResponse) throws IOException {

			}
		};
		restTemplate.setErrorHandler(responseErrorHandler);
		List<HttpMessageConverter<?>> converters = restTemplate.getMessageConverters();

		FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
		fastJsonHttpMessageConverter.setFastJsonConfig(fastJsonConfig);
		fastJsonHttpMessageConverter.setSupportedMediaTypes(new ArrayList<MediaType>() {
			private static final long serialVersionUID = 6688659690562975474L;

			{
				this.add(MediaType.APPLICATION_JSON);
				this.add(MediaType.APPLICATION_JSON_UTF8);
			}
		});

		// 找到httpMessageConverter替换成UTF8类型的
		for (int i = 0; i < converters.size(); i++) {
			HttpMessageConverter<?> httpMessageConverter = converters.get(i);
			if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
				converters.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
			}else if(httpMessageConverter.getClass().equals(MappingJackson2HttpMessageConverter.class)){
				converters.set(i, fastJsonHttpMessageConverter);
				break;
			}
		}
		return restTemplate;
	}

	@Bean
	public ClientHttpRequestFactory clientHttpRequestFactoryBsApp() {
		HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
		HttpClient httpClient = HttpClientBuilder.create().setRedirectStrategy(new LaxRedirectStrategy()).build();
		clientHttpRequestFactory.setHttpClient(httpClient);
		clientHttpRequestFactory.setConnectTimeout(10000);
		clientHttpRequestFactory.setReadTimeout(10000);
		clientHttpRequestFactory.setConnectionRequestTimeout(10000);
		return clientHttpRequestFactory;
	}

	/**
	 * restTemplate 代理访问.
	 * @return
	 */
    @Bean("restTemplateProxy")
    public RestTemplate restTemplateProxy() {
        RestTemplate restTemplate = restTemplateBuilder
                .build();

        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setProxy(
                new Proxy(
                        Proxy.Type.HTTP,
                        new InetSocketAddress("127.0.0.1", 7070)  //设置代理服务
                )
        );
        restTemplate.setRequestFactory(requestFactory);

        List<HttpMessageConverter<?>> converters = restTemplate.getMessageConverters();

		FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
		fastJsonHttpMessageConverter.setFastJsonConfig(fastJsonConfig);
		fastJsonHttpMessageConverter.setSupportedMediaTypes(new ArrayList<MediaType>() {
			private static final long serialVersionUID = 6688659690562975474L;

			{
				this.add(MediaType.APPLICATION_JSON);
				this.add(MediaType.APPLICATION_JSON_UTF8);
			}
		});

        // 找到httpMessageConverter替换成UTF8类型的
        for (int i = 0; i < converters.size(); i++) {
            HttpMessageConverter<?> httpMessageConverter = converters.get(i);
            if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
                converters.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
            }else if(httpMessageConverter.getClass().equals(MappingJackson2HttpMessageConverter.class)){
				converters.set(i, fastJsonHttpMessageConverter);
				break;
			}
        }
        return restTemplate;
    }


	@Bean(name = "restTemplateHttps")
	public RestTemplate restTemplateHttps() throws NoSuchAlgorithmException, KeyManagementException {

		SSLContext sslContext = SSLContext.getInstance("SSL");
		sslContext.init(null, new TrustManager[]{new X509TrustManager() {
			@Override
			public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {

			}

			@Override
			public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {

			}

			@Override
			public X509Certificate[] getAcceptedIssuers() {
				return new X509Certificate[]{};
			}
		}}, new java.security.SecureRandom());

		SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);
		CloseableHttpClient httpClient = HttpClients.custom()
				.setSSLSocketFactory(csf)
				.build();
		HttpComponentsClientHttpRequestFactory requestFactory =
				new HttpComponentsClientHttpRequestFactory();
		requestFactory.setHttpClient(httpClient);
		RestTemplate restTemplate = new RestTemplate(requestFactory);
		return restTemplate;
	}

	@Bean
	public ClientHttpRequestFactory clientHttpRequestFactory() {
		HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
		return clientHttpRequestFactory;
	}

}

