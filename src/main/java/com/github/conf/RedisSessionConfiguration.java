package com.github.conf;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.session.web.http.CookieHttpSessionIdResolver;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.session.web.http.DefaultCookieSerializer;
import org.springframework.session.web.http.HttpSessionIdResolver;

/**
 * @version v1.0
 * @author: leolin
 * @since: 04/11/2020 2:40 PM
 * @description :类描述
 */
@Configuration
@EnableRedisHttpSession(maxInactiveIntervalInSeconds = 1800, redisNamespace = "spring-portal:session")
public class RedisSessionConfiguration {

    private String cookieName = "cdmhsession";

    private Integer timeout = 1800;

    @Bean
    public CookieSerializer cookieSerializer() {
        DefaultCookieSerializer cookieSerializer = new DefaultCookieSerializer();
        //sessionId名称
        cookieSerializer.setCookieName(cookieName);
        cookieSerializer.setCookiePath("/");
//        cookieSerializer.setCookieMaxAge(timeout);
        return cookieSerializer;
    }

    @Bean
    public HttpSessionIdResolver httpSessionIdResolver() {
        CookieHttpSessionIdResolver cookieHttpSessionIdResolver = new CookieHttpSessionIdResolver();
        cookieHttpSessionIdResolver.setCookieSerializer(cookieSerializer());

        // 初始化session redis过期时间和preKey
//        super.setMaxInactiveIntervalInSeconds(timeout);
//        super.setRedisNamespace("spring-portal:session");
        return cookieHttpSessionIdResolver;
    }

}
