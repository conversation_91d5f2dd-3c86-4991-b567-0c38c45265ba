/*
 * .............................................
 *
 * 				    _ooOoo_
 * 		  	       o8888888o
 * 	  	  	       88" . "88
 *                 (| -_- |)
 *                  O\ = /O
 *              ____/`---*\____
 *               . * \\| |// `.
 *             / \\||| : |||// \
 *           / _||||| -:- |||||- \
 *             | | \\\ - /// | |
 *            | \_| **\---/** | |
 *           \  .-\__ `-` ___/-. /
 *            ___`. .* /--.--\ `. . __
 *        ."" *< `.___\_<|>_/___.* >*"".
 *      | | : `- \`.;`\ _ /`;.`/ - ` : | |
 *         \ \ `-. \_ __\ /__ _/ .-` / /
 *======`-.____`-.___\_____/___.-`____.-*======
 *
 * .............................................
 *              佛祖保佑 永无BUG
 *
 * 佛曰:
 * 写字楼里写字间，写字间里程序员；
 * 程序人员写程序，又拿程序换酒钱。
 * 酒醒只在网上坐，酒醉还来网下眠；
 * 酒醉酒醒日复日，网上网下年复年。
 * 但愿老死电脑间，不愿鞠躬老板前；
 * 奔驰宝马贵者趣，公交自行程序员。
 * 别人笑我忒疯癫，我笑自己命太贱；
 * 不见满街漂亮妹，哪个归得程序员？
 *
 * 北纬30.√  <EMAIL>
 */
package com.github.mapper;

import com.github.model.EngineStyle;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Mapper
@Repository
public interface EngineStyleMapper {

    EngineStyle get(Integer id);

    void add(EngineStyle engineStyle);

    void addSlefStyle(EngineStyle engineStyle);

    void update(EngineStyle engineStyle);

    /**
     * 根据引擎类型 获取引擎样式
     *
     * @param engineType
     * @return
     */
    List<EngineStyle> getListByEngineType(@Param("engineType") String engineType, @Param("styleIdLimit") Integer styleIdLimit);


    /**
     * 根据引擎类型 查找系统和wfwfid指定样式
     *
     * @param engineType
     * @param wfwfid
     * @return
     */
    List<EngineStyle> getListByEngineTypeAndWfwFid(@Param("engineType") String engineType, @Param("wfwfid") Integer wfwfid, @Param("styleIdLimit") Integer styleIdLimit);


    /**
     * 据引擎类型，和类型 获取对应样式
     *
     * @param engineType
     * @param type
     * @return
     */
    List<EngineStyle> getListByEngineTypeAndType(@Param("engineType") String engineType, @Param("type") Integer type, @Param("styleIdLimit") Integer styleIdLimit);

    /**
     * 查询当前样式所在的位置。用于定位分页
     *
     * @param engineType
     * @param type
     * @param code
     * @param styleIdLimit
     * @return
     */
    Integer getEngineStylePosition(@Param("engineType") String engineType, @Param("type") Integer type, @Param("code") Integer code, @Param("styleIdLimit") Integer styleIdLimit);


    /**
     * 根据类型 获取所有样式
     *
     * @param type
     * @return
     */
    List<EngineStyle> getAllListByType(@Param("type") Integer type, @Param("styleIdLimit") Integer styleIdLimit);


    /**
     * 根据code和类型查找 样式信息(包含type)
     *
     * @param code
     * @param engineType
     * @return
     */
    EngineStyle getEngineStyleByCodeAndEngineType(@Param("code") Integer code, @Param("engineType") String engineType, @Param("version") String version);

    /**
     * 根据code和类型查找 搜索样式信息
     *
     * @param code
     * @param engineType
     * @return
     */
    EngineStyle getSearchEngineStyleByCodeAndEngineType(@Param("code") Integer code, @Param("engineType") String engineType, @Param("version") String version);

    List<EngineStyle> getSelfStyleList(@Param("wfwfid") Integer wfwfid, @Param("engineType") String engineType, @Param("type") Integer type);

    // 这里可以获取所有类型样式，并根据 styleIdLimit 取小于该Id的数据
    List<EngineStyle> getStyleListLimitId(@Param("wfwfid") Integer wfwfid, @Param("engineType") String engineType, @Param("type") Integer type, @Param("styleIdLimit") Integer styleIdLimit);

    /**
     * 查询首页样式
     *
     * @param code
     * @param engineType
     * @param wfwfid
     * @return
     */
    EngineStyle getEngineStyleByCodeAndEngineTypeAndWfwfid(@Param("code") Integer code, @Param("engineType") String engineType, @Param("wfwfid") Integer wfwfid);

    /**
     * 根据code、wfwfid和类型查找，不判断状态，用于样式版本筛选
     *
     * @param code
     * @param engineType
     * @return
     */
    List<EngineStyle> getEngineStyleByCodeAndEngineTypeWithoutStatus(@Param("code") Integer code, @Param("engineType") String engineType, @Param("type") Integer type);

    /**
     * 根据code、wfwfid和类型查找，版本，不判断状态，用于样式版本筛选
     *
     * @param code
     * @param engineType
     * @param version
     * @param type
     * @return
     */
    EngineStyle getEngineStyleByCodeAndEngineTypeAndVersionWithoutStatus(@Param("code") Integer code, @Param("engineType") String engineType, @Param("type") Integer type, @Param("version") String version);

    /**
     * 根据code、wfwfid和类型查找，版本，不判断状态，用于样式版本筛选
     *
     * @param code
     * @param engineType
     * @param version
     * @param type
     * @return
     */
    EngineStyle getEngineStyleByCodeAndEngineTypeAndVersion(@Param("code") Integer code, @Param("engineType") String engineType,
                                                            @Param("type") Integer type, @Param("version") String version,
                                                            @Param("status") Boolean status);

    /**
     * 引擎类型，样式编号，具体引擎类型分类 查询
     *
     * @param code
     * @param engineType
     * @param type1
     * @param type2
     * @return
     */
    List<EngineStyle> getEngineStyleByCodeAndEngineTypeWithoutStatusBy2Type(@Param("code") Integer code, @Param("engineType") String engineType, @Param("type1") Integer type1, @Param("type2") Integer type2);

    /**
     * 版本，引擎类型，样式编号，具体引擎类型分类 查询
     *
     * @param code
     * @param engineType
     * @param type1
     * @param type2
     * @param version
     * @return
     */
    EngineStyle getEngineStyleByCodeAndEngineTypeAndVersionWithoutStatusBy2Type(@Param("code") Integer code, @Param("engineType") String engineType, @Param("type1") Integer type1,
                                                                                @Param("type2") Integer type2, @Param("version") String version);

    /**
     * 版本，引擎类型，样式编号，具体引擎类型分类 查询 状态查询
     *
     * @param code
     * @param engineType
     * @param type1
     * @param type2
     * @param version
     * @return
     */
    EngineStyle getEngineStyleByCodeAndEngineTypeAndVersionBy2Type(@Param("code") Integer code, @Param("engineType") String engineType, @Param("type1") Integer type1,
                                                                   @Param("type2") Integer type2, @Param("version") String version, @Param("status") Boolean status);

    /**
     * 联合查询，获取到配置给单位或者模板的样式列表
     *
     * @param engineType
     * @param styleType
     * @param template
     * @param wfwfid
     * @return
     */
    List<EngineStyle> getListByUnion(@Param("engineType") String engineType, @Param("styleType") Integer styleType, @Param("template") String template, @Param("wfwfid") Integer wfwfid, @Param("styleIdLimit") Integer styleIdLimit);


    Integer getMaxNextId();

}
