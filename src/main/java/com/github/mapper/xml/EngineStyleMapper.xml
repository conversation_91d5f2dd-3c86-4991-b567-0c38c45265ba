<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.EngineStyleMapper">

    <resultMap id="BaseResultMap" type="com.github.model.EngineStyle">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="imgUrl" column="img_url"/>
        <result property="code" column="code"/>
        <result property="engineType" column="engine_type"/>
        <result property="type" column="type"/>
        <result property="wfwfid" column="wfwfid"/>
        <result property="order" column="order"/>
        <result property="des" column="des"/>
        <result property="status" column="status"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="isSys" column="is_sys"/>
        <result property="tplPath" column="tpl_path"/>
        <result property="originId" column="origin_id"/>
        <result property="version" column="version"/>
        <result property="dataType" column="data_type"/>
    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="com.github.mapper.EngineStyleMapper.columns">
	<![CDATA[
        id
        , `name`, img_url, code, engine_type, `type`, wfwfid, `order`, des, status,create_user_id,is_sys,tpl_path,origin_id,version, data_ype
        ]]>
	</sql>
    <insert id="add" useGeneratedKeys="true" keyProperty="id">
		<![CDATA[
        insert into t_engine_style(id, `name`, img_url, code, engine_type, `type`, wfwfid, `order`, des, status,
                                   create_user_id, is_sys, tpl_path, origin_id, version)
        VALUES (#{id},
                #{name},
                #{imgUrl},
                #{code},
                #{engineType},
                #{type},
                #{wfwfid},
                #{order},
                #{des},
                #{status},
                #{createUserId},
                #{isSys},
                #{tplPath},
                #{originId},
                #{version})
        ]]>
	</insert>


    <insert id="addSlefStyle" useGeneratedKeys="true" keyProperty="id">
		<![CDATA[
        insert into t_engine_style(id, `name`, img_url, code, engine_type, `type`, wfwfid, `order`, des, status,
                                   create_user_id, is_sys, tpl_path, origin_id, version)
        VALUES (#{id},
                #{name},
                #{imgUrl},
                (SELECT CASE WHEN MAX(t.code) IS NULL THEN 1 ELSE MAX(t.code) END
                 FROM t_engine_style t
                 WHERE t.wfwfid = #{wfwfid}
                   and t.engine_type = #{engineType}),
                #{engineType},
                #{type},
                #{wfwfid},
                (SELECT CASE WHEN MAX(t.code) IS NULL THEN 1 ELSE MAX(t.code) END
                 FROM t_engine_style t
                 WHERE t.wfwfid = #{wfwfid}
                   and t.engine_type = #{engineType}),
                #{des},
                #{status},
                #{createUserId},
                #{isSys},
                #{tplPath},
                #{originId},
                #{version})
        ]]>
	</insert>

    <update id="update">
		 <![CDATA[
        UPDATE t_engine_style
        SET `name`           = #{name},
            img_url          = #{imgUrl},
            code             = #{code},
            engine_type      = #{engineType},
            `type`           = #{type},
            `wfwfid`         = #{wfwfid},
            `order`          = #{order},
            `des`            = #{des},
            `status`         = #{status},
            `create_user_id` = #{createUserId},
            is_sys           = #{isSys},
            origin_id        = #{originId},
            version          = #{version}
        WHERE id = #{id}
        ]]>
	</update>

    <select id="getListByEngineType" resultMap="BaseResultMap">
        select *
        from t_engine_style
        where engine_type = #{engineType}
          and `status` = '1'
		 <![CDATA[ and id <= #{styleIdLimit} ]]>
		order by `order`
    </select>

    <select id="getListByEngineTypeAndType" resultMap="BaseResultMap">
        select *
        from t_engine_style
        where engine_type = #{engineType}
          and type = #{type}
          and `status` = '1'
		<![CDATA[ and id <= #{styleIdLimit} ]]>
		order by `order`
    </select>

    <select id="getListByUnion" resultType="com.github.model.EngineStyle">
        SELECT
        *
        FROM
        t_engine_style t join t_engine_style_wfwfid t2 on t.id = t2.style
        WHERE t.engine_type = #{engineType}
        and t.type = #{styleType}
        and t.`status` = '1'
        <![CDATA[ and t.id <= #{styleIdLimit} ]]>
        <if test="wfwfid != null">
            and t2.wfwfid = #{wfwfid}
        </if>
        <if test="template != null">
            and t2.template = #{template}
        </if>
    </select>

    <select id="getEngineStylePosition" resultType="java.lang.Integer">
        select count(id)
        from t_engine_style
        where engine_type = #{engineType}
          and type = #{type}
          and `status` = '1'
		<![CDATA[ and id <= #{styleIdLimit} ]]>
		<![CDATA[ and code <= #{code}
        ]]>
    </select>

    <select id="getAllListByType" resultMap="BaseResultMap">
        select *
        from t_engine_style
        where type = #{type}
          and `status` = '1'
		<![CDATA[ and id < #{styleIdLimit} ]]>
        order by `order`
    </select>

    <select id="getEngineStyleByCodeAndEngineType" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        t_engine_style t
        WHERE
        t.`code` = #{code}
        AND t.engine_type = #{engineType}
        AND t.type = 0
        <!--		AND t.`status` = 1-->
        AND t.`version` = #{version}
        LIMIT 1
    </select>

    <select id="getSearchEngineStyleByCodeAndEngineType" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        t_engine_style t
        WHERE
        t.`code` = #{code}
        <!--		AND t.`status` = 1-->
        AND t.engine_type = #{engineType}
        AND (t.type = 0 OR t.type = 1)
        AND version = #{version}
        LIMIT 1
    </select>


    <select id="getListByEngineTypeAndWfwFid" resultMap="BaseResultMap">
        SELECT *
        FROM t_engine_style t
        WHERE t.engine_type = #{engineType}
          AND (t.wfwfid = 0 OR t.wfwfid = #{wfwfid})
          AND t.`status` = 1
		<![CDATA[ and id < #{styleIdLimit} ]]>
        ORDER BY t.order
    </select>

    <select id="getSelfStyleList" resultMap="BaseResultMap">
        SELECT *
        FROM t_engine_style t
        WHERE t.wfwfid = #{wfwfid}
          and t.engine_type = #{engineType}
          and t.type = #{type}
          AND t.`status` = 1
        ORDER BY t.order
    </select>
    <select id="getStyleListLimitId" resultMap="BaseResultMap">
        SELECT *
        FROM t_engine_style t
        WHERE t.wfwfid = #{wfwfid}
          and t.engine_type = #{engineType}
          and t.type = #{type}
          AND t.`status` = 1
		 <![CDATA[ and t.id < #{styleIdLimit} ]]>
        ORDER BY t.order
    </select>

    <select id="get" resultMap="BaseResultMap">
        SELECT *
        FROM t_engine_style t
        WHERE t.id = #{id} LIMIT 1
    </select>

    <select id="getEngineStyleByCodeAndEngineTypeAndWfwfid" resultType="com.github.model.EngineStyle">
        SELECT *
        FROM t_engine_style t
        WHERE t.`code` = #{code}
          AND t.engine_type = #{engineType}
          AND t.wfwfid = #{wfwfid}
          AND t.type = 1
          AND t.`status` = 1 LIMIT 1
    </select>

    <select id="getEngineStyleByCodeAndEngineTypeWithoutStatus" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        t_engine_style t
        WHERE
        t.`code` = #{code}
        AND t.engine_type = #{engineType}
        AND t.wfwfid = 0
        <if test="type != null">
            AND t.type = #{type}
        </if>
    </select>

    <select id="getEngineStyleByCodeAndEngineTypeAndVersion" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        t_engine_style t
        WHERE
        t.`code` = #{code}
        AND t.engine_type = #{engineType}
        AND t.wfwfid = 0
        AND t.version = #{version}
        <if test="type != null">
            AND t.type = #{type}
        </if>
        <if test="status != null">
            AND t.status = #{status}
        </if>
        LIMIT 1
    </select>

    <select id="getEngineStyleByCodeAndEngineTypeAndVersionWithoutStatus" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        t_engine_style t
        WHERE
        t.`code` = #{code}
        AND t.engine_type = #{engineType}
        AND t.wfwfid = 0
        AND t.version = #{version}
        <if test="type != null">
            AND t.type = #{type}
        </if>
        LIMIT 1
    </select>

    <select id="getEngineStyleByCodeAndEngineTypeWithoutStatusBy2Type" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        t_engine_style t
        WHERE
        t.`code` = #{code}
        AND t.engine_type = #{engineType}
        AND t.wfwfid = 0
        <if test="type1 != null and type2 != null">
            AND (t.type = #{type1} or t.type = #{type2})
        </if>
    </select>
    <select id="getEngineStyleByCodeAndEngineTypeAndVersionWithoutStatusBy2Type" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        t_engine_style t
        WHERE
        t.`code` = #{code}
        AND t.engine_type = #{engineType}
        AND t.wfwfid = 0
        AND t.version = #{version}
        <if test="type1 != null and type2 != null">
            AND (t.type = #{type1} or t.type = #{type2})
        </if>
        LIMIT 1
    </select>
    <select id="getEngineStyleByCodeAndEngineTypeAndVersionBy2Type" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        t_engine_style t
        WHERE
        t.`code` = #{code}
        AND t.engine_type = #{engineType}
        AND t.wfwfid = 0
        AND t.version = #{version}
        <if test="type1 != null and type2 != null">
            AND (t.type = #{type1} or t.type = #{type2})
        </if>
        <if test="status != null">
            AND t.status = #{status}
        </if>
        LIMIT 1
    </select>

    <select id="getMaxNextId" resultType="java.lang.Integer">
        select max(id + 1)
        from t_engine_style
    </select>

</mapper>
