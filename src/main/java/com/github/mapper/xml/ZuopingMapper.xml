<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.ZuopingMapper">

	<resultMap id="BaseResultMap" type="com.github.model.Zuoping">
        <result property="id" column="id"/>
        <result property="fenzu" column="fenzu"/>
        <result property="xuhao" column="xuhao"/>
        <result property="saiqu" column="saiqu"/>
        <result property="xingm" column="xingm"/>
        <result property="gzdw" column="gzdw"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="com.github.mapper.ZuopingMapper.columns">
	<![CDATA[
		id, fenzu, xuhao, saiqu, xingm, gzdw
	]]>
	</sql>

	<!-- useGeneratedKeys="true" keyProperty="xxx" for sqlserver and mysql -->
	<insert id="add" useGeneratedKeys="true" keyProperty="id">
    <![CDATA[
        INSERT INTO t_zuoping (
        	id, 
        	fenzu, 
        	xuhao, 
        	saiqu, 
        	xingm, 
        	gzdw
        ) VALUES (
        	#{id}, 
        	#{fenzu}, 
        	#{xuhao}, 
        	#{saiqu}, 
        	#{xingm}, 
        	#{gzdw}
        )
    ]]>
	</insert>
    
	<update id="update" >
    <![CDATA[
        UPDATE t_zuoping SET
	        fenzu = #{fenzu}, 
	        xuhao = #{xuhao}, 
	        saiqu = #{saiqu}, 
	        xingm = #{xingm}, 
	        gzdw = #{gzdw}
        WHERE 
	        id = #{id} 
    ]]>
	</update>

    <delete id="delete">
    <![CDATA[
        DELETE FROM t_zuoping WHERE
        id = #{id} 
    ]]>
    </delete>
    
    <select id="get" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.ZuopingMapper.columns" />
	    <![CDATA[
		    FROM t_zuoping 
	        WHERE 
		        id = #{id} 
	    ]]>
	</select>

	<select id="getList" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.ZuopingMapper.columns" /> FROM t_zuoping
	</select>

	<select id="getZuopingList" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.ZuopingMapper.columns" /> FROM t_zuoping
        <where>
            ...
        </where>
	</select>

    <select id="getFenzuList" resultType="com.github.model.Zuoping">
		select fenzu from t_zuoping GROUP BY fenzu
	</select>

	<select id="getSaiquList" resultType="com.github.model.Zuoping">
		select saiqu from t_zuoping GROUP BY saiqu
	</select>

</mapper>
