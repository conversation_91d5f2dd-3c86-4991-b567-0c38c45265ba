<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.EngineStyleHtmlMapper">

	<resultMap id="BaseResultMap" type="com.github.model.EngineStyleHtml">
        <result property="id" column="id"/>
		<result property="styleId" column="style_id"/>
		<result property="html" column="html"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="com.github.mapper.EngineStyleHtmlMapper.columns">
	<![CDATA[ id, style_id, html ]]>
	</sql>
	<insert id="add" useGeneratedKeys="true" keyProperty="id">
		<![CDATA[
            insert into t_engine_style_html(id, style_id, html)
            VALUES (
               #{id},
               #{styleId},
               #{html}
            )
		]]>
	</insert>

	<update id="update">
		 <![CDATA[
        UPDATE t_engine_style_html SET
	         style_id = #{styleId},
	          html = #{html}
        WHERE
	        id = #{id}
    ]]>
	</update>

	<select id="get" resultMap="BaseResultMap">
		select *
		from t_engine_style_html where id = #{id}
	</select>
	<select id="getByStyleId" resultMap="BaseResultMap">
		select *
		from t_engine_style_html where style_id = #{styleId}
	</select>

</mapper>
