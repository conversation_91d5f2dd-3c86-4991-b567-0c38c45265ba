<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.github.mapper.ZhuanjiaMapper">

	<resultMap id="BaseResultMap" type="com.github.model.Zhuanjia">
        <result property="id" column="id"/>
        <result property="xingm" column="xingm"/>
        <result property="phone" column="phone"/>
        <result property="gzdw" column="gzdw"/>
        <result property="dept" column="dept"/>
        <result property="dytj" column="dytj"/>
        <result property="detj" column="detj"/>
        <result property="saiqu" column="saiqu"/>
        <result property="sfzh" column="sfzh"/>
        <result property="zyfx" column="zyfx"/>
        <result property="zjly" column="zjly"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="com.github.mapper.ZhuanjiaMapper.columns">
	<![CDATA[
		id, xingm, phone, gzdw, dept, dytj, detj, saiqu, sfzh, zyfx, zjly
	]]>
	</sql>

	<!-- useGeneratedKeys="true" keyProperty="xxx" for sqlserver and mysql -->
	<insert id="add" useGeneratedKeys="true" keyProperty="id">
    <![CDATA[
        INSERT INTO t_zhuanjia (
        	id, 
        	xingm, 
        	phone, 
        	gzdw, 
        	dept, 
        	dytj, 
        	detj, 
        	saiqu, 
        	sfzh, 
        	zyfx, 
        	zjly
        ) VALUES (
        	#{id}, 
        	#{xingm}, 
        	#{phone}, 
        	#{gzdw}, 
        	#{dept}, 
        	#{dytj}, 
        	#{detj}, 
        	#{saiqu}, 
        	#{sfzh}, 
        	#{zyfx}, 
        	#{zjly}
        )
    ]]>
	</insert>
    
	<update id="update" >
    <![CDATA[
        UPDATE t_zhuanjia SET
	        xingm = #{xingm}, 
	        phone = #{phone}, 
	        gzdw = #{gzdw}, 
	        dept = #{dept}, 
	        dytj = #{dytj}, 
	        detj = #{detj}, 
	        saiqu = #{saiqu}, 
	        sfzh = #{sfzh}, 
	        zyfx = #{zyfx}, 
	        zjly = #{zjly}
        WHERE 
	        id = #{id} 
    ]]>
	</update>

    <delete id="delete">
    <![CDATA[
        DELETE FROM t_zhuanjia WHERE
        id = #{id} 
    ]]>
    </delete>
    
    <select id="get" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.ZhuanjiaMapper.columns" />
	    <![CDATA[
		    FROM t_zhuanjia 
	        WHERE 
		        id = #{id} 
	    ]]>
	</select>

	<select id="getList" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.ZhuanjiaMapper.columns" /> FROM t_zhuanjia
	</select>

	<select id="getZhuanjiaListDytj" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.ZhuanjiaMapper.columns" /> FROM t_zhuanjia where dytj = #{dytj}
	</select>

	<select id="getZhuanjiaList" resultMap="BaseResultMap">
		SELECT <include refid="com.github.mapper.ZhuanjiaMapper.columns" /> FROM t_zhuanjia
        <where>
            ...
        </where>
	</select>
	<!--***************************************************************-->

</mapper>
