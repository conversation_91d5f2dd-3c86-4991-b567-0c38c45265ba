/*
 */
package com.github.mapper;

import java.util.List;
import com.github.model.Zuoping;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;


@Mapper
@Repository
public interface ZuopingMapper {

	void add(Zuoping zuoping);
	void delete(Integer id);
	void update(Zuoping zuoping);
	Zuoping get(Integer id);
	List<Zuoping> getList();
	List<Zuoping> getZuopingList(Zuoping zuoping);

	List<Zuoping> getFenzuList();

	List<Zuoping> getSaiquList();

	List<Zuoping> getListByFenzu(String fenzu);
	/* --------------------------------------------------- */
}
