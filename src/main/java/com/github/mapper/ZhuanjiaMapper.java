/*
 */
package com.github.mapper;

import java.util.List;
import com.github.model.Zhuanjia;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;


@Mapper
@Repository
public interface ZhuanjiaMapper {

	void add(Zhuan<PERSON><PERSON> zhuanjia);
	void delete(Integer id);
	void update(Zhuanjia zhuanjia);
	Zhuanjia get(Integer id);
	List<Zhuanjia> getList();
	List<Zhuanjia> getZhuanjiaList(Zhuanjia zhuanjia);

	List<Zhuanji<PERSON>> getZhuanjiaListDytj(String dytj);
}
