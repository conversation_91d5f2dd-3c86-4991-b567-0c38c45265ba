package com.github.ali;

/**
 * @className Main
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/24 15:42
 * @Version 1.0
 **/
// dashscope SDK的版本 >= 2.19.0
import java.util.*;

import com.github.util.TemplateConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import io.reactivex.Flowable;

import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import com.alibaba.dashscope.common.MultiModalMessage;
import com.alibaba.dashscope.exception.UploadFileException;
import com.alibaba.dashscope.exception.InputRequiredException;
import java.lang.System;

public class Main {
    private static final Logger logger = LoggerFactory.getLogger(Main.class);
    private static StringBuilder reasoningContent = new StringBuilder();
    private static StringBuilder finalContent = new StringBuilder();
    private static boolean isFirstPrint = true;
    public static StringBuilder allText = new StringBuilder();

    private static void handleGenerationResult(MultiModalConversationResult message) {
        String re = message.getOutput().getChoices().get(0).getMessage().getReasoningContent();
        String reasoning = Objects.isNull(re)?"":re; // 默认值

        List<Map<String, Object>> content = message.getOutput().getChoices().get(0).getMessage().getContent();
        if (!reasoning.isEmpty()) {
            reasoningContent.append(reasoning);
            if (isFirstPrint) {
                System.out.println("====================思考过程====================");
                isFirstPrint = false;
            }
            System.out.print(reasoning);
        }

        if (Objects.nonNull(content) && !content.isEmpty()) {
            Object text = content.get(0).get("text");
            finalContent.append(content.get(0).get("text"));
            if (!isFirstPrint) {
                System.out.println("\n====================完整回复====================");
                isFirstPrint = true;
            }
            allText.append(text);
            System.out.print(text);
        }
    }
    public static MultiModalConversationParam buildMultiModalConversationParam(MultiModalMessage Msg)  {
        return MultiModalConversationParam.builder()
                // 若没有配置环境变量，请用百炼API Key将下行替换为：.apiKey("sk-xxx")
                .apiKey("sk-5a95a3fcf61e4ec49402a03161ec9494")
                // 此处以 qvq-max 为例，可按需更换模型名称
                .model("qvq-max")
                .messages(Arrays.asList(Msg))
                .incrementalOutput(true)
                .build();
    }

    public static void streamCallWithMessage(MultiModalConversation conv, MultiModalMessage Msg)
            throws NoApiKeyException, ApiException, InputRequiredException, UploadFileException {
        MultiModalConversationParam param = buildMultiModalConversationParam(Msg);
        Flowable<MultiModalConversationResult> result = conv.streamCall(param);
        result.blockingForEach(message -> {
            handleGenerationResult(message);
        });
        // 最终输出完整的内容
        System.out.println(allText.toString());
    }
    public static void main(String[] args) {
        try {
            String text = "使用html+css+js+thymeleaf3 编写一个网站首页html当中的一个局部模块的html代码，要求如下 \n" +
                    "模块为名师工作室模块，以网格布局，每条数据显示教师头像、名称和职称，简介 字段\n" +
                    "其中头像，名称，职称字段 放到当前内容块的上半部分且居中，简介字段放下半部分； \n" +
                    "数据条目至少3个教师信息 ，整个模块显示内容部分可以左右轮播切换显示更多教师信息，轮播左右样式为小箭头\n" +
                    "整个模块只显示一行数据，方便左右轮播切换数据显示，轮播小箭头在整个数据行高度居中位置\n" +
                    "- 该模块右上角提供\"更多\"链接跳转至列表页。\n" +
                    "** 生成样式代码编号为 indexNo=925\n" +
                    "** 需要输出所有的完整代码，你不能为了节省输出token而将示例中已有的代码忽略输出了，不能有其他方法保持原样或者使用原代码写法的省略\n"+
                    "** 更详细要求请参考上传的附件图片\n" +
                    "** 最外层div的class 命名 eng-graphic-style+indexNo,如果有tab切换，tab切换的class统一class为eng-tabs，\n" +
                    "** js对象的命名规范：graphicTemp+indexNo，\n" +
                    "** 如果有轮播效果，请使用swiper，swiper版本用的swiper2，\n" +
                    "** 如果有封面图，封面图片要加上图片裁剪，加上class js-crop-img 比如：\n" +
                    "<div class=\"img-box js-crop-img\"> <img src=\"g-banner.png\" alt=\"\"> </div> .img-box 的css要加上overflow: hidden\n" +
                    "** 标题（item-engine-title class等必须存要在不能）、更多、以及tab切换的地方，需要按照图片附件效果实现样式。\n" +
                    "** 示例模版代码中的initTab，generateSlides，detail，replaceUrlParam等js方法需要同等改写，不能有功能缺失，initSwiper等swiper相关方法根据是否有轮播功能决定是否重写"+
                    "** 最后需要加上\n" +
                    "function engineInitCallback(elem) {\n" +
                    "        graphicTempX.init(elem);\n" +
                    "        return graphicTempX;\n" +
                    "    }的回调方法 \n" +
                    "** 兼容性要求是至少兼容IE9及以上版本\n" +
                    "** 需要有移动端适配\n" +
                    "** 需要用到图片的地方可以是19-25数字编号+.jpg\n" +
                    "** 对象数据取值顺序如下：\n" +
                    "0 图片，1 标题，2 子标题，3 作者，4简介说明，5 数字内容，6发布时间 10移动端封面  100开始自定义字段\n" +
                    "如果参考样式附件图片，发现有不满足的字段，需要用到101开始的字段进行扩充。\n" +
                    "按照如下格式生成替换indexNo的sql，\n" +
                    "INSERT INTO `engine`.`t_engine_style`(`name`, `img_url`, `code`, `engine_type`, `type`, `wfwfid`, `order`, `status`) VALUES ('样式+indexNo', '/upload/engine/template/graphic/template_+indexNo+.jpg', indexNo, 'graphic', 0, 0, indexNo, 1);\n" +
                    "有字段不满足的情况下，需要扩充字段，需要生成添加字段sql。\n" +
                    "INSERT INTO `engine`.`t_extra_general_field`(`style`, `type`, `name`, `description`, `sequence`, `data_type`, `flag`) VALUES (indexNo, 'graphic', 'field0', '借用方式', 100, 'text', 100);\n" +
                    "把最终生成的sql 输出sql代码；\n" +
                    "** 生成html代码之后 输出html代码；\n" +
                    "已有实现的基础代码如附件，请在此风格上进行扩写和完善图片对应的样式和布局，同时根据案例html中js的方法定义和实现，扩写完成本html代码的对应功能,\n" +
                    "注意：其次general/fragment/common_style html引入不能取消\n";

            text = text + TemplateConstants.content;
            String text2 = "请描述这张图的内容，布局，颜色主题等";
            MultiModalConversation conv = new MultiModalConversation();
            MultiModalMessage userMsg = MultiModalMessage.builder()
                    .role(Role.USER.getValue())
                    .content(Arrays.asList(Collections.singletonMap("image", "https://mh.chaoxing.com/engine2/sts2/alists/5f60d926-05e6-427c-9ce2-834b764115dc.png"),
                            Collections.singletonMap("text", text2)))
                    .build();
            streamCallWithMessage(conv, userMsg);
//             打印最终结果
//            if (reasoningContent.length() > 0) {
//                System.out.println("\n====================完整回复====================");
//                System.out.println(finalContent.toString());
//            }
        } catch (ApiException | NoApiKeyException | UploadFileException | InputRequiredException e) {
            logger.error("An exception occurred: {}", e.getMessage());
        }
        System.exit(0);
    }
}
