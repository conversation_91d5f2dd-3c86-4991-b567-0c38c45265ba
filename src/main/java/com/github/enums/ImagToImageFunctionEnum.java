package com.github.enums;

import lombok.Getter;

/** 图生图编辑功能枚举
 * <AUTHOR>
 * @version ver 1.0
 * @className ImagToImageFunctionEnum
 * @description
 * @blame gao<PERSON>an
 * @date 2025-03-26 16:01:11
 */
@Getter
public enum ImagToImageFunctionEnum {

    STYLIZATION_ALL("全局风格化","stylization_all"),

    STYLIZATION_LOCAL("局部风格化","stylization_local"),

    DESCRIPTION_EDIT_WITH_MASK("局部重绘","description_edit_with_mask"),

    REMOVE_WATERMARK("去水印","remove_watermark"),

    EXPAND("扩图","expand"),

    SUPER_RESOLUTION("图像超分","super_resolution"),

    COLORIZATION("图像上色","colorization"),

    DOODLE ("线稿作画","doodle"),

    CONTROL_CARTOON_FEATURE ("垫图（仅支持卡通形象）","control_cartoon_feature");

    private String name;
    private String value;

     ImagToImageFunctionEnum(String name,String value) {
        this.name = name;
        this.value = value;
    }
}
