package com.github.enums;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * @version v1.0
 * @author: liban
 * @since: 15/12/2021 11:10 AM
 * @description 短信验证码发送
 */
public enum SmsEnum {

    /** 成功 **/
    SUCCESS("0","发送成功"),
    /** 错误 **/
    ERROR_2("-2","其他错误"),
    ERROR_3("-3","帐号或则密码错误"),
    ERROR_4("-4","一次提交信息不能超过600个手机号码"),
    ERROR_5("-5","企业号帐户余额不足，请先充值再提交短信息"),
    ERROR_6("-6","定时发送时间不是有效的时间格式"),
    ERROR_8("-8","发送内容需在3到250个字之间"),
    ERROR_9("-9","发送号码为空"),
    ERROR_11("-11","手机信息不能为空"),
    ERROR_12("-12","手机号格式错误，不是11位数字"),
    ERROR_13("-13","短信内容不能为空"),
    ERROR_14("-14","短信类型或所属产品或注册号不正确"),
    ERROR_15("-15","参数areaid,schoolid不正确"),
    ERROR_16("-16","IP不合法"),
    ERROR_17("-17","没有此注册号的权限，需要申请"),
    ERROR_18("-18","reg参数与拥有的注册号不一致"),
    ERROR_19("-19","同一手机号相同内容一天发送超过3次"),
    ERROR_20("-20","同一手机号一天发送短信超过10条"),
    ERROR_21("-21","同一手机号发送短信间隔在一分钟内"),
    ERROR_99("-99","网络或其它未知异常"),
    ERROR_404("-404","签名未注册"),
    ERROR_1021("-1021","不符合模版");

    /** 响应码 **/
    private String code;
    /** 响应内容 **/
    private String message;


    /**
     * 根据code获取信息
     *
     * @param code
     */
    public static String getMessage(String code) {
        Optional<SmsEnum> smsEnum = Arrays.stream(SmsEnum.values()).filter(t -> Objects.equals(code, t.getCode())).findFirst();
        return smsEnum.map(SmsEnum::getMessage).orElse("网络或其它未知异常");
    }


    SmsEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    void setMessage(String message) {
        this.message = message;
    }
}
