package com.github.enums;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 门户错误码定义
 */
public enum ErrorCode {

    CODE_DEFAULT("默认错误","00001",""),

    CODE_ENGINE_NOT_EXIST("应用不存在错误","30001","该应用不存在，请检查请求的参数或者应用ID"),
    CODE_UNKNOWN_LOCATION("未知的位置错误","40001","当前获取的位置信息错误，未知"),
    CODE_DATACENTER_NOT_EXIST("数据中心不存在该记录","40002","当前获取的位置信息错误，未知"),
    CODE_NO_PER_ERROR("暂无权限","40003","暂无权限，COOKIE校验不通过"),
    CODE_UNKNOWN_TEMPLATE_PATH("引擎类型错误","40004","模板路径不存在"),
    CODE_UNKNOWN_DATACENTER_TYPE("数据中心错误","40005","未知的数据中心产品类型"),
    CODE_UNKNOWN_YZ_CONFIG("云舟配置不存在","40006","修改的云舟配置不存在"),
    CODE_UNKNOWN_ORIGIN_ENGINE("同源应用不存在","40007","应用引用的同源应用不存在"),
    CODE_NO_UID_ERROR("暂无权限","40008","暂无权限，COOKIE校验不通过或UID为空"),
    CODE_NO_WEB_PER_ERROR("暂无权限","40009","暂无权限，非网站管理员"),

    CODE_WEBSITE_NULL_ERROR("网站不存在","40010","网站不存在！"),
    CODE_REQUEST_VALID("不允许请求错误","50001","不允许当前url地址访问"),
    CODE_DECODE_ERROR("请求解码错误","50002","当前请求内容解码错误，如decode"),
    CODE_NULL_POINTER("空数据异常","50003","当前请求空指针异常"),
    CODE_PARAMS_ERROR("参数错误","50004","请求url时传递的参数错误"),
    CODE_CONTENT_TRANS_ERROR("内容格式转换错误","50005","数据内容中出现格式转换错误，比如数字转换，日期转换等"),
    CODE_TYPE_ERROR("空数据异常","50006","分类数据不存在"),
    CODE_DATA_ERROR("空数据异常","50007","数据不存在"),
    CODE_DB_ERROR("数据库异常","50008","操作数据失败"),
    CODE_OUTER_DATA_ERROR("数据源配置错误","50009","外部数据源请求异常"),
    CODE_OUTER_DATA_RETURN_ERROR("数据源配置错误","50010","外部数据源返回格式错误"),
    CODE_OUTER_DATA_TYPE_ERROR("数据源配置错误","50011","分类接口请求异常"),
    CODE_OUTER_DATA_TYPE_RETURN_ERROR("数据源配置错误","50012","分类接口返回格式错误"),
    CODE_OUTER_DATA_SEARCH_ERROR("数据源配置错误","50013","搜索接口请求异常"),
    CODE_OUTER_DATA_SEARCH_RETURN_ERROR("数据源配置错误","50014","搜索接口返回格式错误"),
    CODE_OUTER_DATA_SEARCH_TYPE_ERROR("数据源配置错误","50015","搜索类型接口请求异常"),
    CODE_OUTER_DATA_SEARCH_TYPE_RETURN_ERROR("数据源配置错误","50016","搜索类型接口返回格式错误"),
    CODE_OUTER_DATA_DETAIL_ERROR("数据源配置错误","50017","未配置详情页地址"),
    CODE_OUTER_DATA_DETAIL_RETURN_ERROR("数据源配置错误","50018","未配置详情接口返回格式错误"),

    CODE_INNER_URI_ERROR("内部接口调用异常","50019","内部接口调用异常，请联系管理员"),

    CODE_PARAMS_DECODE_ERROR("参数解密错误","50020","参数不正确"),
    CODE_PARAMS_ENCODE_ERROR("参数加密错误","50021","参数不正确"),


    CODE_SENSITIVE_ERROR("包含敏感信息错误","50022","含敏感信息"),

    CODE_REPEAT_ERROR("数据操作错误","50023","名称重复"),

    CODE_SHORTCUT_COVER_ERROR("无法获取视频封面","50024","无法获取视频封面"),
    CODE_SHORT_VIDEO_TOO_LITTLE_ERROR("视频截图异常","50025","视频帧数不能小于1"),

    CODE_URI_REST_ERROR("数据请求异常","50026","接口数据请求异常"),
    CODE_URI_REST_FORM_ERROR("数据请求异常","50027","表单接口数据请求异常"),
    CODE_URI_REST_SEARCH_FAST_LINK_ERROR("搜索快捷入口异常","50028","外部快捷入口数据源接口不正确"),

    CODE_NO_SITE_HIERARCHY_ERROR("单位区域架构错误","50029","单位区域架构未激活"),
    CODE_COUNT_USER_ONLINE_ERROR("网站统计失败","50030","网站在线人数统计设置保存失败"),
    CODE_CHECK_IP_ERROR("IP验证失败","50031","IP验证失败"),
    CODE_ADD_TYPE_HAS_SUB_ERROR("操作分类异常","50032","存在子分类的分类不能添加数据"),
    CODE_TYPE_SUB_MORE_5_ERROR("操作分类异常","50033","目前支持最大分类层级为5级"),
    CODE_ENGINE_TYPE_ERROR("引擎类型错误","50034","引擎类型不存在"),

    CODE_AUDITING_EDIT_ERROR("审核中的异常","50035","审核中的数据不能修改"),
    CODE_AUDIT_UN_PASS_PUB_ERROR("审核数据的异常","50036","审核不通过的数据不能发布"),
    CODE_UN_SUBMIT_AUDIT_PUB_ERROR("审核数据的异常","50037","待提交审核的数据不能发布"),
    CODE_UN_SUBMIT_AUDIT_CANT_AUDIT_ERROR("审核数据的异常","50038","不能审核待提交审核的数据"),
    CODE_AUDITED_ERROR("审核数据的异常","50039","该条数据已审核通过"),
    CODE_AUDIT_HAS_SUBMIT_ERROR("审核数据的异常","50040","该条数据已被提交审核"),
    CODE_AUDITED_CANCEL_ERROR("审核数据的异常","50041","该条数据已被审核,撤回失败"),

    CODE_NOT_MY_WFWFID_ERROR("用户单位异常","50042","非本单位的应用"),
    CODE_IMPORT_ERROR("导入异常","50043","模版不正确，请下载最新的模版"),
    CODE_URI_REST_YZ_ERROR("云舟接口异常","50044","云舟接口获取数据失败"),
    CODE_REPEAT_SUBMIT_ERROR("重复提交","50045","重复提交"),
    CODE_PD_TYPE_ERROR("产品类型非法","50046","cookie异常或当前产品类型不正确"),
    CODE_USE_ERROR("操作异常","50047","不能删除网站首页"),
    CODE_USE2_ERROR("操作异常","50048","该网站已设置为系统模板！"),
    CODE_SYSTEM_FILE_ERROR("系统文件读取异常","50049","plugs.json文件读取异常！"),
    CODE_IP_ERROR("ip异常","50050","非法IP"),
    CODE_NODE_AUDITED_ERROR("ip异常","50051","当前节点已被其他管理员审核，请刷新页面获取最新审核数据。"),
    CODE_WEBSITE_TYPE_ERROR("网站异常","50052","网站类型错误！"),
    CODE_NO_PAGE_ERROR("网站异常","50053","页面不存在"),
    CODE_PAGE_AUDITING_EDIT_ERROR("审核页面中的异常","50054","审核中的页面不能修改"),
    CODE_PAGE_AUDIT_UN_PASS_PUB_ERROR("审核页面异常","50055","审核不通过的页面不能发布"),
    CODE_PAGE_UN_SUBMIT_AUDIT_PUB_ERROR("审核页面异常","50056","待提交审核的页面不能发布"),
    CODE_PAGE_UN_SUBMIT_AUDIT_CANT_AUDIT_ERROR("审核页面异常","50057","不能审核待提交审核的页面"),
    CODE_PAGE_AUDITED_ERROR("审核页面异常","50058","该页面已审核通过"),
    CODE_PAGE_AUDIT_HAS_SUBMIT_ERROR("审核页面异常","50059","该页面已被提交审核"),
    CODE_PAGE_AUDITED_CANCEL_ERROR("审核页面异常","50060","该页面已被审核,撤回失败"),
    CODE_NO_PAGE_AUDITED_ERROR("审核页面异常","50061","审核的页面不存在"),

    CODE_PRODUCT_COPY_OPEN_TYPE_ERROR("文化馆开通异常","50062","文化馆开通类型错误"),

    CODE_COOKIE_GET_ERROR("cookie获取异常","50063","cookie获取错误"),

    CODE_WEB_JSON_PARSE_ERROR("webJson解析错误","50064","webJson解析错误"),

    CODE_REFERER_ERROR("refer错误","50066","请求来源错误"),
    CODE_MODULE_CONTENT_ERROR("组件类型错误","50067","组件类型错误"),

    CODE_COOKIE_DOMAIN_ERROR("cookie获取异常","50068","网站域名和cookie不一致"),
    CODE_JSON_PARSE_ERROR("JSON解析错误","50069","JSON解析错误"),
    CODE_JSON_PC_VERISON_ERROR("JSON解析错误","50070","您当前PC端页面的数据不是最新版本，请先刷新页面，再编辑您的数据!"),
    CODE_JSON_APP_VERISON_ERROR("JSON解析错误","50071","您当前移动端页面的数据不是最新版本，请先刷新页面，再编辑您的数据!"),

    CODE_PARAMS_TIMEOUT_ERROR("链接超时","50072","链接超时，请重新进入"),

    CODE_WEBSITE_MAX("单位网站数量超过最大值","50073","单位网站数量超过最大值"),

    CODE_JSON_NULL_ERROE("JSON解析错误","50074","JSON不能为空"),
    CODE_TOP_MODULE_SETTING_NULL_ERROR("系统默认组件错误","50075","系统默认组件未配置"),
    CODE_TOP_MODULE_SETTING_ID_ERROR("系统默认组件错误","50076","系统默认组件ID配置错误"),
    CODE_URL_DECODER_ERROR("URL错误","50077","URL解码错误"),
    CODE_PAGE_THEME_CHOOSE_ERROR("主题包错误","50078","选择主题包PAGE_THEME_ID错误,PAGE_THEME_ID不能为0"),
    CODE_WEB_CLONE_BF_ERROR("网站克隆错误","50079","非法创建站点"),
    CODE_PAGE_WEBSITE_PARAM_REL_ERROR("参数关系错误","50080","pageId和websiteId关系错误"),
    CODE_MODULE_DEL_ERROR("删除失败","50081","组件存在默认组件类型依赖关系，先修改组件类型的默认组件ID后再删除"),
    CODE_COOKIE_WEBSITE_ID_ERROR("cookie错误","50082","cookie中的websiteId和当前网站不一致"),
    CODE_SUB_PAGE_NO_WEBSITE_ERROR("子页面查询错误","50083","2.0列表页或详情页pageId所属网站不存在"),
    CODE_WEBJSON_CONTENT_MAX_ERROR("webjson超长","50084","PC端webjson内容不能超过3MB"),
    CODE_WEBJSON_APP_CONTENT_MAX_ERROR("webjson超长","50085","APP端webjson内容不能超过3MB"),
    CODE_MODULE_DEL_ROLE_ERROR("组件权限错误","50086","无组件删除权限"),
    CODE_MODULE_ADD_ROLE_ERROR("组件权限错误","50087","无组件新增权限"),
    CODE_PAGE_CLONE_APP_MAX_ERROR("网站克隆失败","50088","网站存在单个页面应用数超过50个"),
    CODE_V2_SUB_PAGE_CLONE_APP_MAX_ERROR("网站克隆失败","50089","2.0网站存在单个列表或详情页面应用数超过10个"),
    CODE_BS_PAGE_CLONE_APP_MAX_ERROR("网站克隆失败","50090","大屏网站存在单个页面应用数超过100个"),
    CODE_REPAIR_APP_ERROR("脏数据修复失败","50091","脏数据修复失败"),
    CODE_JSON_VERISON_NULL_ERROR("JSON解析错误","50092","您当前页面的数据缺少版本号，联系前端同学排查!"),
    CODE_MODULE_UPDATE_ROLE_ERROR("组件权限错误","50093","无组件修改权限"),
    CODE_MODULE_NOT_EXIST_ERROR("组件不存在错误","50094","组件不存在错误"),
    ;

    private String name;
    private String code;
    private String value;

    public static String prekey = "mh_portal_";


    ErrorCode(String name, String code, String value) {
        this.name = name;
        this.code = code;
        this.value = value;
    }

    public static ErrorCode getByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        ErrorCode[] errorCodes = ErrorCode.values();
        AtomicReference<ErrorCode> pcode = new AtomicReference<>();
        Arrays.stream(errorCodes).forEach(errorCode -> {
            if(code.equals(errorCode.getCode())){
                pcode.set(errorCode);
            }
        });
        return pcode.get();
    }

    public String getName() {
        return name;
    }

    void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    void setValue(String value) {
        this.value = value;
    }
}
