#!/bin/sh

TARGET_JAR=$(ls *.jar);
JAR=${TARGET_JAR[0]}
echo "start spring boot application: $JAR"
echo "command line run: java -jar $JAR --spring.profiles.active=prod"

cat /dev/null > console.log

nohup java -Xmx8192M -Xms8192M -Xmn5120M -XX:+UseG1GC -XX:+PrintCommandLineFlags -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:gclogs/gc.log -XX:MaxGCPauseMillis=100 -XX:+ParallelRefProcEnabled -XX:MaxMetaspaceSize=512M -XX:MetaspaceSize=512M -XX:ErrorFile=./hs_err_pid.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./engine2.hprof -Djava.io.tmpdir=./tmps2 -jar $JAR --spring.profiles.active=prod > console.log &
# nohup java -jar $JAR --spring.profiles.active=prod > console.log &
# nohup java -Xms32m -Xmx32m -jar $JAR --spring.profiles.active=prod > console.log &

sleep 3
cat console.log
