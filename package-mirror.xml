<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">

    <id>package-mirror</id>

    <formats>
        <format>dir</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <fileSets>
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>${file.separator}</outputDirectory>
            <includes>
                <include>plugs.json</include>
                <include>*.xml</include>
                <include>*.properties</include>
                <include>**/*.xls</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>src/main/resources/static</directory>
            <outputDirectory>${file.separator}/static</outputDirectory>
            <includes>
                <include>**</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>${file.separator}</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${basedir}/lib/</directory>
            <outputDirectory>lib/</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
    <dependencySets>
        <dependencySet>
            <outputDirectory>lib/</outputDirectory>
            <useProjectArtifact>false</useProjectArtifact>
            <excludes>
                <exclude>mysql:mysql-connector-java:jar:8.0.16</exclude>
                <exclude>org.springframework.boot:spring-boot-starter-web:jar:2.1.6.RELEASE</exclude>
                <exclude>org.jolokia:jolokia-core:jar:1.6.1</exclude>
                <exclude>org.mybatis:mybatis:jar:3.5.2</exclude>
                <exclude>com.github.pagehelper:pagehelper-spring-boot-starter:jar:1.2.12</exclude>
                <exclude>io.netty:netty-handler:jar:4.1.36.Final</exclude>
                <exclude>org.springframework.boot:spring-boot:jar:2.1.6.RELEASE</exclude>
                <exclude>org.slf4j:slf4j-api:jar:1.7.26</exclude>
                <exclude>com.googlecode.json-simple:json-simple:jar:1.1.1</exclude>
                <exclude>org.apache.commons:commons-math3:jar:3.6.1</exclude>
                <exclude>ch.qos.logback:logback-classic:jar:1.2.3</exclude>
                <exclude>javax.annotation:javax.annotation-api:jar:1.3.2</exclude>
                <exclude>org.jboss.logging:jboss-logging:jar:3.3.2.Final</exclude>
                <exclude>org.springframework.boot:spring-boot-starter-aop:jar:2.1.6.RELEASE</exclude>
                <exclude>com.esotericsoftware:minlog:jar:1.3.0</exclude>
                <exclude>com.github.pagehelper:pagehelper:jar:5.1.10</exclude>
                <exclude>org.hibernate.validator:hibernate-validator:jar:6.0.17.Final</exclude>
                <exclude>io.netty:netty-common:jar:4.1.36.Final</exclude>
                <exclude>org.thymeleaf.extras:thymeleaf-extras-java8time:jar:3.0.4.RELEASE</exclude>
                <exclude>io.netty:netty-transport:jar:4.1.36.Final</exclude>
                <exclude>org.springframework:spring-oxm:jar:5.1.8.RELEASE</exclude>
                <exclude>org.slf4j:jul-to-slf4j:jar:1.7.26</exclude>
                <exclude>com.github.pagehelper:pagehelper-spring-boot-autoconfigure:jar:1.2.12</exclude>
                <exclude>org.springframework:spring-jcl:jar:5.1.8.RELEASE</exclude>
                <exclude>com.fasterxml:classmate:jar:1.4.0</exclude>
                <exclude>org.apache.commons:commons-compress:jar:1.18</exclude>
                <exclude>org.hdrhistogram:HdrHistogram:jar:2.1.9</exclude>
                <exclude>org.springframework.boot:spring-boot-starter-thymeleaf:jar:2.1.6.RELEASE</exclude>
                <exclude>commons-lang:commons-lang:jar:2.6</exclude>
                <exclude>net.coobird:thumbnailator:jar:0.4.8</exclude>
                <exclude>com.github.jsqlparser:jsqlparser:jar:2.0</exclude>
                <exclude>org.springframework.boot:spring-boot-starter-data-redis:jar:2.1.6.RELEASE</exclude>
                <exclude>ch.qos.logback:logback-core:jar:1.2.3</exclude>
                <exclude>org.springframework.boot:spring-boot-starter-actuator:jar:2.1.6.RELEASE</exclude>
                <exclude>org.springframework.data:spring-data-keyvalue:jar:2.1.9.RELEASE</exclude>
                <exclude>com.zaxxer:HikariCP:jar:3.2.0</exclude>
                <exclude>org.springframework:spring-core:jar:5.1.8.RELEASE</exclude>
                <exclude>io.netty:netty-buffer:jar:4.1.36.Final</exclude>
                <exclude>org.apache.httpcomponents:httpclient:jar:4.5.9</exclude>
                <exclude>org.apache.httpcomponents:httpcore:jar:4.4.11</exclude>
                <exclude>org.springframework:spring-aop:jar:5.1.8.RELEASE</exclude>
                <exclude>com.esotericsoftware:reflectasm:jar:1.11.3</exclude>
                <exclude>org.springframework:spring-beans:jar:5.1.8.RELEASE</exclude>
                <exclude>org.springframework.data:spring-data-redis:jar:2.1.9.RELEASE</exclude>
                <exclude>org.springframework.boot:spring-boot-starter-json:jar:2.1.6.RELEASE</exclude>
                <exclude>com.alibaba:druid-spring-boot-starter:jar:1.1.20</exclude>
                <exclude>org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:jar:2.1.0</exclude>
                <exclude>io.netty:netty-codec:jar:4.1.36.Final</exclude>
                <exclude>javax.validation:validation-api:jar:2.0.1.Final</exclude>
                <exclude>io.netty:netty-resolver:jar:4.1.36.Final</exclude>
                <exclude>org.springframework.data:spring-data-commons:jar:2.1.9.RELEASE</exclude>
                <exclude>com.fasterxml.jackson.core:jackson-core:jar:2.9.9</exclude>
                <exclude>commons-io:commons-io:jar:2.6</exclude>
                <exclude>io.lettuce:lettuce-core:jar:5.1.7.RELEASE</exclude>
                <exclude>de.codecentric:spring-boot-admin-starter-client:jar:2.1.6</exclude>
                <exclude>org.ow2.asm:asm:jar:5.0.4</exclude>
                <exclude>org.springframework:spring-expression:jar:5.1.8.RELEASE</exclude>
                <exclude>com.github.ben-manes.caffeine:caffeine:jar:2.6.2</exclude>
                <exclude>org.apache.logging.log4j:log4j-to-slf4j:jar:2.17.0</exclude>
                <exclude>com.fasterxml.jackson.core:jackson-annotations:jar:2.9.0</exclude>
                <exclude>org.springframework.boot:spring-boot-starter-**********************</exclude>
                <exclude>com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.9.9</exclude>
                <exclude>com.alibaba:druid:jar:1.1.20</exclude>
                <exclude>org.reactivestreams:reactive-streams:jar:1.0.2</exclude>
                <exclude>org.springframework.boot:spring-boot-actuator-autoconfigure:jar:2.1.6.RELEASE</exclude>
                <exclude>org.springframework:spring-context-support:jar:5.1.8.RELEASE</exclude>
                <exclude>org.springframework:spring-**********************</exclude>
                <exclude>commons-codec:commons-codec:jar:1.11</exclude>
                <exclude>org.apache.commons:commons-pool2:jar:2.6.0</exclude>
                <exclude>org.springframework.boot:spring-boot-starter-logging:jar:2.1.6.RELEASE</exclude>
                <exclude>org.springframework:spring-context:jar:5.1.8.RELEASE</exclude>
                <exclude>org.springframework.boot:spring-boot-autoconfigure:jar:2.1.6.RELEASE</exclude>
                <exclude>org.springframework.boot:spring-boot-starter-tomcat:jar:2.1.6.RELEASE</exclude>
                <exclude>de.codecentric:spring-boot-admin-client:jar:2.1.6</exclude>
                <exclude>com.fasterxml.jackson.core:jackson-databind:jar:2.9.9</exclude>
                <exclude>org.springframework:spring-web:jar:5.1.8.RELEASE</exclude>
                <exclude>org.aspectj:aspectjweaver:jar:1.9.4</exclude>
                <exclude>org.attoparser:attoparser:jar:2.0.5.RELEASE</exclude>
                <exclude>com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.9.9</exclude>
                <exclude>org.thymeleaf:thymeleaf-spring5:jar:3.0.11.RELEASE</exclude>
                <exclude>org.springframework.boot:spring-boot-actuator:jar:2.1.6.RELEASE</exclude>
                <exclude>org.mybatis.spring.boot:mybatis-spring-boot-starter:jar:2.1.0</exclude>
                <exclude>org.unbescape:unbescape:jar:1.1.6.RELEASE</exclude>
                <exclude>io.projectreactor:reactor-core:jar:3.2.10.RELEASE</exclude>
                <exclude>org.springframework.boot:spring-boot-starter:jar:2.1.6.RELEASE</exclude>
                <exclude>com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.9.9</exclude>
                <exclude>org.apache.logging.log4j:log4j-api:jar:2.17.0</exclude>
                <exclude>org.springframework:spring-tx:jar:5.1.8.RELEASE</exclude>
                <exclude>org.latencyutils:LatencyUtils:jar:2.0.3</exclude>
                <exclude>org.springframework:spring-webmvc:jar:5.1.8.RELEASE</exclude>
                <exclude>org.thymeleaf:thymeleaf:jar:3.0.11.RELEASE</exclude>
                <exclude>io.micrometer:micrometer-core:jar:1.1.5</exclude>
                <exclude>org.objenesis:objenesis:jar:2.2</exclude>
                <exclude>org.yaml:snakeyaml:jar:1.23</exclude>
                <exclude>com.esotericsoftware:kryo:jar:4.0.0</exclude>
                <exclude>org.springframework.boot:spring-boot-starter-cache:jar:2.1.6.RELEASE</exclude>
                <exclude>org.mybatis:mybatis-spring:jar:2.0.2</exclude>
            </excludes>
        </dependencySet>
    </dependencySets>
</assembly>

